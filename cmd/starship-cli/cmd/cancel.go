package cmd

import (
	"git.woa.com/kmetis/starship-engine/pkg/task/publish"

	"github.com/spf13/cobra"
)

func NewCancelCommand() *cobra.Command {
	cancelCmd := &cobra.Command{
		Use:   "cancel",
		Short: "Cancel a task",
		Long:  "Cancel a task, such as a publish task.",
	}

	cancelCmd.AddCommand(NewCancelPublishCommand())

	return cancelCmd
}

func NewCancelPublishCommand() *cobra.Command {
	cancelPublishCmd := &cobra.Command{
		Use:   "publish [taskName]",
		Short: "Cancel a publish task",
		Long:  "Cancel a publish task with the given name.",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			taskName := args[0]
			publish.CancelTask(taskName)
		},
	}
	return cancelPublishCmd
}
