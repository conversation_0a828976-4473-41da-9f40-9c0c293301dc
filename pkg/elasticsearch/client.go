package elasticsearch

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/config"

	"github.com/elastic/go-elasticsearch/v8"
	"k8s.io/klog/v2"
)

// Client ES客户端封装
type Client struct {
	es     *elasticsearch.Client
	config *config.ElasticsearchConfig
}

// NewClient 创建新的ES客户端
func NewClient(esConfig *config.ElasticsearchConfig) (*Client, error) {

	// 创建自定义Transport
	transport := &http.Transport{
		MaxIdleConnsPerHost:   10,
		ResponseHeaderTimeout: esConfig.Timeout,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
		DisableKeepAlives: false,
	}

	cfg := elasticsearch.Config{
		Addresses:  esConfig.Addresses,
		Username:   esConfig.Username,
		Password:   esConfig.Password,
		Transport:  transport,
		MaxRetries: esConfig.MaxRetries,
	}

	// 如果启用调试模式，添加日志
	if esConfig.Debug {
		klog.Infof("Creating ES client with addresses: %v", esConfig.Addresses)
	}

	es, err := elasticsearch.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create elasticsearch client: %w", err)
	}

	client := &Client{
		es:     es,
		config: esConfig,
	}

	// 不在创建时测试连接，延迟到实际使用时
	klog.Infof("ES client created for addresses: %v", esConfig.Addresses)

	return client, nil
}

// ping 测试ES连接
func (c *Client) ping(ctx context.Context) error {
	klog.Infof("Testing ES connection to: %v", c.config.Addresses)

	res, err := c.es.Info(c.es.Info.WithContext(ctx))
	if err != nil {
		klog.Errorf("ES connection failed: %v", err)
		return fmt.Errorf("elasticsearch connection failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		klog.Errorf("ES ping failed with status: %s", res.Status())
		return fmt.Errorf("elasticsearch ping failed: %s", res.Status())
	}

	klog.Info("ES connection successful")
	return nil
}

// ClusterData 集群数据结构，分别存储TKE和EKS
type ClusterData struct {
	TKEClusters []*ClusterInfo
	EKSClusters []*ClusterInfo
}

// SyncClustersData 并行同步集群数据
func (c *Client) SyncClustersData(ctx context.Context, date time.Time) (*ClusterData, error) {
	klog.Infof("Starting to sync clusters data for date: %s", date.Format("2006-01-02"))

	// 首先测试ES连接
	if err := c.ping(ctx); err != nil {
		return nil, fmt.Errorf("elasticsearch connection test failed: %w", err)
	}

	// 创建结果通道
	type syncResult struct {
		clusters []*ClusterInfo
		isEKS    bool
		err      error
	}

	resultCh := make(chan syncResult, 2)

	// 并行同步TKE集群数据
	go func() {
		tkeIndex := c.config.GetTKEIndexName(date)
		klog.Infof("Starting TKE sync from index: %s", tkeIndex)
		clusters, err := c.fetchClustersFromIndex(ctx, tkeIndex)
		if err != nil {
			klog.Errorf("Failed to fetch TKE clusters from index %s: %v", tkeIndex, err)
		} else {
			klog.Infof("Fetched %d TKE clusters from index %s", len(clusters), tkeIndex)
		}
		resultCh <- syncResult{clusters: clusters, isEKS: false, err: err}
	}()

	// 并行同步EKS集群数据
	go func() {
		eksIndex := c.config.GetEKSIndexName(date)
		klog.Infof("Starting EKS sync from index: %s", eksIndex)
		clusters, err := c.fetchClustersFromIndex(ctx, eksIndex)
		if err != nil {
			klog.Errorf("Failed to fetch EKS clusters from index %s: %v", eksIndex, err)
		} else {
			klog.Infof("Fetched %d EKS clusters from index %s", len(clusters), eksIndex)
		}
		resultCh <- syncResult{clusters: clusters, isEKS: true, err: err}
	}()

	// 收集结果
	var tkeClusters, eksClusters []*ClusterInfo
	var tkeErr, eksErr error

	for i := 0; i < 2; i++ {
		result := <-resultCh
		if result.isEKS {
			eksClusters = result.clusters
			eksErr = result.err
		} else {
			tkeClusters = result.clusters
			tkeErr = result.err
		}
	}

	// 检查错误
	if tkeErr != nil && eksErr != nil {
		return nil, fmt.Errorf("failed to fetch data from both TKE and EKS indices: TKE error: %v, EKS error: %v", tkeErr, eksErr)
	}

	totalClusters := len(tkeClusters) + len(eksClusters)
	klog.Infof("Successfully synced %d clusters in total (%d TKE, %d EKS)",
		totalClusters, len(tkeClusters), len(eksClusters))

	return &ClusterData{
		TKEClusters: tkeClusters,
		EKSClusters: eksClusters,
	}, nil
}

// fetchClustersFromIndex 从指定索引获取集群数据
func (c *Client) fetchClustersFromIndex(ctx context.Context, index string) ([]*ClusterInfo, error) {
	// 检查索引是否存在
	exists, err := c.indexExists(ctx, index)
	if err != nil {
		return nil, fmt.Errorf("failed to check index existence: %w", err)
	}
	if !exists {
		klog.Warningf("Index %s does not exist", index)
		return nil, nil
	}

	var clusters []*ClusterInfo

	// 使用scroll API分批获取数据
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match_all": map[string]interface{}{},
		},
		"size": c.config.BatchSize,
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal query: %w", err)
	}

	// 初始搜索
	res, err := c.es.Search(
		c.es.Search.WithContext(ctx),
		c.es.Search.WithIndex(index),
		c.es.Search.WithBody(bytes.NewReader(queryBytes)),
		c.es.Search.WithScroll(time.Minute*5),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to execute initial search: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("search request failed: %s", res.Status())
	}

	// 处理初始响应
	batch, scrollID, err := c.parseSearchResponse(res.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to parse initial search response: %w", err)
	}
	clusters = append(clusters, batch...)

	// 继续scroll获取剩余数据
	for scrollID != "" {
		scrollRes, err := c.es.Scroll(
			c.es.Scroll.WithContext(ctx),
			c.es.Scroll.WithScrollID(scrollID),
			c.es.Scroll.WithScroll(time.Minute*5),
		)
		if err != nil {
			klog.Errorf("Failed to execute scroll: %v", err)
			break
		}

		if scrollRes.IsError() {
			klog.Errorf("Scroll request failed: %s", scrollRes.Status())
			scrollRes.Body.Close()
			break
		}

		batch, newScrollID, err := c.parseSearchResponse(scrollRes.Body)
		scrollRes.Body.Close()
		if err != nil {
			klog.Errorf("Failed to parse scroll response: %v", err)
			break
		}

		if len(batch) == 0 {
			break
		}

		clusters = append(clusters, batch...)
		scrollID = newScrollID
	}

	// 清理scroll上下文
	if scrollID != "" {
		c.clearScroll(ctx, scrollID)
	}

	return clusters, nil
}

// indexExists 检查索引是否存在
func (c *Client) indexExists(ctx context.Context, index string) (bool, error) {
	res, err := c.es.Indices.Exists(
		[]string{index},
		c.es.Indices.Exists.WithContext(ctx),
	)
	if err != nil {
		return false, err
	}
	defer res.Body.Close()

	return res.StatusCode == 200, nil
}

// parseSearchResponse 解析搜索响应
func (c *Client) parseSearchResponse(body io.Reader) ([]*ClusterInfo, string, error) {
	var response ESSearchResponse
	if err := json.NewDecoder(body).Decode(&response); err != nil {
		return nil, "", fmt.Errorf("failed to decode response: %w", err)
	}

	clusters := make([]*ClusterInfo, 0, len(response.Hits.Hits))
	for _, hit := range response.Hits.Hits {
		clusters = append(clusters, &hit.Source)
	}

	return clusters, response.ScrollID, nil
}

// clearScroll 清理scroll上下文
func (c *Client) clearScroll(ctx context.Context, scrollID string) {
	res, err := c.es.ClearScroll(
		c.es.ClearScroll.WithContext(ctx),
		c.es.ClearScroll.WithScrollID(scrollID),
	)
	if err != nil {
		klog.Errorf("Failed to clear scroll: %v", err)
		return
	}
	defer res.Body.Close()

	if res.IsError() {
		klog.Errorf("Clear scroll request failed: %s", res.Status())
	}
}

// Close 关闭客户端
func (c *Client) Close() error {
	// ES客户端不需要显式关闭
	return nil
}
