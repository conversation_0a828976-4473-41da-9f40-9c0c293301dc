package task

import (
	"time"

	response "git.woa.com/kmetis/starship-engine/api/v1/common"
)

// 单条任务响应
type DetailResponse struct {
	TaskName     string    `json:"task_name"`
	Description  string    `json:"description"`
	AppAction    string    `json:"app_action"`
	Component    string    `json:"component"`
	ImageVersion string    `json:"image_version"`
	AddonVersion string    `json:"addon_version"`
	User         string    `json:"user"`
	Status       string    `json:"status"`
	CreateTime   time.Time `json:"create_time"`
	ChangeID     string    `json:"change_id"`
	TaskID       int64     `json:"task_id"`
	Progress     float32   `json:"progress"`
}

// 分页列表响应
type ListResponse struct {
	response.CommonResponse
	Data ListData `json:"data"`
}

type ListData struct {
	Items    []DetailResponse `json:"items"`
	Total    int64            `json:"total"`
	Page     int              `json:"page"`
	PageSize int              `json:"page_size"`
}

type CreateResponse struct {
	response.CommonResponse
	Data CreateData `json:"data"`
}

type CreateData struct {
	TaskName string `json:"task_name"`
}

type UpdateResponse struct {
	response.CommonResponse
	Data UpdateData `json:"data"`
}

type UpdateData struct {
	TaskName string `json:"task_name"`
	Status   string `json:"status"`
}

type BatchTerminateResponse struct {
	response.CommonResponse
	Data BatchTerminateData `json:"data"`
}

type BatchTerminateData struct {
	SuccessCount int                   `json:"success_count"`
	SkippedCount int                   `json:"skipped_count"`
	FailedCount  int                   `json:"failed_count"`
	Results      []TaskTerminateResult `json:"results"`
}

type TaskTerminateResult struct {
	TaskId   int64  `json:"task_id"`
	TaskName string `json:"task_name"`
	Status   string `json:"status"`
	Message  string `json:"message"`
}

type ClusterInfo struct {
	ClusterId      string    `json:"cluster_id"`
	MetaId         string    `json:"meta_id"`
	Type           string    `json:"type"`
	Region         string    `json:"region"`
	Status         string    `json:"status"`
	Stage          string    `json:"stage"`
	Reason         string    `json:"reason"`
	ClusterLink    string    `json:"cluster_link"`
	MonitorLink    string    `json:"monitor_link"`
	LogLink        string    `json:"log_link"`
	CreateTime     time.Time `json:"create_time"`
	UpdateTime     time.Time `json:"update_time"`
	Namespace      string    `json:"namespace"`
	AccountName    string    `json:"account_name"`     // 客户账户名称字段
	RollbackTaskId string    `json:"rollback_task_id"` // 回滚任务ID字段
}

type ListClustersResponse struct {
	Items    []*ClusterInfo `json:"items"`
	Total    int64          `json:"total"`
	PageNum  int            `json:"page_num"`
	PageSize int            `json:"page_size"`
}

type ExportClustersResponse struct {
	Content  string `json:"content,omitempty"`
	Filename string `json:"filename"`
	Format   string `json:"format"`
	Total    int64  `json:"total"`
}

type RollbackClusterResponse struct {
	TaskId    string `json:"task_id,omitempty"`
	ClusterId string `json:"cluster_id"`
	Status    string `json:"status"`
	Message   string `json:"message"`
}

// BatchRollbackResponse 批量回滚响应
type BatchRollbackResponse struct {
	TotalClusters   int                       `json:"total_clusters"`
	SuccessClusters int                       `json:"success_clusters"`
	FailedClusters  int                       `json:"failed_clusters"`
	Results         []RollbackClusterResponse `json:"results"`
	OverallStatus   string                    `json:"overall_status"`
	Message         string                    `json:"message"`
}
