package db

import (
	"fmt"
	"sync"

	"git.woa.com/kmetis/starship-engine/pkg/config"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"k8s.io/klog/v2"
)

var (
	DB       *gorm.DB
	onceDB   sync.Once
	dbSource = "%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=10s"
)

func InitDB(db config.Database) {
	onceDB.Do(func() {
		dbSource = fmt.Sprintf(dbSource, db.DbUser, db.DbPasswd, db.DbHost, db.DbPort, db.DbDatabase)
		var err error
		DB, err = gorm.Open(mysql.Open(dbSource), &gorm.Config{})
		if err != nil {
			klog.Fatalf("failed to connect database: %v", err)

		}
		// 检查 Cluster 表是否存在
		migrator := DB.Migrator()
		if !migrator.HasTable(&Cluster{}) {
			// 如果表不存在，自动创建表
			err = migrator.AutoMigrate(&Cluster{})
			if err != nil {
				klog.Fatalf("failed to migrate database: %v", err)
			}
		}

		if !migrator.HasTable(&Task{}) {
			err = migrator.AutoMigrate(&Task{})
			if err != nil {
				klog.Fatalf("failed to migrate database: %v", err)
			}
		}

		if !migrator.HasTable(&Batch{}) {
			err = migrator.AutoMigrate(&Batch{})
			if err != nil {
				klog.Fatalf("failed to migrate database: %v", err)
			}
		}

		if !migrator.HasTable(&AppBatchStrategy{}) {
			err = migrator.AutoMigrate(&AppBatchStrategy{})
			if err != nil {
				klog.Fatalf("failed to migrate database: %v", err)
			}
		}
	})
}

func InitSqliteDB() {
	//onceDB.Do(func() {
	//	var err error
	//	// 使用 SQLite 数据库，文件名为 starship.db
	//	DB, err = gorm.Open(sqlite.Open("starship.db"), &gorm.Config{})
	//	if err != nil {
	//		klog.Fatalf("failed to connect database: %v", err)
	//
	//	}
	//	// 检查 Cluster 表是否存在
	//	migrator := DB.Migrator()
	//	if !migrator.HasTable(&Cluster{}) {
	//		// 如果表不存在，自动创建表
	//		err = migrator.AutoMigrate(&Cluster{})
	//		if err != nil {
	//			klog.Fatalf("failed to migrate database: %v", err)
	//		}
	//	}
	//
	//	if !migrator.HasTable(&Task{}) {
	//		err = migrator.AutoMigrate(&Task{})
	//		if err != nil {
	//			klog.Fatalf("failed to migrate database: %v", err)
	//		}
	//	}
	//
	//	if !migrator.HasTable(&Batch{}) {
	//		err = migrator.AutoMigrate(&Batch{})
	//		if err != nil {
	//			klog.Fatalf("failed to migrate database: %v", err)
	//		}
	//	}
	//})
}
