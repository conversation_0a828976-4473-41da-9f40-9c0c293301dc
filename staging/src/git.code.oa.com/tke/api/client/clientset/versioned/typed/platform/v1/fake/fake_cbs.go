/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeCBSs implements CBSInterface
type FakeCBSs struct {
	Fake *FakePlatformV1
}

var cbssResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "cbss"}

var cbssKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "CBS"}

// Get takes name of the cBS, and returns the corresponding cBS object, and an error if there is any.
func (c *FakeCBSs) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.CBS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(cbssResource, name), &platformv1.CBS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CBS), err
}

// List takes label and field selectors, and returns the list of CBSs that match those selectors.
func (c *FakeCBSs) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.CBSList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(cbssResource, cbssKind, opts), &platformv1.CBSList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.CBSList{ListMeta: obj.(*platformv1.CBSList).ListMeta}
	for _, item := range obj.(*platformv1.CBSList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested cBSs.
func (c *FakeCBSs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(cbssResource, opts))
}

// Create takes the representation of a cBS and creates it.  Returns the server's representation of the cBS, and an error, if there is any.
func (c *FakeCBSs) Create(ctx context.Context, cBS *platformv1.CBS, opts v1.CreateOptions) (result *platformv1.CBS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(cbssResource, cBS), &platformv1.CBS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CBS), err
}

// Update takes the representation of a cBS and updates it. Returns the server's representation of the cBS, and an error, if there is any.
func (c *FakeCBSs) Update(ctx context.Context, cBS *platformv1.CBS, opts v1.UpdateOptions) (result *platformv1.CBS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(cbssResource, cBS), &platformv1.CBS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CBS), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeCBSs) UpdateStatus(ctx context.Context, cBS *platformv1.CBS, opts v1.UpdateOptions) (*platformv1.CBS, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(cbssResource, "status", cBS), &platformv1.CBS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CBS), err
}

// Delete takes name of the cBS and deletes it. Returns an error if one occurs.
func (c *FakeCBSs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(cbssResource, name), &platformv1.CBS{})
	return err
}

// Patch applies the patch and returns the patched cBS.
func (c *FakeCBSs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.CBS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(cbssResource, name, pt, data, subresources...), &platformv1.CBS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.CBS), err
}
