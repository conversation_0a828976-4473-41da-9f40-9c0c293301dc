/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeEniIpamds implements EniIpamdInterface
type FakeEniIpamds struct {
	Fake *FakePlatformV1
}

var eniipamdsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "eniipamds"}

var eniipamdsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "EniIpamd"}

// Get takes name of the eniIpamd, and returns the corresponding eniIpamd object, and an error if there is any.
func (c *FakeEniIpamds) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.EniIpamd, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(eniipamdsResource, name), &platformv1.EniIpamd{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.EniIpamd), err
}

// List takes label and field selectors, and returns the list of EniIpamds that match those selectors.
func (c *FakeEniIpamds) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.EniIpamdList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(eniipamdsResource, eniipamdsKind, opts), &platformv1.EniIpamdList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.EniIpamdList{ListMeta: obj.(*platformv1.EniIpamdList).ListMeta}
	for _, item := range obj.(*platformv1.EniIpamdList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested eniIpamds.
func (c *FakeEniIpamds) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(eniipamdsResource, opts))
}

// Create takes the representation of a eniIpamd and creates it.  Returns the server's representation of the eniIpamd, and an error, if there is any.
func (c *FakeEniIpamds) Create(ctx context.Context, eniIpamd *platformv1.EniIpamd, opts v1.CreateOptions) (result *platformv1.EniIpamd, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(eniipamdsResource, eniIpamd), &platformv1.EniIpamd{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.EniIpamd), err
}

// Update takes the representation of a eniIpamd and updates it. Returns the server's representation of the eniIpamd, and an error, if there is any.
func (c *FakeEniIpamds) Update(ctx context.Context, eniIpamd *platformv1.EniIpamd, opts v1.UpdateOptions) (result *platformv1.EniIpamd, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(eniipamdsResource, eniIpamd), &platformv1.EniIpamd{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.EniIpamd), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeEniIpamds) UpdateStatus(ctx context.Context, eniIpamd *platformv1.EniIpamd, opts v1.UpdateOptions) (*platformv1.EniIpamd, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(eniipamdsResource, "status", eniIpamd), &platformv1.EniIpamd{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.EniIpamd), err
}

// Delete takes name of the eniIpamd and deletes it. Returns an error if one occurs.
func (c *FakeEniIpamds) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(eniipamdsResource, name), &platformv1.EniIpamd{})
	return err
}

// Patch applies the patch and returns the patched eniIpamd.
func (c *FakeEniIpamds) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.EniIpamd, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(eniipamdsResource, name, pt, data, subresources...), &platformv1.EniIpamd{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.EniIpamd), err
}
