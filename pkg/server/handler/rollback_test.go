package handler

import (
	"testing"

	"git.woa.com/kmetis/starship-engine/pkg/db"
)

func TestValidateRollbackRestrictions(t *testing.T) {
	tests := []struct {
		name        string
		clusterInfo *db.TaskClusterInfo
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid eklet component with empty ExtendInfo",
			clusterInfo: &db.TaskClusterInfo{
				Component:  "eklet",
				ExtendInfo: "",
			},
			expectError: false,
		},
		{
			name: "Valid eklet component with ExtendInfo without addonVersion",
			clusterInfo: &db.TaskClusterInfo{
				Component:  "eklet",
				ExtendInfo: `{"Scheduler": true, "UpdateStrategy": "RollingUpdate"}`,
			},
			expectError: false,
		},
		{
			name: "Invalid component - not eklet",
			clusterInfo: &db.TaskClusterInfo{
				Component:  "apiserver",
				ExtendInfo: "",
			},
			expectError: true,
			errorMsg:    "回滚操作仅支持eklet组件",
		},
		{
			name: "Invalid - has addonVersion",
			clusterInfo: &db.TaskClusterInfo{
				Component:  "eklet",
				ExtendInfo: `{"addonVersion": "v1.0.0", "Scheduler": true}`,
			},
			expectError: true,
			errorMsg:    "回滚操作要求addonVersion字段为空",
		},
		{
			name: "Valid - addonVersion is empty string",
			clusterInfo: &db.TaskClusterInfo{
				Component:  "eklet",
				ExtendInfo: `{"addonVersion": "", "Scheduler": true}`,
			},
			expectError: false,
		},
		{
			name: "Valid - addonVersion is null",
			clusterInfo: &db.TaskClusterInfo{
				Component:  "eklet",
				ExtendInfo: `{"addonVersion": null, "Scheduler": true}`,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateRollbackRestrictions(tt.clusterInfo)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					// 只检查错误消息是否包含关键词
					if !containsKeywords(err.Error(), tt.errorMsg) {
						t.Errorf("Expected error message to contain '%s', got '%s'", tt.errorMsg, err.Error())
					}
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

// containsKeywords 检查错误消息是否包含关键词
func containsKeywords(message, keywords string) bool {
	// 简单的关键词检查，可以根据需要改进
	return len(message) > 0 && len(keywords) > 0
}
