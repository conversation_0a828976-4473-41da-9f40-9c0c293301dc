package common

import "net/http"

type CommonResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

func NewSuccess(data interface{}) CommonResponse {
	return CommonResponse{
		Code:    http.StatusOK,
		Message: "success",
		Data:    data,
	}
}

func NewError(code int, msg string) CommonResponse {
	return CommonResponse{
		Code:    code,
		Message: msg,
	}
}
