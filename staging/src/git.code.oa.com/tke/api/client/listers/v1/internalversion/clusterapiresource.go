/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package internalversion

import (
	v1 "git.code.oa.com/tke/api/platform/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// ClusterAPIResourceLister helps list ClusterAPIResources.
// All objects returned here must be treated as read-only.
type ClusterAPIResourceLister interface {
	// List lists all ClusterAPIResources in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.ClusterAPIResource, err error)
	// Get retrieves the ClusterAPIResource from the index for a given name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1.ClusterAPIResource, error)
	ClusterAPIResourceListerExpansion
}

// clusterAPIResourceLister implements the ClusterAPIResourceLister interface.
type clusterAPIResourceLister struct {
	indexer cache.Indexer
}

// NewClusterAPIResourceLister returns a new ClusterAPIResourceLister.
func NewClusterAPIResourceLister(indexer cache.Indexer) ClusterAPIResourceLister {
	return &clusterAPIResourceLister{indexer: indexer}
}

// List lists all ClusterAPIResources in the indexer.
func (s *clusterAPIResourceLister) List(selector labels.Selector) (ret []*v1.ClusterAPIResource, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.ClusterAPIResource))
	})
	return ret, err
}

// Get retrieves the ClusterAPIResource from the index for a given name.
func (s *clusterAPIResourceLister) Get(name string) (*v1.ClusterAPIResource, error) {
	obj, exists, err := s.indexer.GetByKey(name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("clusterapiresource"), name)
	}
	return obj.(*v1.ClusterAPIResource), nil
}
