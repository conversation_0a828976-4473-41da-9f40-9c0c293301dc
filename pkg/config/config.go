package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

var c *Config

type Config struct {
	DB Database `yaml:"db"`
	// batch interval
	BatchInterval map[string]Interval `yaml:"batchInterval"`
	// elasticsearch config
	Elasticsearch ElasticsearchConfig `yaml:"elasticsearch"`
	// zhiyan change order config
	ZhiyanChangeOrder ZhiyanChangeOrderConfig `yaml:"zhiyanChangeOrder"`
	// robot notification config
	RobotNotification RobotNotificationConfig `yaml:"robotNotification"`
	// cluster region override config
	ClusterRegionOverride map[string]string `yaml:"clusterRegionOverride"`
	// cos config
	COS COSConfig `yaml:"cos"`
}

type Database struct {
	DbHost     string `yaml:"dbHost"`
	DbPort     string `yaml:"dbPort"`
	DbUser     string `yaml:"dbUser"`
	DbPasswd   string `yaml:"dbPasswd"`
	DbDatabase string `yaml:"dbDatabase"`
}

type Interval struct {
	BatchInterval    int64 `yaml:"batchInterval"`
	SubBatchInterval int64 `yaml:"subBatchInterval"`
}

type ElasticsearchConfig struct {
	Addresses   []string      `yaml:"addresses"`
	Username    string        `yaml:"username"`
	Password    string        `yaml:"password"`
	Timeout     time.Duration `yaml:"timeout"`
	MaxRetries  int           `yaml:"maxRetries"`
	Debug       bool          `yaml:"debug"`
	IndexPrefix string        `yaml:"indexPrefix"`
	BatchSize   int           `yaml:"batchSize"`
	SyncTimeout time.Duration `yaml:"syncTimeout"`
}

type ZhiyanChangeOrderConfig struct {
	ProjectName       string `yaml:"projectName"`
	Token             string `yaml:"token"`
	GetOrderUrl       string `yaml:"getOrderUrl"`
	ReportCalendarUrl string `yaml:"reportCalendarUrl"`
}

type RobotNotificationConfig struct {
	DefaultRobot    string            `yaml:"defaultRobot"`    // 默认机器人webhook URL
	ComponentRobots map[string]string `yaml:"componentRobots"` // 组件专用机器人配置
}

type COSConfig struct {
	BucketURL string `yaml:"bucketUrl"`
	SecretID  string `yaml:"secretId"`
	SecretKey string `yaml:"secretKey"`
}

func InitConfig(config string) error {
	data, err := os.ReadFile(config)
	if err != nil {
		fmt.Println(fmt.Sprintf("Failed to read %s", config))
		return err
	}
	var conf Config
	if err := yaml.Unmarshal(data, &conf); err != nil {
		fmt.Println("Failed to unmarshal file.")
		return err
	}

	c = &conf
	return c.Validate()
}

func (c *Config) Validate() error {
	if c.DB.DbHost == "" || c.DB.DbPort == "" || c.DB.DbUser == "" || c.DB.DbPasswd == "" || c.DB.DbDatabase == "" {
		return fmt.Errorf("invalid db config")
	}
	if c.BatchInterval == nil || len(c.BatchInterval) == 0 {
		return fmt.Errorf("invalid batch interval config")
	}
	if err := c.validateElasticsearchConfig(); err != nil {
		return fmt.Errorf("invalid elasticsearch config: %w", err)
	}
	if err := c.validateZhiyanChangeOrderConfig(); err != nil {
		return fmt.Errorf("invalid zhiyan change order config: %w", err)
	}
	if err := c.validateRobotNotificationConfig(); err != nil {
		return fmt.Errorf("invalid robot notification config: %w", err)
	}
	if err := c.validateClusterRegionOverrideConfig(); err != nil {
		return fmt.Errorf("invalid cluster region override config: %w", err)
	}
	if err := c.validateCOSConfig(); err != nil {
		return fmt.Errorf("invalid cos config: %w", err)
	}
	return nil
}

func (c *Config) validateElasticsearchConfig() error {
	if len(c.Elasticsearch.Addresses) == 0 {
		return fmt.Errorf("elasticsearch addresses cannot be empty")
	}
	if c.Elasticsearch.Timeout <= 0 {
		return fmt.Errorf("elasticsearch timeout must be positive")
	}
	if c.Elasticsearch.MaxRetries < 0 {
		return fmt.Errorf("elasticsearch max retries cannot be negative")
	}
	if c.Elasticsearch.BatchSize <= 0 {
		return fmt.Errorf("elasticsearch batch size must be positive")
	}
	if c.Elasticsearch.SyncTimeout <= 0 {
		return fmt.Errorf("elasticsearch sync timeout must be positive")
	}
	return nil
}

func (c *Config) validateZhiyanChangeOrderConfig() error {
	if c.ZhiyanChangeOrder.ProjectName == "" {
		return fmt.Errorf("zhiyan change order project name cannot be empty")
	}
	if c.ZhiyanChangeOrder.Token == "" {
		return fmt.Errorf("zhiyan change order token cannot be empty")
	}
	if c.ZhiyanChangeOrder.GetOrderUrl == "" {
		return fmt.Errorf("zhiyan change order get order url cannot be empty")
	}
	if c.ZhiyanChangeOrder.ReportCalendarUrl == "" {
		return fmt.Errorf("zhiyan change order report calendar url cannot be empty")
	}
	return nil
}

func (c *Config) validateRobotNotificationConfig() error {
	if c.RobotNotification.DefaultRobot == "" {
		return fmt.Errorf("robot notification default robot cannot be empty")
	}
	return nil
}

func (c *Config) validateClusterRegionOverrideConfig() error {
	// 集群region覆盖配置是可选的，不需要强制验证
	// 但可以验证配置的格式是否正确
	for clusterId, region := range c.ClusterRegionOverride {
		if clusterId == "" {
			return fmt.Errorf("cluster region override: cluster ID cannot be empty")
		}
		if region == "" {
			return fmt.Errorf("cluster region override: region cannot be empty for cluster %s", clusterId)
		}
	}
	return nil
}

func (c *Config) validateCOSConfig() error {
	if c.COS.BucketURL == "" {
		return fmt.Errorf("cos bucket URL cannot be empty")
	}
	if c.COS.SecretID == "" {
		return fmt.Errorf("cos secret ID cannot be empty")
	}
	if c.COS.SecretKey == "" {
		return fmt.Errorf("cos secret key cannot be empty")
	}
	return nil
}

func GetDatabaseConfig() Database {
	if c != nil {
		return c.DB
	}
	return GetDefaultDatabaseConfig()
}

func GetDefaultDatabaseConfig() Database {
	return Database{
		DbHost:     "lb-p3pcroiw-u10rbxwf278n3mit.clb.gz-tencentclb.cloud",
		DbPort:     "3306",
		DbUser:     "root",
		DbPasswd:   "]K^B4~urzQVv6n8",
		DbDatabase: "starship_test",
	}
}

func GetSyncClusterDatabaseConfig() Database {
	return Database{}
}

func GetDefaultConfigPath() string {
	if envPath := os.Getenv("STARSHIP_CONFIG"); envPath != "" {
		return envPath
	}

	home, err := os.UserHomeDir()
	if err != nil {
		return filepath.Join("/root", ".starship", "config.yaml")
	}

	return filepath.Join(home, ".starship", "config.yaml")
}

func GetDefaultDbConfig() Database {
	err := InitConfig("/etc/config/config.yaml")
	if err != nil {
		panic(err)
	}
	return c.DB
}

func GetBatchIntervalConfig() (map[string]Interval, error) {
	//return c.BatchInterval, nil
	cfg := map[string]Interval{
		"default": {
			BatchInterval:    3600,
			SubBatchInterval: 300,
		},
	}
	return cfg, nil
}

func GetEksPodClickHouseDBConfig() Database {
	return Database{}
}

func GetElasticsearchConfig() ElasticsearchConfig {
	if c != nil {
		return c.Elasticsearch
	}
	// 返回默认配置，支持环境变量覆盖
	return GetDefaultElasticsearchConfig()
}

func GetDefaultElasticsearchConfig() ElasticsearchConfig {
	config := ElasticsearchConfig{
		Addresses:   []string{"https://lb-8c0obspy-9t8hen40zgwxklpv.clb.gz-tencentclb.cloud:9200"},
		Username:    "elastic",
		Password:    "Q37F3WO6rEreT4Tr",
		Timeout:     30 * time.Second,
		MaxRetries:  3,
		Debug:       true, // 启用调试模式以便排查问题
		IndexPrefix: "starship",
		BatchSize:   1000,
		SyncTimeout: 5 * time.Minute,
	}

	return config
}

// GetTKEIndexName 获取TKE集群索引名称
func (c *ElasticsearchConfig) GetTKEIndexName(date time.Time) string {
	return fmt.Sprintf("%s-tke-cluster-%s", c.IndexPrefix, date.Format("2006-01-02"))
}

// GetEKSIndexName 获取EKS集群索引名称
func (c *ElasticsearchConfig) GetEKSIndexName(date time.Time) string {
	return fmt.Sprintf("%s-eks-cluster-%s", c.IndexPrefix, date.Format("2006-01-02"))
}

// GetZhiyanChangeOrderConfig 获取智研变更订单配置
func GetZhiyanChangeOrderConfig() ZhiyanChangeOrderConfig {
	if c != nil {
		return c.ZhiyanChangeOrder
	}
	// 返回默认配置，支持环境变量覆盖
	return GetDefaultZhiyanChangeOrderConfig()
}

// GetDefaultZhiyanChangeOrderConfig 获取默认的智研变更订单配置
func GetDefaultZhiyanChangeOrderConfig() ZhiyanChangeOrderConfig {
	config := ZhiyanChangeOrderConfig{
		ProjectName:       "tke-serverless",
		Token:             "55413589dcc70f6aa96603a40bc0f095",
		GetOrderUrl:       "http://test.openapi.zhiyan.woa.com/workflow/api/order/get",
		ReportCalendarUrl: "http://test.openapi.zhiyan.woa.com/change_copilot/calendar/api/v1/report_logs",
	}

	return config
}

// GetRobotNotificationConfig 获取机器人通知配置
func GetRobotNotificationConfig() RobotNotificationConfig {
	if c != nil {
		return c.RobotNotification
	}
	// 返回默认配置，支持环境变量覆盖
	return GetDefaultRobotNotificationConfig()
}

// GetDefaultRobotNotificationConfig 获取默认的机器人通知配置
func GetDefaultRobotNotificationConfig() RobotNotificationConfig {
	config := RobotNotificationConfig{
		DefaultRobot:    "http://in.qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6ddd4cc4-de41-4ed9-8803-f6d766525ac6",
		ComponentRobots: make(map[string]string),
	}

	return config
}

// GetRobotWebhookForComponent 获取指定组件的机器人webhook URL
func GetRobotWebhookForComponent(component string) string {
	robotConfig := GetRobotNotificationConfig()

	// 优先使用组件专用机器人
	if webhook, exists := robotConfig.ComponentRobots[component]; exists && webhook != "" {
		return webhook
	}

	// 回退到默认机器人
	return robotConfig.DefaultRobot
}

// GetClusterRegionOverrideConfig 获取集群region覆盖配置
func GetClusterRegionOverrideConfig() map[string]string {
	if c != nil && c.ClusterRegionOverride != nil {
		// 合并配置文件和环境变量
		result := make(map[string]string)

		// 先复制配置文件中的配置
		for k, v := range c.ClusterRegionOverride {
			result[k] = v
		}

		// 再添加环境变量配置（环境变量优先级更高）
		envOverrides := getEnvClusterRegionOverrides()
		for k, v := range envOverrides {
			result[k] = v
		}

		return result
	}
	// 返回默认配置，支持环境变量覆盖
	return getEnvClusterRegionOverrides()
}

// getEnvClusterRegionOverrides 从环境变量获取集群region覆盖配置
func getEnvClusterRegionOverrides() map[string]string {
	result := make(map[string]string)

	// 支持环境变量配置
	// 格式：CLUSTER_REGION_OVERRIDE_<CLUSTER_ID>=region
	// 例如：CLUSTER_REGION_OVERRIDE_cls_12345=gz
	for _, env := range os.Environ() {
		if strings.HasPrefix(env, "CLUSTER_REGION_OVERRIDE_") {
			parts := strings.SplitN(env, "=", 2)
			if len(parts) == 2 {
				clusterId := strings.TrimPrefix(parts[0], "CLUSTER_REGION_OVERRIDE_")
				// 将环境变量中的下划线转换回连字符
				clusterId = strings.ReplaceAll(clusterId, "_", "-")
				result[clusterId] = parts[1]
			}
		}
	}

	return result
}

// GetRegionForCluster 获取指定集群的region，优先使用覆盖配置
func GetRegionForCluster(clusterId string) (string, bool) {
	overrideConfig := GetClusterRegionOverrideConfig()

	// 优先使用覆盖配置
	if region, exists := overrideConfig[clusterId]; exists && region != "" {
		return region, true
	}

	// 没有覆盖配置
	return "", false
}

// GetCOSConfig 获取COS配置
func GetCOSConfig() COSConfig {
	if c != nil {
		return c.COS
	}
	// 返回默认配置，支持环境变量覆盖
	return GetDefaultCOSConfig()
}

// GetDefaultCOSConfig 获取默认的COS配置
func GetDefaultCOSConfig() COSConfig {
	config := COSConfig{
		BucketURL: "https://blakeke-1255429800.cos-internal.ap-guangzhou.tencentcos.cn",
		SecretID:  "AKIDuhIivuhJiu1vfBTYR6Xt1xfBUcXEhw50",
		SecretKey: "kGckfMp6B3h7LODoDPSJUwNejcIqDNK4",
	}

	return config
}
