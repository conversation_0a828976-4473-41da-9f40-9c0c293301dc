/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1beta1

import (
	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// ScalerTaskLister helps list ScalerTasks.
// All objects returned here must be treated as read-only.
type ScalerTaskLister interface {
	// List lists all ScalerTasks in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1beta1.ScalerTask, err error)
	// ScalerTasks returns an object that can list and get ScalerTasks.
	ScalerTasks(namespace string) ScalerTaskNamespaceLister
	ScalerTaskListerExpansion
}

// scalerTaskLister implements the ScalerTaskLister interface.
type scalerTaskLister struct {
	indexer cache.Indexer
}

// NewScalerTaskLister returns a new ScalerTaskLister.
func NewScalerTaskLister(indexer cache.Indexer) ScalerTaskLister {
	return &scalerTaskLister{indexer: indexer}
}

// List lists all ScalerTasks in the indexer.
func (s *scalerTaskLister) List(selector labels.Selector) (ret []*v1beta1.ScalerTask, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1beta1.ScalerTask))
	})
	return ret, err
}

// ScalerTasks returns an object that can list and get ScalerTasks.
func (s *scalerTaskLister) ScalerTasks(namespace string) ScalerTaskNamespaceLister {
	return scalerTaskNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// ScalerTaskNamespaceLister helps list and get ScalerTasks.
// All objects returned here must be treated as read-only.
type ScalerTaskNamespaceLister interface {
	// List lists all ScalerTasks in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1beta1.ScalerTask, err error)
	// Get retrieves the ScalerTask from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1beta1.ScalerTask, error)
	ScalerTaskNamespaceListerExpansion
}

// scalerTaskNamespaceLister implements the ScalerTaskNamespaceLister
// interface.
type scalerTaskNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all ScalerTasks in the indexer for a given namespace.
func (s scalerTaskNamespaceLister) List(selector labels.Selector) (ret []*v1beta1.ScalerTask, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1beta1.ScalerTask))
	})
	return ret, err
}

// Get retrieves the ScalerTask from the indexer for a given namespace and name.
func (s scalerTaskNamespaceLister) Get(name string) (*v1beta1.ScalerTask, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1beta1.Resource("scalertask"), name)
	}
	return obj.(*v1beta1.ScalerTask), nil
}
