package server

import (
	"git.woa.com/kmetis/starship-engine/pkg/server/handler"
	"github.com/gin-gonic/gin"
)

func registerRoutes(r *gin.Engine) {
	// 基础健康检查路由
	r.GET("/health", handler.Health)

	// API版本分组
	v1 := r.Group("/api/v1")
	{
		// task相关路由
		taskGroup := v1.Group("/task")
		{
			taskGroup.GET("/list", handler.ListTasks)
			taskGroup.POST("/create", handler.CreateTask)
			taskGroup.PUT("/updateStatus", handler.UpdateTaskStatus)
			taskGroup.PUT("/batchTerminate", handler.BatchTerminateTasks)
			taskGroup.GET("/batch", handler.GetTaskBatchByTaskName)
		}

		clusterGroup := v1.Group("/cluster")
		{
			clusterGroup.GET("/list", handler.ListClusters)
			clusterGroup.GET("/detail", handler.GetClusterDetail)
			clusterGroup.GET("/process", handler.GetTaskProcessBar)
			clusterGroup.POST("/export", handler.ExportClusters)
			clusterGroup.POST("/rollback", handler.RollbackCluster)
		}

		// 统计相关路由 - 简化接口地址
		statsGroup := v1.Group("/stats")
		{
			statsGroup.GET("/tasks", handler.TaskCreationStats)
			statsGroup.GET("/clusters", handler.ClusterDeploymentStats)
			statsGroup.GET("/components", handler.GetComponents)
			statsGroup.GET("/date-range", handler.GetDateRange)
			statsGroup.GET("/overview", handler.GetQuickStats)
		}

		// ES相关路由
		esGroup := v1.Group("/elasticsearch")
		{
			esGroup.GET("/stats", handler.GetESStats)
			esGroup.GET("/clusters", handler.GetESClusters)
			esGroup.GET("/clusters/:clusterId", handler.GetESCluster)
			esGroup.POST("/sync", handler.ForceESSync)
			esGroup.GET("/summary", handler.GetESClustersByType)
			esGroup.GET("/cache/info", handler.GetESCacheInfo)
			esGroup.GET("/cache/changes", handler.GetESCacheChanges)
			// 新增的查询和导出接口
			esGroup.POST("/query", handler.QueryESClusters)
			esGroup.POST("/export", handler.ExportESClusters)
		}

		batchStrategyGroup := v1.Group("/batch-strategy")
		{
			batchStrategyGroup.GET("", handler.GetBatchStrategy)
			batchStrategyGroup.POST("", handler.CreateBatchStrategy)
			batchStrategyGroup.PUT("", handler.UpdateBatchStrategy)
		}
	}
}
