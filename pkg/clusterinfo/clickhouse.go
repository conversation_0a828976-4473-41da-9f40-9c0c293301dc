package clusterinfo

import (
	"fmt"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/config"
	"github.com/ClickHouse/clickhouse-go/v2"
)

var clickhouseConn clickhouse.Conn

type EksPod struct {
	ClusterId string `ch:"clusterId"`
	PodCount  uint64 `ch:"podCount"`
}

func newClickhouseConn(cfg config.Database) (clickhouse.Conn, error) {
	if clickhouseConn != nil {
		return clickhouseConn, nil
	}
	var err error
	clickhouseConn, err = clickhouse.Open(&clickhouse.Options{
		Addr: []string{fmt.Sprintf("%s:%s", cfg.DbHost, cfg.DbPort)},
		Auth: clickhouse.Auth{
			Database: cfg.DbDatabase,
			Username: cfg.DbUser,
			Password: cfg.DbPasswd,
		},
		DialTimeout:     time.Second,
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: time.Hour,
	})
	return clickhouseConn, err
}

func StatEksPodNumber() (map[string]int64, error) {
	return nil, fmt.Errorf("method not implemented")
	//query := "SELECT clusterId, count(1) as podCount FROM eks_pods WHERE podType = 0  AND toDate(reqTime) <= yesterday()  AND toDate(finishTime) >= yesterday() GROUP BY clusterId"
	//conn, err := newClickhouseConn(config.GetEksPodClickHouseDBConfig())
	//if err != nil {
	//	return nil, err
	//}
	//defer conn.Close()
	//
	//rows, err := conn.Query(context.TODO(), query)
	//if err != nil {
	//	return nil, err
	//}
	//defer rows.Close()
	//podNums := make(map[string]int64)
	//for rows.Next() {
	//	var pod EksPod
	//	if err := rows.Scan(&pod.ClusterId, &pod.PodCount); err != nil {
	//		return nil, err
	//	}
	//	podNums[pod.ClusterId] = int64(pod.PodCount)
	//}
	//return podNums, nil
}
