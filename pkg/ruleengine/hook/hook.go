package hook

import "fmt"

var EngineHookMap = map[string]EngineHook{}

type EngineHook interface {
	Execute(args ...interface{}) error
}

func RegisterEngineHook(name string, hook EngineHook) {
	EngineHookMap[name] = hook
}

func GetEngineHook(name string) (EngineHook, error) {
	hook, ok := EngineHookMap[name]
	if !ok {
		return nil, fmt.Errorf("engine hook not found: %s", name)
	}
	return hook, nil
}
