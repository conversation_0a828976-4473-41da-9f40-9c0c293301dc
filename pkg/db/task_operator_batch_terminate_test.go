package db

import (
	"testing"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB() {
	var err error
	// 使用内存数据库进行测试
	DB, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database: " + err.Error())
	}

	// 自动迁移表结构
	err = DB.AutoMigrate(&Task{}, &Batch{}, &Cluster{})
	if err != nil {
		panic("failed to migrate database: " + err.Error())
	}
}

func TestBatchTerminateTasksByIds(t *testing.T) {
	// 初始化测试数据库
	setupTestDB()

	// 创建测试任务
	testTasks := []*Task{
		{
			TaskName:  "test-task-1",
			AppAction: "publish",
			Component: "test-component",
			User:      "test-user",
			ChangeId:  "test-change-1",
			Status:    consts.RUNNING,
		},
		{
			TaskName:  "test-task-2",
			AppAction: "publish",
			Component: "test-component",
			User:      "test-user",
			ChangeId:  "test-change-2",
			Status:    consts.COMPLETED,
		},
		{
			TaskName:  "test-task-3",
			AppAction: "publish",
			Component: "test-component",
			User:      "test-user",
			ChangeId:  "test-change-3",
			Status:    consts.PAUSE_STA,
		},
	}

	// 插入测试数据
	for _, task := range testTasks {
		err := CreateTask(task)
		if err != nil {
			t.Fatalf("Failed to create test task: %v", err)
		}
	}

	// 获取任务ID
	var taskIds []int64
	for _, task := range testTasks {
		taskInfo, err := GetTaskByTaskName(task.TaskName)
		if err != nil {
			t.Fatalf("Failed to get task by name: %v", err)
		}
		taskIds = append(taskIds, taskInfo.ID)
	}

	// 测试批量终止
	result, err := BatchTerminateTasksByIds(taskIds)
	if err != nil {
		t.Fatalf("BatchTerminateTasksByIds failed: %v", err)
	}

	// 验证结果
	if result.SuccessCount != 2 {
		t.Errorf("Expected 2 successful terminations, got %d", result.SuccessCount)
	}
	if result.SkippedCount != 1 {
		t.Errorf("Expected 1 skipped task, got %d", result.SkippedCount)
	}
	if result.FailedCount != 0 {
		t.Errorf("Expected 0 failed tasks, got %d", result.FailedCount)
	}

	// 验证数据库中的状态
	for i, taskId := range taskIds {
		task, err := GetTaskByTaskId(taskId)
		if err != nil {
			t.Fatalf("Failed to get task %d: %v", taskId, err)
		}

		switch i {
		case 0, 2: // 第1和第3个任务应该被终止
			if task.Status != consts.TERMINATED {
				t.Errorf("Task %d should be terminated, but status is %s", taskId, task.Status)
			}
		case 1: // 第2个任务已完成，应该保持不变
			if task.Status != consts.COMPLETED {
				t.Errorf("Task %d should remain completed, but status is %s", taskId, task.Status)
			}
		}
	}

	t.Logf("Batch terminate test completed successfully")
	t.Logf("Success: %d, Skipped: %d, Failed: %d", result.SuccessCount, result.SkippedCount, result.FailedCount)
}

func TestBatchTerminateTasksByIds_NotFound(t *testing.T) {
	setupTestDB()

	// 测试不存在的任务ID
	nonExistentIds := []int64{99999, 99998}
	result, err := BatchTerminateTasksByIds(nonExistentIds)
	if err != nil {
		t.Fatalf("BatchTerminateTasksByIds failed: %v", err)
	}

	if result.FailedCount != 2 {
		t.Errorf("Expected 2 failed tasks, got %d", result.FailedCount)
	}

	for _, r := range result.Results {
		if r.Status != "not_found" {
			t.Errorf("Expected status 'not_found', got '%s'", r.Status)
		}
	}
}
