/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	"context"
	"time"

	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/portrait.k8s.io/v1beta1"
	scheme "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// WorkloadCheckpointsGetter has a method to return a WorkloadCheckpointInterface.
// A group's client should implement this interface.
type WorkloadCheckpointsGetter interface {
	WorkloadCheckpoints(namespace string) WorkloadCheckpointInterface
}

// WorkloadCheckpointInterface has methods to work with WorkloadCheckpoint resources.
type WorkloadCheckpointInterface interface {
	Create(ctx context.Context, workloadCheckpoint *v1beta1.WorkloadCheckpoint, opts v1.CreateOptions) (*v1beta1.WorkloadCheckpoint, error)
	Update(ctx context.Context, workloadCheckpoint *v1beta1.WorkloadCheckpoint, opts v1.UpdateOptions) (*v1beta1.WorkloadCheckpoint, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1beta1.WorkloadCheckpoint, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1beta1.WorkloadCheckpointList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.WorkloadCheckpoint, err error)
	WorkloadCheckpointExpansion
}

// workloadCheckpoints implements WorkloadCheckpointInterface
type workloadCheckpoints struct {
	client rest.Interface
	ns     string
}

// newWorkloadCheckpoints returns a WorkloadCheckpoints
func newWorkloadCheckpoints(c *PortraitV1beta1Client, namespace string) *workloadCheckpoints {
	return &workloadCheckpoints{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the workloadCheckpoint, and returns the corresponding workloadCheckpoint object, and an error if there is any.
func (c *workloadCheckpoints) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.WorkloadCheckpoint, err error) {
	result = &v1beta1.WorkloadCheckpoint{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("workloadcheckpoints").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of WorkloadCheckpoints that match those selectors.
func (c *workloadCheckpoints) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.WorkloadCheckpointList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1beta1.WorkloadCheckpointList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("workloadcheckpoints").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested workloadCheckpoints.
func (c *workloadCheckpoints) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("workloadcheckpoints").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a workloadCheckpoint and creates it.  Returns the server's representation of the workloadCheckpoint, and an error, if there is any.
func (c *workloadCheckpoints) Create(ctx context.Context, workloadCheckpoint *v1beta1.WorkloadCheckpoint, opts v1.CreateOptions) (result *v1beta1.WorkloadCheckpoint, err error) {
	result = &v1beta1.WorkloadCheckpoint{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("workloadcheckpoints").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(workloadCheckpoint).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a workloadCheckpoint and updates it. Returns the server's representation of the workloadCheckpoint, and an error, if there is any.
func (c *workloadCheckpoints) Update(ctx context.Context, workloadCheckpoint *v1beta1.WorkloadCheckpoint, opts v1.UpdateOptions) (result *v1beta1.WorkloadCheckpoint, err error) {
	result = &v1beta1.WorkloadCheckpoint{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("workloadcheckpoints").
		Name(workloadCheckpoint.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(workloadCheckpoint).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the workloadCheckpoint and deletes it. Returns an error if one occurs.
func (c *workloadCheckpoints) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("workloadcheckpoints").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *workloadCheckpoints) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("workloadcheckpoints").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched workloadCheckpoint.
func (c *workloadCheckpoints) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.WorkloadCheckpoint, err error) {
	result = &v1beta1.WorkloadCheckpoint{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("workloadcheckpoints").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
