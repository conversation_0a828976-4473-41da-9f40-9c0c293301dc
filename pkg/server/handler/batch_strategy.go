package handler

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

type GetBatchStrategyRequest struct {
	Name     string `json:"name" form:"name"`
	PageNum  int    `json:"page_num" form:"page_num" binding:"required"`
	PageSize int    `json:"page_size" form:"page_size" binding:"required"`
}

type CreateBatchStrategyRequest struct {
	Name     string `json:"name" validate:"required"`
	Strategy string `json:"strategy" validate:"required"`
}

type UpdateBatchStrategyRequest struct {
	ID       int64  `json:"id" uri:"id" validate:"required"`
	Name     string `json:"name"`
	Strategy string `json:"strategy"`
}

func GetBatchStrategy(c *gin.Context) {
	var req GetBatchStrategyRequest
	if err := util.BindAndValidate(c, &req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	var batchStrategies []*db.AppBatchStrategy
	var err error
	if req.Name != "" {
		batchStrategies, err = db.GetAppBatchStrategyByNameLike(req.Name)
	} else {
		batchStrategies, err = db.GetAppBatchStrategy()
	}
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}
	if req.PageSize > 0 {
		batchStrategies = batchStrategies[(req.PageNum-1)*req.PageSize : req.PageNum*req.PageSize]
	}
	c.JSON(http.StatusOK, batchStrategies)
}

func CreateBatchStrategy(c *gin.Context) {
	var req CreateBatchStrategyRequest
	if err := util.BindAndValidate(c, &req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	if err := db.CreateAppBatchStrategy(&db.AppBatchStrategy{Name: req.Name, Strategy: req.Strategy}); err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, nil)
}

func UpdateBatchStrategy(c *gin.Context) {
	var req UpdateBatchStrategyRequest
	if err := util.BindAndValidate(c, &req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}
	if req.Name == "" && req.Strategy == "" {
		handleError(c, http.StatusBadRequest, errors.New("name and strategy cannot be empty"))
		return
	}
	batchStrategy, err := db.GetAppBatchStrategyById(req.ID)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}
	if req.Name != batchStrategy.Name || req.Strategy != batchStrategy.Strategy {
		if req.Name != "" {
			batchStrategy.Name = req.Name
		}
		if req.Strategy != "" {
			batchStrategy.Strategy = req.Strategy
		}
		if err := db.UpdateAppBatchStrategy(batchStrategy); err != nil {
			handleError(c, http.StatusInternalServerError, err)
			return
		}
	}
	c.JSON(http.StatusOK, nil)
}
