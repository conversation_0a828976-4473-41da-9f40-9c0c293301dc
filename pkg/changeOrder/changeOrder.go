package changeOrder

import (
	"git.woa.com/kmetis/starship-engine/pkg/util"

	"k8s.io/klog/v2"
)

func CheckChangeOrderApplicant(changeID, user string) bool {
	if changeID == "" || user == "" {
		return false
	}

	changeOrder, err := GetPostOrder(changeID)
	if err != nil {
		klog.Errorf("Failed to get change order %s: %v", changeID, err)
		return false
	}
	return util.Contains(changeOrder.Data.Item.Applicant, user)
}
