package internalapi

import (
	"encoding/json"
	"testing"

	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/model"
)

func TestBuildUpdateReq_WithRawValues(t *testing.T) {
	// 测试包含RawValues的ExtendInfo
	extendInfo := model.ExtendInfo{
		RawValues: "test-raw-values-content",
		Scheduler: true,
	}
	extendInfoJSON, err := json.Marshal(extendInfo)
	if err != nil {
		t.Fatalf("Failed to marshal extend info: %v", err)
	}

	cluster := &db.TaskClusterInfo{
		ClusterId:  "cls-test123",
		Component:  "apiserver",
		AppAction:  "publish",
		Region:     "ap-guangzhou",
		ChangeId:   "test-change-123",
		ExtendInfo: string(extendInfoJSON),
		AppID:      123456,
		UIN:        "test-uin",
		SubUIN:     "test-sub-uin",
		Token:      "test-token",
		User:       "test-user",
		MetaID:     "test-meta-id",
		Type:       "tke",
		ImageTag:   "v1.0.0",
	}

	req, err := buildUpdateReq(cluster)
	if err != nil {
		t.Fatalf("buildUpdateReq failed: %v", err)
	}

	// 验证RawValues是否正确设置到Addon中
	if req.Values.Addon == nil {
		t.Fatal("Values.Addon should not be nil")
	}

	rawValues, exists := req.Values.Addon["RawValues"]
	if !exists {
		t.Fatal("RawValues should exist in Values.Addon")
	}

	rawValuesStr, ok := rawValues.(string)
	if !ok {
		t.Fatal("RawValues should be a string")
	}

	if rawValuesStr != "test-raw-values-content" {
		t.Errorf("Expected RawValues to be 'test-raw-values-content', got '%s'", rawValuesStr)
	}
}

func TestBuildUpdateReq_WithoutRawValues(t *testing.T) {
	// 测试不包含RawValues的ExtendInfo
	extendInfo := model.ExtendInfo{
		Scheduler: true,
	}
	extendInfoJSON, err := json.Marshal(extendInfo)
	if err != nil {
		t.Fatalf("Failed to marshal extend info: %v", err)
	}

	cluster := &db.TaskClusterInfo{
		ClusterId:  "cls-test123",
		Component:  "apiserver",
		AppAction:  "publish",
		Region:     "ap-guangzhou",
		ChangeId:   "test-change-123",
		ExtendInfo: string(extendInfoJSON),
		AppID:      123456,
		UIN:        "test-uin",
		SubUIN:     "test-sub-uin",
		Token:      "test-token",
		User:       "test-user",
		MetaID:     "test-meta-id",
		Type:       "tke",
		ImageTag:   "v1.0.0",
	}

	req, err := buildUpdateReq(cluster)
	if err != nil {
		t.Fatalf("buildUpdateReq failed: %v", err)
	}

	// 验证Addon为空或不包含RawValues
	if req.Values.Addon != nil {
		if _, exists := req.Values.Addon["RawValues"]; exists {
			t.Error("RawValues should not exist in Values.Addon when not provided")
		}
	}
}

func TestBuildUpdateReq_EmptyExtendInfo(t *testing.T) {
	// 测试空的ExtendInfo
	cluster := &db.TaskClusterInfo{
		ClusterId:  "cls-test123",
		Component:  "apiserver",
		AppAction:  "publish",
		Region:     "ap-guangzhou",
		ChangeId:   "test-change-123",
		ExtendInfo: "",
		AppID:      123456,
		UIN:        "test-uin",
		SubUIN:     "test-sub-uin",
		Token:      "test-token",
		User:       "test-user",
		MetaID:     "test-meta-id",
		Type:       "tke",
		ImageTag:   "v1.0.0",
	}

	req, err := buildUpdateReq(cluster)
	if err != nil {
		t.Fatalf("buildUpdateReq failed: %v", err)
	}

	// 验证Addon为空或不包含RawValues
	if req.Values.Addon != nil {
		if _, exists := req.Values.Addon["RawValues"]; exists {
			t.Error("RawValues should not exist in Values.Addon when ExtendInfo is empty")
		}
	}
}

func TestBuildUpdateReq_InvalidExtendInfo(t *testing.T) {
	// 测试无效的ExtendInfo JSON
	cluster := &db.TaskClusterInfo{
		ClusterId:  "cls-test123",
		Component:  "apiserver",
		AppAction:  "publish",
		Region:     "ap-guangzhou",
		ChangeId:   "test-change-123",
		ExtendInfo: "invalid-json",
		AppID:      123456,
		UIN:        "test-uin",
		SubUIN:     "test-sub-uin",
		Token:      "test-token",
		User:       "test-user",
		MetaID:     "test-meta-id",
		Type:       "tke",
		ImageTag:   "v1.0.0",
	}

	req, err := buildUpdateReq(cluster)
	if err != nil {
		t.Fatalf("buildUpdateReq failed: %v", err)
	}

	// 验证即使ExtendInfo无效，也不应该影响其他功能
	if req.ClusterId != "cls-test123" {
		t.Error("ClusterId should be set correctly even with invalid ExtendInfo")
	}

	// 验证Addon为空或不包含RawValues
	if req.Values.Addon != nil {
		if _, exists := req.Values.Addon["RawValues"]; exists {
			t.Error("RawValues should not exist in Values.Addon when ExtendInfo is invalid JSON")
		}
	}
}

func TestBuildRollbackReq(t *testing.T) {
	cluster := &db.TaskClusterInfo{
		ClusterId:      "cls-test123",
		Component:      "apiserver",
		AppAction:      "publish",
		Region:         "gz", // 使用短地域名
		ChangeId:       "test-change-123",
		AppID:          123456,
		UIN:            "test-uin",
		SubUIN:         "test-sub-uin",
		StarshipTaskId: "test-starship-task-id",
	}

	req, err := buildRollbackReq(cluster)
	if err != nil {
		t.Fatalf("buildRollbackReq failed: %v", err)
	}

	// 验证基本字段
	if req.Action != "RollbackApp" {
		t.Errorf("Expected Action to be 'RollbackApp', got '%s'", req.Action)
	}

	if req.TaskId != 123456 {
		t.Errorf("Expected TaskId to be 123456, got %d", req.TaskId)
	}

	if req.AppID != 123456 {
		t.Errorf("Expected AppID to be 123456, got %d", req.AppID)
	}

	if req.UIN != "test-uin" {
		t.Errorf("Expected UIN to be 'test-uin', got '%s'", req.UIN)
	}

	if req.SubAccountUIN != "test-sub-uin" {
		t.Errorf("Expected SubAccountUIN to be 'test-sub-uin', got '%s'", req.SubAccountUIN)
	}

	// Region should be processed by GetLongRegion, so it might be different
	if req.Region == "" {
		t.Error("Region should not be empty")
	}
}
