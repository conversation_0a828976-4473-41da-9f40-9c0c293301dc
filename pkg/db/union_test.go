package db

import (
	"fmt"
	"reflect"
	"testing"

	"git.woa.com/kmetis/starship-engine/pkg/batchStrategy"
	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

func TestGetClusterTaskCount(t *testing.T) {
	InitSqliteDB()
	count, err := GetTaskStats("", "", []int64{1, 2, 3, 4, 5, 6, 7})
	if err != nil {
		panic(err)
	}
	for _, task := range count {
		fmt.Printf("taskId: %d, total:%d, success:%d ,failed:%d\n", task.TaskId, task.Total, task.Success, task.Failed)
	}
}

func TestDealCompleted(t *testing.T) {
	InitSqliteDB()
	task, _ := GetUnplayedTaskAndSetRunning(2)
	fmt.Println(task)
}

func TestDealComplete(t *testing.T) {
	//InitSqliteDB()
	//err := DealCompleted()
	//if err != nil {
	//	panic(err)
	//}
}

func TestRemoveDuplicateClusters(t *testing.T) {
	tests := []struct {
		name     string
		input    []string
		expected []string
	}{
		{
			name:     "无重复集群",
			input:    []string{"cls-12345678", "cls-87654321", "cls-11111111"},
			expected: []string{"cls-12345678", "cls-87654321", "cls-11111111"},
		},
		{
			name:     "有重复集群",
			input:    []string{"cls-12345678", "cls-87654321", "cls-12345678", "cls-11111111", "cls-87654321"},
			expected: []string{"cls-12345678", "cls-87654321", "cls-11111111"},
		},
		{
			name:     "包含空字符串和空白字符",
			input:    []string{"cls-12345678", "", "  ", "cls-87654321", "cls-12345678", "   cls-11111111   "},
			expected: []string{"cls-12345678", "cls-87654321", "cls-11111111"},
		},
		{
			name:     "全部为空",
			input:    []string{"", "  ", "   "},
			expected: []string{},
		},
		{
			name:     "空切片",
			input:    []string{},
			expected: []string{},
		},
		{
			name:     "单个集群重复多次",
			input:    []string{"cls-12345678", "cls-12345678", "cls-12345678"},
			expected: []string{"cls-12345678"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := removeDuplicateClusters(tt.input)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("removeDuplicateClusters() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// TestPrepareBatchAndPodData_QueryUnplayedTaskCompatibility 测试prepareBatchAndPodData与queryUnplayedTask的兼容性
func TestPrepareBatchAndPodData_QueryUnplayedTaskCompatibility(t *testing.T) {
	// 模拟EksPodStrategy返回的BatchConfig（已经有正确的BatchId序列）
	batches := []batchStrategy.BatchConfig{
		{
			Region:         "qy",
			RegionPriority: 1,
			BatchId:        1,
			SubBatchId:     1,
			Interval:       1200,
			Pods: []util.Pod{
				{ClusterId: "cls-qy-001", Namespace: "default", PodName: "pod-qy-1"},
			},
			Clusters: []string{"cls-qy-001"},
		},
		{
			Region:         "jnec",
			RegionPriority: 2,
			BatchId:        2,
			SubBatchId:     1,
			Interval:       1200,
			Pods: []util.Pod{
				{ClusterId: "cls-jnec-001", Namespace: "default", PodName: "pod-jnec-1"},
			},
			Clusters: []string{"cls-jnec-001"},
		},
	}

	taskId := int64(12345)

	// 调用prepareBatchAndPodData
	dbBatches, dbClusters := prepareBatchAndPodData(taskId, batches)

	t.Logf("Generated %d batch records and %d cluster records", len(dbBatches), len(dbClusters))

	// 验证batch表记录
	for i, batch := range dbBatches {
		t.Logf("Batch %d: TaskId=%d, BatchId=%d, SubBatchId=%d, Region=%s, Interval=%d",
			i+1, batch.TaskId, batch.BatchId, batch.SubBatchId, batch.Region, batch.Interval)

		// 验证TaskId
		if batch.TaskId != taskId {
			t.Errorf("Batch %d: TaskId should be %d, got %d", i+1, taskId, batch.TaskId)
		}

		// 验证BatchId连续性
		expectedBatchId := int64(i + 1)
		if batch.BatchId != expectedBatchId {
			t.Errorf("Batch %d: BatchId should be %d, got %d", i+1, expectedBatchId, batch.BatchId)
		}

		// 验证SubBatchId
		if batch.SubBatchId != 1 {
			t.Errorf("Batch %d: SubBatchId should be 1, got %d", i+1, batch.SubBatchId)
		}
	}

	// 验证cluster表记录
	for i, cluster := range dbClusters {
		// 验证TaskId
		if cluster.TaskId != taskId {
			t.Errorf("Cluster %d: TaskId should be %d, got %d", i+1, taskId, cluster.TaskId)
		}

		// 验证BatchId和SubBatchId与对应的batch记录匹配
		found := false
		for _, batch := range dbBatches {
			if batch.BatchId == cluster.BatchId && batch.SubBatchId == cluster.SubBatchId {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Cluster %d: No matching batch found for BatchId=%d, SubBatchId=%d",
				i+1, cluster.BatchId, cluster.SubBatchId)
		}
	}

	t.Logf("✅ prepareBatchAndPodData compatibility test passed - ready for queryUnplayedTask")
}

// TestHandlePodBasedComponent_AppActionPassing 测试appAction参数传递
func TestHandlePodBasedComponent_AppActionPassing(t *testing.T) {
	// 模拟Pod读取函数
	mockPodReader := func(filePath string) ([]util.Pod, error) {
		return []util.Pod{
			{ClusterId: "cls-test-001", Namespace: "default", PodName: "test-pod-1"},
		}, nil
	}

	task := model.Task{
		Component: model.Component{
			Name: "test-component",
		},
		ClusterFile: "test-file.txt",
	}

	taskId := int64(12345)
	appAction := "precheck"

	var dbBatches []*Batch
	var allDbClusters []*Cluster

	// 调用公共函数
	err := handlePodBasedComponent(task, appAction, taskId, &dbBatches, &allDbClusters, mockPodReader)
	if err != nil {
		t.Logf("handlePodBasedComponent error (expected due to region lookup): %v", err)
		return
	}

	t.Logf("Generated %d batches and %d clusters with appAction=%s", len(dbBatches), len(allDbClusters), appAction)

	// 验证基本结构
	if len(dbBatches) == 0 {
		t.Error("Expected at least one batch")
	}
	if len(allDbClusters) == 0 {
		t.Error("Expected at least one cluster")
	}

	// 验证TaskId正确传递
	for i, batch := range dbBatches {
		if batch.TaskId != taskId {
			t.Errorf("Batch %d: TaskId should be %d, got %d", i+1, taskId, batch.TaskId)
		}
	}

	for i, cluster := range allDbClusters {
		if cluster.TaskId != taskId {
			t.Errorf("Cluster %d: TaskId should be %d, got %d", i+1, taskId, cluster.TaskId)
		}
	}

	t.Logf("✅ handlePodBasedComponent appAction passing test completed")
}
