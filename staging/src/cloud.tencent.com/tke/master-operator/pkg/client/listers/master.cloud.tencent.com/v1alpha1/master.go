/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// MasterLister helps list Masters.
// All objects returned here must be treated as read-only.
type MasterLister interface {
	// List lists all Masters in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.Master, err error)
	// Masters returns an object that can list and get Masters.
	Masters(namespace string) MasterNamespaceLister
	MasterListerExpansion
}

// masterLister implements the MasterLister interface.
type masterLister struct {
	indexer cache.Indexer
}

// NewMasterLister returns a new MasterLister.
func NewMasterLister(indexer cache.Indexer) MasterLister {
	return &masterLister{indexer: indexer}
}

// List lists all Masters in the indexer.
func (s *masterLister) List(selector labels.Selector) (ret []*v1alpha1.Master, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha1.Master))
	})
	return ret, err
}

// Masters returns an object that can list and get Masters.
func (s *masterLister) Masters(namespace string) MasterNamespaceLister {
	return masterNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// MasterNamespaceLister helps list and get Masters.
// All objects returned here must be treated as read-only.
type MasterNamespaceLister interface {
	// List lists all Masters in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.Master, err error)
	// Get retrieves the Master from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1alpha1.Master, error)
	MasterNamespaceListerExpansion
}

// masterNamespaceLister implements the MasterNamespaceLister
// interface.
type masterNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all Masters in the indexer for a given namespace.
func (s masterNamespaceLister) List(selector labels.Selector) (ret []*v1alpha1.Master, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha1.Master))
	})
	return ret, err
}

// Get retrieves the Master from the indexer for a given namespace and name.
func (s masterNamespaceLister) Get(name string) (*v1alpha1.Master, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1alpha1.Resource("master"), name)
	}
	return obj.(*v1alpha1.Master), nil
}
