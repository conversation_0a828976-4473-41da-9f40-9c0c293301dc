package util

import (
	"bufio"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"os"
	"os/user"
	"strings"
	"time"

	ianvs "git.woa.com/ianvs/ianvs-sdk/pkg/client"
	"git.woa.com/tke/tops/pkg/component/http"
	"git.woa.com/tke/tops/pkg/context"
	"git.woa.com/tke/tops/pkg/logger/logrus"
	"git.woa.com/tke/tops/pkg/service/cauth"
	regionUtil "git.woa.com/tke/tops/pkg/util/region"
	"github.com/Masterminds/semver"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/api/v1/task"
	"git.woa.com/kmetis/starship-engine/pkg/consts"
)

// FileReader 统一的文件读取接口
type FileReader interface {
	ReadFromPath(path string) ([]Pod, error)
	ReadFromContent(content string) ([]Pod, error)
}

// EkletAgentFileReader eklet-agent组件的文件读取器
type EkletAgentFileReader struct{}

// ReadFromPath 从文件路径读取eklet-agent数据
func (r *EkletAgentFileReader) ReadFromPath(path string) ([]Pod, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, fmt.Errorf("failed to open agent file: %w", err)
	}
	defer file.Close()

	return r.readFromReader(file)
}

// ReadFromContent 从内存内容读取eklet-agent数据
func (r *EkletAgentFileReader) ReadFromContent(content string) ([]Pod, error) {
	reader := strings.NewReader(content)
	return r.readFromReader(reader)
}

// readFromReader 统一的读取逻辑
func (r *EkletAgentFileReader) readFromReader(reader io.Reader) ([]Pod, error) {
	var agents []Pod
	scanner := bufio.NewScanner(reader)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid line format, expected 'clusterId namespace': %s", line)
		}

		agents = append(agents, Pod{
			ClusterId: parts[0],
			Namespace: parts[1],
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading agent data: %w", err)
	}

	if len(agents) == 0 {
		return nil, fmt.Errorf("no valid cluster data found in file content")
	}

	return agents, nil
}

type Application struct {
	Namespace string
	Name      string
}

type Pod struct {
	ClusterId string
	Namespace string
	PodName   string
}

func GetClusterTypeById(clusterId string) (string, error) {
	_, regionID, err := DecodeClusterID(clusterId)
	if err != nil {
		return "", fmt.Errorf("DeCodeClusterId(%s) error!%s", clusterId, err.Error())
	}
	return GetClusterType(regionID), nil
}

func GetUinByAppId(url string, appId uint64) (string, error) {
	httpExec := http.NewHTTP(time.Second * 10)
	noDataCtx := context.NewNoData(logrus.New())

	cauthSvc := cauth.NewCAuthService(url, httpExec)
	uin, err := cauthSvc.GetUINByAppId(noDataCtx, appId)
	if err != nil {
		return uin, fmt.Errorf("get uin by appid failed: %v", err)
	}
	return uin, nil
}

func GetShortRegion(region string) string {
	if strings.HasPrefix(region, "-") {
		return regionUtil.LongToShort(region)
	}
	return region
}

func GetLongRegion(region string) string {
	if !strings.HasPrefix(region, "-") {
		return regionUtil.ShortToLong(region)
	}
	return region
}

func StringToSliceMap(str string) ([]map[string]string, error) {
	var result []map[string]string
	err := json.Unmarshal([]byte(str), &result)
	if err != nil {
		klog.Errorf("StringToSliceMap(%s) error!%s", str, err.Error())
		return nil, err
	}
	return result, nil
}

func ReadFile(fileName string) ([]string, error) {
	file, err := os.Open(fileName)
	if err != nil {
		return []string{}, err
	}
	defer file.Close()

	var clusters []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		clusters = append(clusters, scanner.Text())
	}
	if scanner.Err() != nil {
		return []string{}, err
	}
	return clusters, nil
}

// ReadFileFromContent 从内存内容读取集群列表，适用于普通组件
// 文件每行格式应为: clusterId，其中clusterId为集群ID
func ReadFileFromContent(content string) ([]string, error) {
	if strings.TrimSpace(content) == "" {
		return nil, fmt.Errorf("file content is empty")
	}

	reader := strings.NewReader(content)
	var clusters []string
	scanner := bufio.NewScanner(reader)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue // 跳过空行
		}
		clusters = append(clusters, line)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file content: %w", err)
	}

	if len(clusters) == 0 {
		return nil, fmt.Errorf("no valid cluster data found in file content")
	}

	return clusters, nil
}

func ReadProjectFile(projectFile string) ([]Application, error) {
	file, err := os.Open(projectFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open project file: %w", err)
	}
	defer file.Close()

	var projects []Application
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid line format: %s", line)
		}

		projects = append(projects, Application{
			Namespace: parts[0],
			Name:      parts[1],
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading project file: %w", err)
	}

	return projects, nil
}

// ReadEksPodFile 读取包含clusterId、namespace和pod-name的文件
// 文件每行格式应为: clusterId namespace pod-name
func ReadEksPodFile(podFile string) ([]Pod, error) {
	file, err := os.Open(podFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open pod file: %w", err)
	}
	defer file.Close()

	var pods []Pod
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) != 3 {
			return nil, fmt.Errorf("invalid line format, expected 'clusterId namespace pod-name': %s", line)
		}

		pods = append(pods, struct {
			ClusterId string
			Namespace string
			PodName   string
		}{
			ClusterId: parts[0],
			Namespace: parts[1],
			PodName:   parts[2],
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading pod file: %w", err)
	}

	return pods, nil
}

// ReadEkletAgentFile 读取包含clusterId和namespace的文件
// 文件每行格式应为: clusterId namespace，其中clusterId为集群ID，namespace作为eksId使用
// 保持向后兼容性的函数
func ReadEkletAgentFile(agentFile string) ([]Pod, error) {
	reader := &EkletAgentFileReader{}
	return reader.ReadFromPath(agentFile)
}

// ReadEkletAgentFileFromContent 从内存内容读取eklet-agent数据
// 新增函数，支持直接处理文件内容
func ReadEkletAgentFileFromContent(content string) ([]Pod, error) {
	reader := &EkletAgentFileReader{}
	return reader.ReadFromContent(content)
}

// ReadTcrCertFile 读取包含clusterId和namespace的文件
// 文件每行格式应为: clusterId namespace
func ReadTcrCertFile(certFile string) ([]Pod, error) {
	file, err := os.Open(certFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open cert file: %w", err)
	}
	defer file.Close()

	var certs []Pod
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid line format, expected 'clusterId namespace': %s", line)
		}

		certs = append(certs, Pod{
			ClusterId: parts[0],
			Namespace: parts[1],
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading cert file: %w", err)
	}

	return certs, nil
}

func FormatAge(duration time.Duration) string {
	seconds := int(duration.Seconds())
	minutes := seconds / 60
	hours := minutes / 60
	days := hours / 24
	years := days / 365

	if years > 0 {
		return fmt.Sprintf("%dy", years)
	} else if days > 0 {
		return fmt.Sprintf("%dd", days)
	} else if hours > 0 {
		return fmt.Sprintf("%dh", hours)
	} else if minutes > 0 {
		return fmt.Sprintf("%dm", minutes)
	} else {
		return fmt.Sprintf("%ds", seconds)
	}
}

func GenerateRandomString(length int) string {
	const letters = "abcdefghijklmnopqrstuvwxyz0123456789"
	result := make([]byte, length)
	for i := range result {
		index, err := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		if err != nil {
			// 处理错误，这里简单地打印错误并使用默认字符
			fmt.Printf("Error generating random index: %v\n", err)
			result[i] = 'a'
		} else {
			result[i] = letters[index.Int64()]
		}
	}
	return string(result)
}

func GetCurrentUser() (string, error) {
	currentUser, err := user.Current()
	if err != nil {
		fmt.Println("Get Current User error.")
		klog.V(2).InfoS("Get current user error!", "err", err)
		return "", err
	}
	return currentUser.Username, nil
}

func CheckTimeIsValid(startTimeStr, endTimeStr string) bool {
	// 加载UTC时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Printf("Load location error: %v\n", err)
		klog.V(1).ErrorS(err, "Load location error.")
		return false
	}

	// 使用时区解析开始时间
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, loc)
	if err != nil {
		fmt.Printf("Parse publish timewindow starttime error: %v\n", err)
		klog.V(1).ErrorS(err, "Parse publish timeWindow startTime error.")
		return false
	}

	// 使用时区解析结束时间
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, loc)
	if err != nil {
		fmt.Printf("Parse publish timeWindow endTime error: %v\n", err)
		klog.V(1).ErrorS(err, "Parse publish timeWindow endTime error.")
		return false
	}

	// 获取当前时间并转换到UTC时区
	now := time.Now().In(loc)

	if now.Before(startTime) || now.After(endTime) {
		fmt.Println("Change order has expired.") // 修改语法错误
		klog.V(2).InfoS("Change order has expired.")
		return false
	}
	return true
}

func Contains(slice []string, str string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}

func GetRestConfig(cls, token, user string) (config *rest.Config, err error) {
	defer func() {
		if r := recover(); r != nil {
			config = nil
			err = fmt.Errorf("failed to get rest config")
		}
	}()

	client := ianvs.NewTmpTokenClient(user, token)
	config, err = client.GetRestConfig(cls)
	if err != nil {
		return nil, err
	}
	return
}

func AddLink(component string, clusters []*task.ClusterInfo) {
	// 定义链接生成函数映射
	linkGenerators := map[string]func(cluster *task.ClusterInfo){
		consts.APISERVER: generateAPIServerLinks,
		consts.EKLET:     generateEkletLinks,
		consts.COREDNS:   generateCoreDNSLinks,
		consts.SCHEDULER: generateSchedulerLinks,
	}

	// 获取对应的生成函数，默认使用APISERVER的生成函数
	generator, exists := linkGenerators[component]
	if !exists {
		generator = generateAPIServerLinks
	}

	// 为每个集群生成链接
	for _, cluster := range clusters {
		generator(cluster)
	}
}

// 生成CoreDNS相关链接
func generateCoreDNSLinks(cluster *task.ClusterInfo) {
	cluster.ClusterLink = fmt.Sprintf(clusterLinkFormat, cluster.Type, cluster.ClusterId)
	cluster.MonitorLink = fmt.Sprintf(coreDNSMonitorLinkFormat, cluster.Type, cluster.Region, cluster.ClusterId)
	cluster.LogLink = fmt.Sprintf(coreDNSLogLinkFormat, cluster.Type, cluster.ClusterId)
}

// 生成Eklet相关链接
func generateEkletLinks(cluster *task.ClusterInfo) {
	cluster.ClusterLink = fmt.Sprintf(clusterLinkFormat, cluster.Type, cluster.ClusterId)

	var baseLink string
	if cluster.Type == consts.TKE {
		baseLink = fmt.Sprintf(ekletTkeMonitorLinkFormat, cluster.MetaId, cluster.ClusterId)
	} else {
		baseLink = fmt.Sprintf(ekletEksMonitorLinkFormat, cluster.MetaId, cluster.ClusterId, cluster.ClusterId)
	}

	cluster.MonitorLink = baseLink
	cluster.LogLink = baseLink
}

// 生成API Server相关链接
func generateAPIServerLinks(cluster *task.ClusterInfo) {
	generateCommonLinks(cluster)
	cluster.LogLink = fmt.Sprintf(apiServerLogLinkFormat, cluster.MetaId, cluster.ClusterId, cluster.ClusterId)
}

// 生成Scheduler相关链接
func generateSchedulerLinks(cluster *task.ClusterInfo) {
	generateCommonLinks(cluster)
	cluster.LogLink = fmt.Sprintf(schedulerLogLinkFormat, cluster.MetaId, cluster.ClusterId, cluster.ClusterId)
}

// 生成公共链接部分
func generateCommonLinks(cluster *task.ClusterInfo) {
	cluster.ClusterLink = fmt.Sprintf(clusterLinkFormat, cluster.Type, cluster.ClusterId)
	cluster.MonitorLink = fmt.Sprintf(commonMonitorLinkFormat, cluster.Region, cluster.ClusterId)
}

func LoadMap(path string) (map[string][]string, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read OpsTeam config: %v", err)
	}

	loadMap := make(map[string][]string)

	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		if line == "" {
			continue
		}
		parts := strings.Split(line, ":")
		if len(parts) != 2 {
			continue
		}
		key := strings.TrimSpace(parts[0])
		values := strings.Split(strings.TrimSpace(parts[1]), ",")
		loadMap[key] = values
	}

	return loadMap, nil
}

// 比较两个版本的大小
func CompareVersion(v1, v2 string) (int, error) {
	version1, err := semver.NewVersion(v1)
	if err != nil {
		return 0, fmt.Errorf("invalid version format: %s (%v)", v1, err)
	}
	version2, err := semver.NewVersion(v2)
	if err != nil {
		return 0, fmt.Errorf("invalid version format: %s (%v)", v2, err)
	}
	return version1.Compare(version2), nil
}
