/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeCOSs implements COSInterface
type FakeCOSs struct {
	Fake *FakePlatformV1
}

var cossResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "coss"}

var cossKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "COS"}

// Get takes name of the cOS, and returns the corresponding cOS object, and an error if there is any.
func (c *FakeCOSs) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.COS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(cossResource, name), &platformv1.COS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.COS), err
}

// List takes label and field selectors, and returns the list of COSs that match those selectors.
func (c *FakeCOSs) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.COSList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(cossResource, cossKind, opts), &platformv1.COSList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.COSList{ListMeta: obj.(*platformv1.COSList).ListMeta}
	for _, item := range obj.(*platformv1.COSList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested cOSs.
func (c *FakeCOSs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(cossResource, opts))
}

// Create takes the representation of a cOS and creates it.  Returns the server's representation of the cOS, and an error, if there is any.
func (c *FakeCOSs) Create(ctx context.Context, cOS *platformv1.COS, opts v1.CreateOptions) (result *platformv1.COS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(cossResource, cOS), &platformv1.COS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.COS), err
}

// Update takes the representation of a cOS and updates it. Returns the server's representation of the cOS, and an error, if there is any.
func (c *FakeCOSs) Update(ctx context.Context, cOS *platformv1.COS, opts v1.UpdateOptions) (result *platformv1.COS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(cossResource, cOS), &platformv1.COS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.COS), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeCOSs) UpdateStatus(ctx context.Context, cOS *platformv1.COS, opts v1.UpdateOptions) (*platformv1.COS, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(cossResource, "status", cOS), &platformv1.COS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.COS), err
}

// Delete takes name of the cOS and deletes it. Returns an error if one occurs.
func (c *FakeCOSs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(cossResource, name), &platformv1.COS{})
	return err
}

// Patch applies the patch and returns the patched cOS.
func (c *FakeCOSs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.COS, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(cossResource, name, pt, data, subresources...), &platformv1.COS{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.COS), err
}
