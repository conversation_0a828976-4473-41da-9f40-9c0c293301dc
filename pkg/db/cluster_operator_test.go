package db

import (
	"fmt"
	"testing"
)

func TestGetTaskFailureRate(t *testing.T) {
	InitSqliteDB()
	rate, err := GetBatchFailureRate()
	if err != nil {
		t.Error(err)
	}
	fmt.Println(rate)
}

func TestGetUnplayedTaskAndSetRunning(t *testing.T) {
	// InitDB()
	// task, err := GetUnplayedTaskAndSetRunning()
	// if err != nil {
	// 	t.Error(err)
	// }
	// fmt.Println(task)
}

func TestDealFuse(t *testing.T) {
	//InitDB()
	//err := DealFuse()
	//if err != nil {
	//	panic(err)
	//}
}

func TestGetClusterInfoByStatus(t *testing.T) {
	InitSqliteDB()
	clusterInfo, err := GetClusterInfoByStatus("running")
	if err != nil {
		t.Error(err)
	}
	fmt.Println(clusterInfo)
}

func TestUpdateClusterById(t *testing.T) {
	InitSqliteDB()
	cluster := &TaskClusterInfo{
		ID:             1,
		AppID:          uint64(123456),
		Type:           "test_type",
		MetaID:         "test_meta",
		UIN:            "test_uin",
		SubUIN:         "test_sub_uin",
		StarshipTaskId: "321",
	}
	err := UpdateClusterById(cluster)
	if err != nil {
		t.Error(err)
	}
}

func TestGetClusterInfo(t *testing.T) {

}

func TestGetClusterByFields(t *testing.T) {
	InitSqliteDB()
	clusters, err := GetClusterByFields(map[string]interface{}{
		"status": "sfds",
	})

	if err != nil {
		panic(err)
	}

	for _, cluster := range clusters {
		fmt.Println(cluster)
	}

	fmt.Println(len(clusters))
}

func TestGetClusterStatusStatistics(t *testing.T) {
	//InitDB()
	//statistics, err := GetClusterStatusStatistics(5)
	//if err != nil {
	//	panic(err)
	//}
	//fmt.Println(statistics)
}

func TestGetClustersByTaskIdAndStatusGroup(t *testing.T) {
	InitSqliteDB()
	clusters, err := GetClusterByFields(map[string]interface{}{
		"task_id": 1,
	})
	if err != nil {
		panic(err)
	}
	clustersMap := make(map[string][]*Cluster)
	for _, cls := range clusters {
		if cls.Status != "" {
			clustersMap[cls.Status] = append(clustersMap[cls.Status], cls)
		}
	}

	fmt.Println(len(clusters))
	for key, cls := range clustersMap {
		fmt.Println("key:", key, " num:", len(cls))
		for _, cl := range cls {
			fmt.Println(key, ":", cl)
		}
	}
}
