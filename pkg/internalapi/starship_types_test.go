package internalapi

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestTaskWrapper_UnmarshalJSON 测试 TaskWrapper 的动态命名规则解析
func TestTaskWrapper_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		wantErr  bool
	}{
		{
			name: "用户提供的驼峰命名法数据",
			jsonData: `{
      "AppName": "eklet",
      "Batch": 1,
      "ChangeId": "102323-1758091250",
      "ClusterId": "cls-ldva5idu",
      "CreateTime": "2025-09-17 07:38:03",
      "Envs": "[]",
      "ExtendInfo": "",
      "Name": "eklet upgrade",
      "Reason": "",
      "Region": "",
      "Stage": "",
      "Status": "pending",
      "SubTasks": [
        {
          "Action": "precheck",
          "AppName": "eklet",
          "CostTime": 0,
          "CreateTime": "2025-09-17 07:38:03",
          "ParentTaskId": 1,
          "Reason": "",
          "Risks": [],
          "Status": "pending",
          "SubTaskId": 1,
          "UpdateTime": "2025-09-17 07:38:03"
        },
        {
          "Action": "upgrade",
          "AppName": "eklet",
          "CostTime": 0,
          "CreateTime": "2025-09-17 07:38:03",
          "ParentTaskId": 1,
          "Reason": "",
          "Risks": [],
          "Status": "pending",
          "SubTaskId": 2,
          "UpdateTime": "2025-09-17 07:38:03"
        },
        {
          "Action": "postcheck",
          "AppName": "eklet",
          "CostTime": 0,
          "CreateTime": "2025-09-17 07:38:03",
          "ParentTaskId": 1,
          "Reason": "",
          "Risks": [],
          "Status": "pending",
          "SubTaskId": 3,
          "UpdateTime": "2025-09-17 07:38:03"
        }
      ],
      "TaskId": 1,
      "Timeout": 60,
      "Type": "",
      "UpdateTime": "2025-09-17 07:38:03"
    }`,
			wantErr: false,
		},
		{
			name: "驼峰命名法",
			jsonData: `{
				"taskId": "task-123",
				"taskName": "test-task",
				"taskStatus": "running",
				"createTime": "2023-01-01T00:00:00Z"
			}`,
			wantErr: false,
		},
		{
			name: "下划线命名法",
			jsonData: `{
				"task_id": "task-456",
				"task_name": "test-task-2",
				"task_status": "completed",
				"create_time": "2023-01-02T00:00:00Z"
			}`,
			wantErr: false,
		},
		{
			name: "混合命名法",
			jsonData: `{
				"task_id": "task-789",
				"TaskName": "test-task-3",
				"task_status": "failed",
				"CreateTime": "2023-01-03T00:00:00Z"
			}`,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var wrapper TaskWrapper
			err := json.Unmarshal([]byte(tt.jsonData), &wrapper)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestTaskWrapper_MarshalJSON 测试 TaskWrapper 的序列化
func TestTaskWrapper_MarshalJSON(t *testing.T) {
	wrapper := TaskWrapper{
		// 这里需要根据实际的 pb.DescribeTaskReply 结构体来设置字段
		// DescribeTaskReply: pb.DescribeTaskReply{...},
	}

	data, err := json.Marshal(wrapper)
	assert.NoError(t, err)
	assert.NotEmpty(t, data)
}

// TestDescribeAppUpgradingProgressResponseImp_UnmarshalJSON 测试完整响应结构的解析
func TestDescribeAppUpgradingProgressResponseImp_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		wantErr  bool
	}{
		{
			name: "驼峰命名法的Task字段",
			jsonData: `{
				"RequestId": "req-123",
				"Task": {
					"taskId": "task-123",
					"taskName": "test-task",
					"taskStatus": "running"
				}
			}`,
			wantErr: false,
		},
		{
			name: "下划线命名法的Task字段",
			jsonData: `{
				"RequestId": "req-456",
				"Task": {
					"task_id": "task-456",
					"task_name": "test-task-2",
					"task_status": "completed"
				}
			}`,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var response DescribeAppUpgradingProgressResponseImp
			err := json.Unmarshal([]byte(tt.jsonData), &response)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, response.RequestId)
			}
		})
	}
}

// TestTaskWrapper_DetailedParsing 详细测试字段解析
func TestTaskWrapper_DetailedParsing(t *testing.T) {
	// 用户提供的实际JSON数据
	jsonData := `{
      "AppName": "eklet",
      "Batch": 1,
      "ChangeId": "102323-1758091250",
      "ClusterId": "cls-ldva5idu",
      "CreateTime": "2025-09-17 07:38:03",
      "Envs": "[]",
      "ExtendInfo": "",
      "Name": "eklet upgrade",
      "Reason": "",
      "Region": "",
      "Stage": "",
      "Status": "pending",
      "SubTasks": [
        {
          "Action": "precheck",
          "AppName": "eklet",
          "CostTime": 0,
          "CreateTime": "2025-09-17 07:38:03",
          "ParentTaskId": 1,
          "Reason": "",
          "Risks": [],
          "Status": "pending",
          "SubTaskId": 1,
          "UpdateTime": "2025-09-17 07:38:03"
        }
      ],
      "TaskId": 1,
      "Timeout": 60,
      "Type": "",
      "UpdateTime": "2025-09-17 07:38:03"
    }`

	var wrapper TaskWrapper
	err := json.Unmarshal([]byte(jsonData), &wrapper)
	assert.NoError(t, err)

	// 打印解析结果以便调试
	t.Logf("Parsed TaskWrapper: %+v", wrapper)

	// 验证序列化后的结果
	serialized, err := json.Marshal(wrapper)
	assert.NoError(t, err)
	t.Logf("Serialized result: %s", string(serialized))
}

// BenchmarkTaskWrapper_UnmarshalJSON 性能测试
func BenchmarkTaskWrapper_UnmarshalJSON(b *testing.B) {
	jsonData := `{
		"task_id": "task-123",
		"task_name": "benchmark-task",
		"task_status": "running",
		"create_time": "2023-01-01T00:00:00Z"
	}`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var wrapper TaskWrapper
		json.Unmarshal([]byte(jsonData), &wrapper)
	}
}
