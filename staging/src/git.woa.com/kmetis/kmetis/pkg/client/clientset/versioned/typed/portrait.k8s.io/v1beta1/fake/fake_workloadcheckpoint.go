/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/portrait.k8s.io/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeWorkloadCheckpoints implements WorkloadCheckpointInterface
type FakeWorkloadCheckpoints struct {
	Fake *FakePortraitV1beta1
	ns   string
}

var workloadcheckpointsResource = schema.GroupVersionResource{Group: "portrait.k8s.io", Version: "v1beta1", Resource: "workloadcheckpoints"}

var workloadcheckpointsKind = schema.GroupVersionKind{Group: "portrait.k8s.io", Version: "v1beta1", Kind: "WorkloadCheckpoint"}

// Get takes name of the workloadCheckpoint, and returns the corresponding workloadCheckpoint object, and an error if there is any.
func (c *FakeWorkloadCheckpoints) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.WorkloadCheckpoint, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(workloadcheckpointsResource, c.ns, name), &v1beta1.WorkloadCheckpoint{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.WorkloadCheckpoint), err
}

// List takes label and field selectors, and returns the list of WorkloadCheckpoints that match those selectors.
func (c *FakeWorkloadCheckpoints) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.WorkloadCheckpointList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(workloadcheckpointsResource, workloadcheckpointsKind, c.ns, opts), &v1beta1.WorkloadCheckpointList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1beta1.WorkloadCheckpointList{ListMeta: obj.(*v1beta1.WorkloadCheckpointList).ListMeta}
	for _, item := range obj.(*v1beta1.WorkloadCheckpointList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested workloadCheckpoints.
func (c *FakeWorkloadCheckpoints) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(workloadcheckpointsResource, c.ns, opts))

}

// Create takes the representation of a workloadCheckpoint and creates it.  Returns the server's representation of the workloadCheckpoint, and an error, if there is any.
func (c *FakeWorkloadCheckpoints) Create(ctx context.Context, workloadCheckpoint *v1beta1.WorkloadCheckpoint, opts v1.CreateOptions) (result *v1beta1.WorkloadCheckpoint, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(workloadcheckpointsResource, c.ns, workloadCheckpoint), &v1beta1.WorkloadCheckpoint{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.WorkloadCheckpoint), err
}

// Update takes the representation of a workloadCheckpoint and updates it. Returns the server's representation of the workloadCheckpoint, and an error, if there is any.
func (c *FakeWorkloadCheckpoints) Update(ctx context.Context, workloadCheckpoint *v1beta1.WorkloadCheckpoint, opts v1.UpdateOptions) (result *v1beta1.WorkloadCheckpoint, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(workloadcheckpointsResource, c.ns, workloadCheckpoint), &v1beta1.WorkloadCheckpoint{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.WorkloadCheckpoint), err
}

// Delete takes name of the workloadCheckpoint and deletes it. Returns an error if one occurs.
func (c *FakeWorkloadCheckpoints) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(workloadcheckpointsResource, c.ns, name), &v1beta1.WorkloadCheckpoint{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeWorkloadCheckpoints) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(workloadcheckpointsResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1beta1.WorkloadCheckpointList{})
	return err
}

// Patch applies the patch and returns the patched workloadCheckpoint.
func (c *FakeWorkloadCheckpoints) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.WorkloadCheckpoint, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(workloadcheckpointsResource, c.ns, name, pt, data, subresources...), &v1beta1.WorkloadCheckpoint{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.WorkloadCheckpoint), err
}
