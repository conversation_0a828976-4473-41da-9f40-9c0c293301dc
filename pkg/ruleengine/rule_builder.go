package ruleengine

import (
	"fmt"
	"os"
	"strings"

	"sigs.k8s.io/yaml"
)

const (
	RULE_PREFIX = "cluster"
)

type BatchRule struct {
	BatchId   int        `yaml:"batchId"`
	BatchName string     `yaml:"batchName"`
	Rule      Matcher    `yaml:"rule"`
	SubBatch  []SubBatch `yaml:"subBatch"`
}

type SubBatch struct {
	SubBatchId   int            `yaml:"subBatchId"`
	SubBatchName string         `yaml:"subBatchName"`
	Interval     int64          `yaml:"interval"`
	PostCall     []CallFunction `yaml:"postCall"`
	Match        Matcher        `yaml:"match"`
}

type CallFunction struct {
	Name       string                 `yaml:"name"`
	CheckPoint string                 `yaml:"checkpoint"`
	Params     map[string]interface{} `yaml:"params"`
}

type Matcher struct {
	Operator string      `yaml:"operator"`
	Rules    []MatchRule `yaml:"rules"`
}

type MatchRule struct {
	Operator   string          `yaml:"operator"`
	Conditions []RuleCondition `yaml:"conditions"`
}

type RuleCondition struct {
	Key      string      `yaml:"key"`
	Operator string      `yaml:"operator"`
	Value    interface{} `yaml:"value"`
}

// FilterRule 集群筛选规则
type FilterRule Matcher

func LoadBatchRule(file string) ([]BatchConfig, error) {
	data, err := os.ReadFile(file)
	if err != nil {
		return nil, err
	}
	var batchConfig []BatchConfig
	err = yaml.Unmarshal(data, &batchConfig)
	return batchConfig, err
}

func buildMatchRuleSql(matchRule MatchRule) (string, error) {
	var builder strings.Builder
	if len(matchRule.Conditions) == 0 {
		return "", fmt.Errorf(`match rule condition is empty`)
	}

	var err error
	for i, condition := range matchRule.Conditions {
		var r string
		// 先处理自定义函数
		if ExistUserFunc(condition.Operator) {
			r = fmt.Sprintf("%s(%s.%s, '%s')", condition.Operator, RULE_PREFIX, condition.Key, condition.Value)
		} else {
			switch condition.Value.(type) {
			case string:
				r, err = buildStringValueRule(condition)
			case float64:
				r, err = buildFloat64ValueRule(condition)
			case int:
				r, err = buildIntValueRule(condition)
			case int64:
				r, err = buildInt64ValueRule(condition)
			case []string:
				r, err = buildStringArrayValueRule(condition)
			case []float64:
				r, err = buildFloat64ArrayValueRule(condition)
			case []int:
				r, err = buildIntArrayValueRule(condition)
			case []int64:
				r, err = buildInt64ArrayValueRule(condition)
			case []interface{}:
				arr, ok := condition.Value.([]interface{})
				if !ok {
					return "", fmt.Errorf(`value %v is not a interface slice`, condition.Value)
				}
				if len(arr) == 0 {
					return "", fmt.Errorf(`value %v is empty`, condition.Value)
				}

				switch arr[0].(type) {
				case string:
					newArr := make([]string, len(arr))
					for i, v := range arr {
						s, ok := v.(string)
						if !ok {
							return "", fmt.Errorf(`value %v is not a string`, v)
						}
						newArr[i] = s
					}
					condition.Value = newArr
					r, err = buildStringArrayValueRule(condition)
				case float64:
					newArr := make([]float64, len(arr))
					for i, v := range arr {
						f, ok := v.(float64)
						if !ok {
							return "", fmt.Errorf(`value %v is not a float64`, v)
						}
						newArr[i] = f
					}
					condition.Value = newArr
					r, err = buildFloat64ArrayValueRule(condition)
				case int:
					newArr := make([]int, len(arr))
					for i, v := range arr {
						val, ok := v.(int)
						if !ok {
							return "", fmt.Errorf(`value %v is not a int`, v)
						}
						newArr[i] = val
					}
					condition.Value = newArr
					r, err = buildIntArrayValueRule(condition)
				case int64:
					newArr := make([]int64, len(arr))
					for i, v := range arr {
						val, ok := v.(int64)
						if !ok {
							return "", fmt.Errorf(`value %v is not a int64`, v)
						}
						newArr[i] = val
					}
					condition.Value = newArr
					r, err = buildInt64ArrayValueRule(condition)
				default:
					return "", fmt.Errorf(`type of value %v is not supported`, condition.Value)
				}
			default:
				return builder.String(), fmt.Errorf(`type of value %v is not supported`, condition.Value)
			}
		}
		if err != nil {
			return "", err
		}

		builder.WriteString(r)
		if i != len(matchRule.Conditions)-1 {
			if matchRule.Operator == "or" {
				builder.WriteString(" || ")
			} else {
				builder.WriteString(" && ")
			}
		}
	}
	return builder.String(), nil
}

func buildMatchSql(match Matcher) (string, error) {
	var builder strings.Builder
	if len(match.Rules) == 0 {
		return builder.String(), nil
	}

	var err error
	for i, rule := range match.Rules {
		var r string
		r, err = buildMatchRuleSql(rule)
		if err != nil {
			return builder.String(), err
		}
		builder.WriteString(fmt.Sprintf("(%s)", r))
		if i != len(match.Rules)-1 {
			if match.Operator == "or" {
				builder.WriteString(" || ")
			} else {
				builder.WriteString(" && ")
			}
		}
	}
	return builder.String(), nil
}

func buildStringValueRule(rule RuleCondition) (string, error) {
	s, ok := rule.Value.(string)
	if !ok {
		return "", fmt.Errorf(`value %v is not a string`, rule.Value)
	}
	switch rule.Operator {
	case "contains":
		return fmt.Sprintf("%s.%s contains '%s'", RULE_PREFIX, rule.Key, s), nil
	case "=":
		return fmt.Sprintf("%s.%s == '%s'", RULE_PREFIX, rule.Key, s), nil
	case "!=":
		return fmt.Sprintf("%s.%s != '%s'", RULE_PREFIX, rule.Key, s), nil
	default:
		return "", fmt.Errorf(`operator %s is not supported`, rule.Operator)
	}
}

func buildFloat64ValueRule(rule RuleCondition) (string, error) {
	val, ok := rule.Value.(float64)
	if !ok {
		return "", fmt.Errorf(`value %v is not a float64`, rule.Value)
	}
	intVal := int64(val)
	switch rule.Operator {
	case "=":
		return fmt.Sprintf("%s.%s == %d", RULE_PREFIX, rule.Key, intVal), nil
	case "!=":
		return fmt.Sprintf("%s.%s != %d", RULE_PREFIX, rule.Key, intVal), nil
	case ">", "<", ">=", "<=":
		return fmt.Sprintf("%s.%s %s %d", RULE_PREFIX, rule.Key, rule.Operator, intVal), nil
	default:
		return "", fmt.Errorf(`operator %s is not supported`, rule.Operator)
	}
}

func buildIntValueRule(rule RuleCondition) (string, error) {
	val, ok := rule.Value.(int)
	if !ok {
		return "", fmt.Errorf(`value %v is not a int`, rule.Value)
	}
	switch rule.Operator {
	case "=":
		return fmt.Sprintf("%s.%s == %d", RULE_PREFIX, rule.Key, val), nil
	case "!=":
		return fmt.Sprintf("%s.%s != %d", RULE_PREFIX, rule.Key, val), nil
	case ">", "<", ">=", "<=":
		return fmt.Sprintf("%s.%s %s %d", RULE_PREFIX, rule.Key, rule.Operator, val), nil
	default:
		return "", fmt.Errorf(`operator %s is not supported`, rule.Operator)
	}
}

func buildInt64ValueRule(rule RuleCondition) (string, error) {
	val, ok := rule.Value.(int64)
	if !ok {
		return "", fmt.Errorf(`value %v is not a int64`, rule.Value)
	}
	switch rule.Operator {
	case "=":
		return fmt.Sprintf("%s.%s == %d", RULE_PREFIX, rule.Key, val), nil
	case "!=":
		return fmt.Sprintf("%s.%s != %d", RULE_PREFIX, rule.Key, val), nil
	case ">", "<", ">=", "<=":
		return fmt.Sprintf("%s.%s %s %d", RULE_PREFIX, rule.Key, rule.Operator, val), nil
	default:
		return "", fmt.Errorf(`operator %s is not supported`, rule.Operator)
	}
}

func buildStringArrayValueRule(rule RuleCondition) (string, error) {
	arr, ok := rule.Value.([]string)
	if !ok {
		return "", fmt.Errorf(`value %v is not a string slice`, rule.Value)
	}
	var builder strings.Builder
	for i, v := range arr {
		builder.WriteString(fmt.Sprintf("'%s'", v))
		if i != len(arr)-1 {
			builder.WriteString(",")
		}
	}
	switch rule.Operator {
	case "in":
		return fmt.Sprintf("%s.%s in [%s]", RULE_PREFIX, rule.Key, builder.String()), nil
	case "not_in":
		return fmt.Sprintf("%s.%s not in [%s]", RULE_PREFIX, rule.Key, builder.String()), nil
	default:
		return "", fmt.Errorf(`operator %s is not supported`, rule.Operator)
	}
}

func buildFloat64ArrayValueRule(rule RuleCondition) (string, error) {
	arr, ok := rule.Value.([]float64)
	if !ok {
		return "", fmt.Errorf(`value %v is not a float64 slice`, rule.Value)
	}
	var builder strings.Builder
	for i, v := range arr {
		builder.WriteString(fmt.Sprintf("%d", int64(v)))
		if i != len(arr)-1 {
			builder.WriteString(",")
		}
	}

	switch rule.Operator {
	case "in":
		return fmt.Sprintf("%s.%s in [%s]", RULE_PREFIX, rule.Key, builder.String()), nil
	case "not_in":
		return fmt.Sprintf("%s.%s not in [%s]", RULE_PREFIX, rule.Key, builder.String()), nil
	default:
		return "", fmt.Errorf(`operator %s is not supported`, rule.Operator)
	}
}

func buildIntArrayValueRule(rule RuleCondition) (string, error) {
	arr, ok := rule.Value.([]int)
	if !ok {
		return "", fmt.Errorf(`value %v is not a int slice`, rule.Value)
	}
	var builder strings.Builder
	for i, v := range arr {
		builder.WriteString(fmt.Sprintf("%d", v))
		if i != len(arr)-1 {
			builder.WriteString(",")
		}
	}

	switch rule.Operator {
	case "in":
		return fmt.Sprintf("%s.%s in [%s]", RULE_PREFIX, rule.Key, builder.String()), nil
	case "not_in":
		return fmt.Sprintf("%s.%s not in [%s]", RULE_PREFIX, rule.Key, builder.String()), nil
	default:
		return "", fmt.Errorf(`operator %s is not supported`, rule.Operator)
	}
}

func buildInt64ArrayValueRule(rule RuleCondition) (string, error) {
	arr, ok := rule.Value.([]int64)
	if !ok {
		return "", fmt.Errorf(`value %v is not a int64 slice`, rule.Value)
	}
	var builder strings.Builder
	for i, v := range arr {
		builder.WriteString(fmt.Sprintf("%d", v))
		if i != len(arr)-1 {
			builder.WriteString(",")
		}
	}

	switch rule.Operator {
	case "in":
		return fmt.Sprintf("%s.%s in [%s]", RULE_PREFIX, rule.Key, builder.String()), nil
	case "not_in":
		return fmt.Sprintf("%s.%s not in [%s]", RULE_PREFIX, rule.Key, builder.String()), nil
	default:
		return "", fmt.Errorf(`operator %s is not supported`, rule.Operator)
	}
}
