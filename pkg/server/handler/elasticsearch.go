package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"git.woa.com/kmetis/starship-engine/api/v1/common"
	"git.woa.com/kmetis/starship-engine/pkg/elasticsearch"

	"github.com/gin-gonic/gin"
	"k8s.io/klog/v2"
)

// ESManager ES管理器接口
type ESManager interface {
	GetESCache() *elasticsearch.Cache
	GetESStats() elasticsearch.CacheStats
	ForceESSync() error
}

var EsManager ESManager

// SetESManager 设置ES管理器
func SetESManager(manager ESManager) {
	EsManager = manager
}

// GetESStats 获取ES同步统计信息
func GetESStats(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	stats := EsManager.GetESStats()
	c.<PERSON>(http.StatusOK, common.NewSuccess(stats))
}

// GetESClusters 获取ES缓存的集群信息
func GetESClusters(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	cache := EsManager.GetESCache()
	if cache == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES cache not available"))
		return
	}

	// 获取查询参数
	clusterType := c.Query("type")     // tke 或 eks
	region := c.Query("region")        // 地域
	keyword := c.Query("keyword")      // 搜索关键词
	pageStr := c.Query("page")         // 页码
	pageSizeStr := c.Query("pageSize") // 每页大小

	var clusters []*elasticsearch.ClusterInfo

	// 根据参数过滤集群
	if keyword != "" {
		clusters = cache.SearchClusters(keyword)
	} else if clusterType != "" {
		clusters = cache.GetClustersByType(clusterType)
	} else if region != "" {
		clusters = cache.GetClustersByRegion(region)
	} else {
		allClusters := cache.GetAllClusters()
		clusters = make([]*elasticsearch.ClusterInfo, 0, len(allClusters))
		for _, cluster := range allClusters {
			clusters = append(clusters, cluster)
		}
	}

	// 分页处理
	page := 1
	pageSize := 20
	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}
	if pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 1000 {
			pageSize = ps
		}
	}

	total := len(clusters)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		clusters = []*elasticsearch.ClusterInfo{}
	} else {
		if end > total {
			end = total
		}
		clusters = clusters[start:end]
	}

	result := map[string]interface{}{
		"clusters": clusters,
		"pagination": map[string]interface{}{
			"page":     page,
			"pageSize": pageSize,
			"total":    total,
		},
	}

	c.JSON(http.StatusOK, common.NewSuccess(result))
}

// GetESCluster 获取指定集群信息
func GetESCluster(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	cache := EsManager.GetESCache()
	if cache == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES cache not available"))
		return
	}

	clusterID := c.Param("clusterId")
	if clusterID == "" {
		c.JSON(http.StatusBadRequest, common.NewError(http.StatusBadRequest, "cluster ID is required"))
		return
	}

	cluster, exists := cache.GetCluster(clusterID)
	if !exists {
		c.JSON(http.StatusNotFound, common.NewError(http.StatusNotFound, "cluster not found"))
		return
	}

	c.JSON(http.StatusOK, common.NewSuccess(cluster))
}

// ForceESSync 强制执行ES同步
func ForceESSync(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	if err := EsManager.ForceESSync(); err != nil {
		klog.Errorf("Failed to force ES sync: %v", err)
		c.JSON(http.StatusInternalServerError, common.NewError(http.StatusInternalServerError, "Failed to trigger sync: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, common.NewSuccess(gin.H{
		"message": "ES sync triggered successfully",
	}))
}

// GetESClustersByType 根据类型获取集群统计
func GetESClustersByType(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	cache := EsManager.GetESCache()
	if cache == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES cache not available"))
		return
	}

	tkeClusters := cache.GetTKEClusters()
	eksClusters := cache.GetEKSClusters()

	// 按地域统计
	tkeRegions := make(map[string]int)
	eksRegions := make(map[string]int)

	for _, cluster := range tkeClusters {
		tkeRegions[cluster.Region]++
	}

	for _, cluster := range eksClusters {
		eksRegions[cluster.Region]++
	}

	result := map[string]interface{}{
		"tke": map[string]interface{}{
			"total":   len(tkeClusters),
			"regions": tkeRegions,
		},
		"eks": map[string]interface{}{
			"total":   len(eksClusters),
			"regions": eksRegions,
		},
		"total": len(tkeClusters) + len(eksClusters),
	}

	c.JSON(http.StatusOK, common.NewSuccess(result))
}

// GetESCacheInfo 获取缓存详细信息
func GetESCacheInfo(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	cache := EsManager.GetESCache()
	if cache == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES cache not available"))
		return
	}

	cacheInfo := cache.GetCacheInfo()
	c.JSON(http.StatusOK, common.NewSuccess(cacheInfo))
}

// GetESCacheChanges 获取缓存变化信息
func GetESCacheChanges(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	cache := EsManager.GetESCache()
	if cache == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES cache not available"))
		return
	}

	changes := cache.CompareWithPrevious()
	c.JSON(http.StatusOK, common.NewSuccess(changes))
}

// QueryESClusters 使用表达式查询集群
func QueryESClusters(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	cache := EsManager.GetESCache()
	if cache == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES cache not available"))
		return
	}

	var req elasticsearch.QueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, common.NewError(http.StatusBadRequest, "Invalid request: "+err.Error()))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 1000 {
		req.PageSize = 1000
	}

	response, err := cache.QueryClusters(&req)
	if err != nil {
		klog.Errorf("Query clusters failed: %v", err)
		c.JSON(http.StatusBadRequest, common.NewError(http.StatusBadRequest, "Query failed: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, common.NewSuccess(response))
}

// ExportESClusters 导出集群数据
func ExportESClusters(c *gin.Context) {
	if EsManager == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES manager not initialized"))
		return
	}

	cache := EsManager.GetESCache()
	if cache == nil {
		c.JSON(http.StatusServiceUnavailable, common.NewError(http.StatusServiceUnavailable, "ES cache not available"))
		return
	}

	var req elasticsearch.ExportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, common.NewError(http.StatusBadRequest, "Invalid request: "+err.Error()))
		return
	}

	// 检查是否请求文件流下载
	downloadParam := c.Query("download")
	isDownload := downloadParam == "true" || downloadParam == "1"

	response, err := cache.ExportClusters(&req)
	if err != nil {
		klog.Errorf("Export clusters failed: %v", err)
		c.JSON(http.StatusBadRequest, common.NewError(http.StatusBadRequest, "Export failed: "+err.Error()))
		return
	}

	// 如果请求文件流下载
	if isDownload {
		if req.Format == "csv" {
			c.Header("Content-Type", "text/csv; charset=utf-8")
			c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", response.Filename))
			c.String(http.StatusOK, response.Content)
		} else if req.Format == "json" {
			c.Header("Content-Type", "application/json; charset=utf-8")
			c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", response.Filename))
			jsonData, _ := json.Marshal(response.Data)
			c.String(http.StatusOK, string(jsonData))
		}
		return
	}

	// 默认返回JSON响应
	c.JSON(http.StatusOK, common.NewSuccess(response))
}
