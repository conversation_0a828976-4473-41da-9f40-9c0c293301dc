package elasticsearch

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/expr-lang/expr"
	"github.com/hashicorp/go-version"
	"k8s.io/klog/v2"
)

// Cache 集群数据内存缓存
type Cache struct {
	// 读写锁保护并发访问
	mu sync.RWMutex

	// 当前活跃的缓存数据
	activeTKEClusters map[string]*ClusterInfo
	activeEKSClusters map[string]*ClusterInfo

	// 备用缓存数据（用于原子切换）
	standbyTKEClusters map[string]*ClusterInfo
	standbyEKSClusters map[string]*ClusterInfo

	// 最后同步时间
	lastSyncTime time.Time

	// 同步状态
	syncing bool

	// 统计信息
	stats CacheStats
}

// CacheStats 缓存统计信息
type CacheStats struct {
	TotalClusters    int           `json:"totalClusters"`
	TKEClusters      int           `json:"tkeClusters"`
	EKSClusters      int           `json:"eksClusters"`
	LastSyncTime     time.Time     `json:"lastSyncTime"`
	LastSyncDuration time.Duration `json:"lastSyncDuration"`
	SyncCount        int64         `json:"syncCount"`
	ErrorCount       int64         `json:"errorCount"`
}

// NewCache 创建新的缓存实例
func NewCache() *Cache {
	return &Cache{
		activeTKEClusters:  make(map[string]*ClusterInfo),
		activeEKSClusters:  make(map[string]*ClusterInfo),
		standbyTKEClusters: make(map[string]*ClusterInfo),
		standbyEKSClusters: make(map[string]*ClusterInfo),
		stats:              CacheStats{},
	}
}

// UpdateClusters 全量更新集群数据（使用双缓存保证服务可用性）
func (c *Cache) UpdateClusters(clusters []*ClusterInfo) {
	// 为了兼容性，将单一列表转换为分类数据
	tkeList := make([]*ClusterInfo, 0)
	eksList := make([]*ClusterInfo, 0)

	for _, cluster := range clusters {
		if cluster.IsTKE() {
			tkeList = append(tkeList, cluster)
		} else if cluster.IsEKS() {
			eksList = append(eksList, cluster)
		}
	}

	c.UpdateClustersByType(tkeList, eksList)
}

// UpdateClustersByType 直接使用分类好的集群数据更新缓存
func (c *Cache) UpdateClustersByType(tkeClusters, eksClusters []*ClusterInfo) {
	startTime := time.Now()

	// 第一阶段：在备用缓存中构建新数据（不加锁，不影响服务）
	newTKEClusters := make(map[string]*ClusterInfo, len(tkeClusters))
	newEKSClusters := make(map[string]*ClusterInfo, len(eksClusters))

	tkeCount := 0
	eksCount := 0

	klog.Infof("Starting to build new cache with %d TKE + %d EKS clusters",
		len(tkeClusters), len(eksClusters))

	// 处理TKE集群
	for _, cluster := range tkeClusters {
		if cluster.ClusterID == "" {
			klog.Warningf("Skipping TKE cluster with empty ID")
			continue
		}
		newTKEClusters[cluster.ClusterID] = cluster
		tkeCount++
	}

	// 处理EKS集群
	for _, cluster := range eksClusters {
		if cluster.ClusterID == "" {
			klog.Warningf("Skipping EKS cluster with empty ID")
			continue
		}
		newEKSClusters[cluster.ClusterID] = cluster
		eksCount++
	}

	buildDuration := time.Since(startTime)
	klog.Infof("New cache built: %d total clusters (%d TKE, %d EKS) in %v",
		tkeCount+eksCount, tkeCount, eksCount, buildDuration)

	// 第二阶段：原子切换缓存（短暂加锁）
	c.mu.Lock()

	// 保存旧数据用于统计对比
	oldTKECount := len(c.activeTKEClusters)
	oldEKSCount := len(c.activeEKSClusters)

	// 原子切换：将新数据设为活跃数据，旧数据移到备用位置
	c.standbyTKEClusters = c.activeTKEClusters
	c.standbyEKSClusters = c.activeEKSClusters
	c.activeTKEClusters = newTKEClusters
	c.activeEKSClusters = newEKSClusters

	// 更新统计信息
	c.stats.TotalClusters = tkeCount + eksCount
	c.stats.TKEClusters = tkeCount
	c.stats.EKSClusters = eksCount
	c.stats.LastSyncTime = startTime
	c.stats.LastSyncDuration = time.Since(startTime)
	c.stats.SyncCount++

	c.lastSyncTime = startTime
	c.syncing = false

	c.mu.Unlock()

	// 记录变化统计
	tkeAdded := tkeCount - oldTKECount
	eksAdded := eksCount - oldEKSCount

	klog.Infof("Cache updated successfully: %d total clusters (%d TKE, %d EKS), changes: TKE %+d, EKS %+d, total duration: %v",
		c.stats.TotalClusters, c.stats.TKEClusters, c.stats.EKSClusters,
		tkeAdded, eksAdded, c.stats.LastSyncDuration)
}

// GetCluster 根据集群ID获取集群信息
func (c *Cache) GetCluster(clusterID string) (*ClusterInfo, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 先在活跃TKE缓存中查找
	if cluster, exists := c.activeTKEClusters[clusterID]; exists {
		return cluster, true
	}

	// 再在活跃EKS缓存中查找
	if cluster, exists := c.activeEKSClusters[clusterID]; exists {
		return cluster, true
	}

	return nil, false
}

// GetAllClusters 获取所有集群信息
func (c *Cache) GetAllClusters() map[string]*ClusterInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 合并活跃的TKE和EKS集群
	result := make(map[string]*ClusterInfo, len(c.activeTKEClusters)+len(c.activeEKSClusters))

	for k, v := range c.activeTKEClusters {
		result[k] = v
	}

	for k, v := range c.activeEKSClusters {
		result[k] = v
	}

	return result
}

// GetClustersByType 根据类型获取集群信息
func (c *Cache) GetClustersByType(clusterType string) []*ClusterInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var result []*ClusterInfo

	if clusterType == "tke" {
		for _, cluster := range c.activeTKEClusters {
			result = append(result, cluster)
		}
	} else if clusterType == "eks" {
		for _, cluster := range c.activeEKSClusters {
			result = append(result, cluster)
		}
	}

	return result
}

// GetClustersByRegion 根据地域获取集群信息
func (c *Cache) GetClustersByRegion(region string) []*ClusterInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var result []*ClusterInfo

	// 在活跃TKE集群中查找
	for _, cluster := range c.activeTKEClusters {
		if cluster.Region == region {
			result = append(result, cluster)
		}
	}

	// 在活跃EKS集群中查找
	for _, cluster := range c.activeEKSClusters {
		if cluster.Region == region {
			result = append(result, cluster)
		}
	}

	return result
}

// GetTKEClusters 获取所有TKE集群
func (c *Cache) GetTKEClusters() []*ClusterInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make([]*ClusterInfo, 0, len(c.activeTKEClusters))
	for _, cluster := range c.activeTKEClusters {
		result = append(result, cluster)
	}
	return result
}

// GetEKSClusters 获取所有EKS集群
func (c *Cache) GetEKSClusters() []*ClusterInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make([]*ClusterInfo, 0, len(c.activeEKSClusters))
	for _, cluster := range c.activeEKSClusters {
		result = append(result, cluster)
	}
	return result
}

// GetTKEClustersMap 获取TKE集群Map
func (c *Cache) GetTKEClustersMap() map[string]*ClusterInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make(map[string]*ClusterInfo, len(c.activeTKEClusters))
	for k, v := range c.activeTKEClusters {
		result[k] = v
	}
	return result
}

// GetEKSClustersMap 获取EKS集群Map
func (c *Cache) GetEKSClustersMap() map[string]*ClusterInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make(map[string]*ClusterInfo, len(c.activeEKSClusters))
	for k, v := range c.activeEKSClusters {
		result[k] = v
	}
	return result
}

// GetStats 获取缓存统计信息
func (c *Cache) GetStats() CacheStats {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return c.stats
}

// IsEmpty 检查缓存是否为空
func (c *Cache) IsEmpty() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return len(c.activeTKEClusters) == 0 && len(c.activeEKSClusters) == 0
}

// IsSyncing 检查是否正在同步
func (c *Cache) IsSyncing() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return c.syncing
}

// SetSyncing 设置同步状态
func (c *Cache) SetSyncing(syncing bool) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.syncing = syncing
}

// GetLastSyncTime 获取最后同步时间
func (c *Cache) GetLastSyncTime() time.Time {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return c.lastSyncTime
}

// IncrementErrorCount 增加错误计数
func (c *Cache) IncrementErrorCount() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.stats.ErrorCount++
	c.syncing = false
}

// SearchClusters 搜索集群（简单的字符串匹配）
func (c *Cache) SearchClusters(keyword string) []*ClusterInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var result []*ClusterInfo

	// 在活跃TKE集群中搜索
	for _, cluster := range c.activeTKEClusters {
		if matchCluster(cluster, keyword) {
			result = append(result, cluster)
		}
	}

	// 在活跃EKS集群中搜索
	for _, cluster := range c.activeEKSClusters {
		if matchCluster(cluster, keyword) {
			result = append(result, cluster)
		}
	}

	return result
}

// matchCluster 检查集群是否匹配关键词
func matchCluster(cluster *ClusterInfo, keyword string) bool {
	if keyword == "" {
		return true
	}

	// 简单的字符串包含匹配
	fields := []string{
		cluster.ClusterID,
		cluster.ClusterName,
		cluster.Region,
		cluster.ClusterType,
		cluster.ClusterStatus,
	}

	for _, field := range fields {
		if contains(field, keyword) {
			return true
		}
	}

	return false
}

// contains 检查字符串是否包含子串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(substr == "" ||
			strings.Contains(strings.ToLower(s), strings.ToLower(substr)))
}

// GetCacheInfo 获取缓存详细信息（用于监控和调试）
func (c *Cache) GetCacheInfo() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return map[string]interface{}{
		"active": map[string]interface{}{
			"tke_clusters": len(c.activeTKEClusters),
			"eks_clusters": len(c.activeEKSClusters),
			"total":        len(c.activeTKEClusters) + len(c.activeEKSClusters),
		},
		"standby": map[string]interface{}{
			"tke_clusters": len(c.standbyTKEClusters),
			"eks_clusters": len(c.standbyEKSClusters),
			"total":        len(c.standbyTKEClusters) + len(c.standbyEKSClusters),
		},
		"last_sync_time": c.lastSyncTime,
		"syncing":        c.syncing,
		"stats":          c.stats,
	}
}

// CompareWithPrevious 比较当前缓存与上一次的差异
func (c *Cache) CompareWithPrevious() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 计算新增的集群
	addedTKE := make([]string, 0)
	addedEKS := make([]string, 0)

	for id := range c.activeTKEClusters {
		if _, exists := c.standbyTKEClusters[id]; !exists {
			addedTKE = append(addedTKE, id)
		}
	}

	for id := range c.activeEKSClusters {
		if _, exists := c.standbyEKSClusters[id]; !exists {
			addedEKS = append(addedEKS, id)
		}
	}

	// 计算删除的集群
	removedTKE := make([]string, 0)
	removedEKS := make([]string, 0)

	for id := range c.standbyTKEClusters {
		if _, exists := c.activeTKEClusters[id]; !exists {
			removedTKE = append(removedTKE, id)
		}
	}

	for id := range c.standbyEKSClusters {
		if _, exists := c.activeEKSClusters[id]; !exists {
			removedEKS = append(removedEKS, id)
		}
	}

	return map[string]interface{}{
		"added": map[string]interface{}{
			"tke": addedTKE,
			"eks": addedEKS,
		},
		"removed": map[string]interface{}{
			"tke": removedTKE,
			"eks": removedEKS,
		},
		"summary": map[string]interface{}{
			"tke_added":   len(addedTKE),
			"tke_removed": len(removedTKE),
			"eks_added":   len(addedEKS),
			"eks_removed": len(removedEKS),
		},
	}
}

// ClearStandbyCache 清理备用缓存（释放内存）
func (c *Cache) ClearStandbyCache() {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 清理备用缓存以释放内存
	c.standbyTKEClusters = make(map[string]*ClusterInfo)
	c.standbyEKSClusters = make(map[string]*ClusterInfo)

	klog.Info("Standby cache cleared to free memory")
}

// QueryClusters 使用表达式查询集群
func (c *Cache) QueryClusters(req *QueryRequest) (*QueryResponse, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 获取所有集群
	allClusters := make([]*ClusterInfo, 0, len(c.activeTKEClusters)+len(c.activeEKSClusters))
	for _, cluster := range c.activeTKEClusters {
		allClusters = append(allClusters, cluster)
	}
	for _, cluster := range c.activeEKSClusters {
		allClusters = append(allClusters, cluster)
	}

	// 应用表达式过滤
	filteredClusters, err := c.filterClustersByExpression(allClusters, req.Expression)
	if err != nil {
		return nil, fmt.Errorf("filter expression error: %v", err)
	}

	// 排序
	if req.SortBy != "" {
		c.sortClusters(filteredClusters, req.SortBy, req.SortOrder)
	}

	// 分页
	total := len(filteredClusters)
	totalPages := (total + req.PageSize - 1) / req.PageSize

	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize
	if start >= total {
		filteredClusters = []*ClusterInfo{}
	} else {
		if end > total {
			end = total
		}
		filteredClusters = filteredClusters[start:end]
	}

	// 字段选择和转换
	data := make([]map[string]interface{}, len(filteredClusters))
	for i, cluster := range filteredClusters {
		data[i] = c.clusterToMap(cluster, req.Fields)
	}

	return &QueryResponse{
		Data:       data,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// ExportClusters 导出集群数据
func (c *Cache) ExportClusters(req *ExportRequest) (*ExportResponse, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 获取所有集群
	allClusters := make([]*ClusterInfo, 0, len(c.activeTKEClusters)+len(c.activeEKSClusters))
	for _, cluster := range c.activeTKEClusters {
		allClusters = append(allClusters, cluster)
	}
	for _, cluster := range c.activeEKSClusters {
		allClusters = append(allClusters, cluster)
	}

	// 应用表达式过滤
	filteredClusters, err := c.filterClustersByExpression(allClusters, req.Expression)
	if err != nil {
		return nil, fmt.Errorf("filter expression error: %v", err)
	}

	// 排序
	if req.SortBy != "" {
		c.sortClusters(filteredClusters, req.SortBy, req.SortOrder)
	}

	// 字段选择和转换
	data := make([]map[string]interface{}, len(filteredClusters))
	for i, cluster := range filteredClusters {
		data[i] = c.clusterToMap(cluster, req.Fields)
	}

	response := &ExportResponse{
		Format:   req.Format,
		Total:    len(filteredClusters),
		Fields:   req.Fields,
		Filename: fmt.Sprintf("clusters_%s.%s", time.Now().Format("20060102_150405"), req.Format),
	}

	if req.Format == "json" {
		response.Data = data
	} else if req.Format == "csv" {
		csvContent, err := c.convertToCSV(data, req.Fields)
		if err != nil {
			return nil, fmt.Errorf("convert to CSV error: %v", err)
		}
		response.Content = csvContent
	}

	return response, nil
}

// filterClustersByExpression 使用表达式过滤集群
func (c *Cache) filterClustersByExpression(clusters []*ClusterInfo, expression string) ([]*ClusterInfo, error) {
	if expression == "" {
		return clusters, nil
	}

	// 编译表达式，使用ClusterInfo作为环境，并添加版本比较和级别比较函数
	program, err := expr.Compile(expression,
		expr.Env(ClusterInfo{}),
		expr.Function("versionLt", c.versionLt),
		expr.Function("versionLte", c.versionLte),
		expr.Function("versionGt", c.versionGt),
		expr.Function("versionGte", c.versionGte),
		expr.Function("versionEq", c.versionEq),
		expr.Function("versionCompare", c.versionCompare),
		expr.Function("levelLt", c.levelLt),
		expr.Function("levelLte", c.levelLte),
		expr.Function("levelGt", c.levelGt),
		expr.Function("levelGte", c.levelGte),
		expr.Function("levelEq", c.levelEq),
		expr.Function("levelCompare", c.levelCompare),
		expr.Function("timeLt", c.timeLt),
		expr.Function("timeLte", c.timeLte),
		expr.Function("timeGt", c.timeGt),
		expr.Function("timeGte", c.timeGte),
		expr.Function("timeEq", c.timeEq),
		expr.Function("timeCompare", c.timeCompare),
	)
	if err != nil {
		return nil, fmt.Errorf("compile expression failed: %v", err)
	}

	filtered := make([]*ClusterInfo, 0)
	for _, cluster := range clusters {
		// 执行表达式
		result, err := expr.Run(program, cluster)
		if err != nil {
			klog.Warningf("Execute expression failed for cluster %s: %v", cluster.ClusterID, err)
			continue
		}

		// 检查结果是否为true
		if boolResult, ok := result.(bool); ok && boolResult {
			filtered = append(filtered, cluster)
		}
	}

	return filtered, nil
}

// sortClusters 对集群进行排序
func (c *Cache) sortClusters(clusters []*ClusterInfo, sortBy, sortOrder string) {
	if sortBy == "" {
		return
	}

	sort.Slice(clusters, func(i, j int) bool {
		val1 := c.getFieldValue(clusters[i], sortBy)
		val2 := c.getFieldValue(clusters[j], sortBy)

		result := c.compareValues(val1, val2)
		if sortOrder == "desc" {
			result = -result
		}
		return result < 0
	})
}

// getFieldValue 获取字段值
func (c *Cache) getFieldValue(cluster *ClusterInfo, fieldName string) interface{} {
	v := reflect.ValueOf(cluster).Elem()
	field := v.FieldByName(fieldName)
	if !field.IsValid() {
		return nil
	}
	return field.Interface()
}

// compareValues 比较两个值
func (c *Cache) compareValues(a, b interface{}) int {
	if a == nil && b == nil {
		return 0
	}
	if a == nil {
		return -1
	}
	if b == nil {
		return 1
	}

	switch va := a.(type) {
	case string:
		if vb, ok := b.(string); ok {
			return strings.Compare(va, vb)
		}
	case int:
		if vb, ok := b.(int); ok {
			return va - vb
		}
	case time.Time:
		if vb, ok := b.(time.Time); ok {
			if va.Before(vb) {
				return -1
			} else if va.After(vb) {
				return 1
			}
			return 0
		}
	}

	// 默认转换为字符串比较
	return strings.Compare(fmt.Sprintf("%v", a), fmt.Sprintf("%v", b))
}

// clusterToMap 将集群信息转换为map，支持字段选择和嵌套字段路径
func (c *Cache) clusterToMap(cluster *ClusterInfo, fields []string) map[string]interface{} {
	result := make(map[string]interface{})

	// 如果没有指定字段，返回所有字段
	if len(fields) == 0 {
		data, _ := json.Marshal(cluster)
		json.Unmarshal(data, &result)
		return result
	}

	// 使用反射获取指定字段
	v := reflect.ValueOf(cluster).Elem()
	t := reflect.TypeOf(cluster).Elem()

	for _, fieldName := range fields {
		// 检查是否是嵌套字段路径（包含方括号）
		if strings.Contains(fieldName, "[") && strings.Contains(fieldName, "]") {
			// 处理嵌套字段路径，如 ComponentVersion["kube-apiserver"]["tag"]
			value := c.extractNestedField(cluster, fieldName)
			result[fieldName] = value
			continue
		}

		found := false
		// 支持JSON标签名称和结构体字段名称
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			jsonTag := field.Tag.Get("json")

			var jsonName string
			if jsonTag != "" {
				jsonName = strings.Split(jsonTag, ",")[0]
			}

			// 匹配JSON标签名称或结构体字段名称
			if jsonName == fieldName || field.Name == fieldName {
				fieldValue := v.Field(i)
				if fieldValue.IsValid() && fieldValue.CanInterface() {
					// 使用请求的字段名称作为key
					result[fieldName] = fieldValue.Interface()
				}
				found = true
				break
			}
		}

		// 如果没找到字段，设置为空值
		if !found {
			result[fieldName] = nil
		}
	}

	return result
}

// extractNestedField 提取嵌套字段值，支持如 ComponentVersion["kube-apiserver"]["tag"] 的路径
func (c *Cache) extractNestedField(cluster *ClusterInfo, fieldPath string) interface{} {
	// 解析字段路径，如 ComponentVersion["kube-apiserver"]["tag"]
	parts := c.parseFieldPath(fieldPath)
	if len(parts) == 0 {
		return nil
	}

	// 获取根字段
	rootField := parts[0]
	var current interface{}

	// 使用反射获取根字段值
	v := reflect.ValueOf(cluster).Elem()
	t := reflect.TypeOf(cluster).Elem()

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		jsonTag := field.Tag.Get("json")

		var jsonName string
		if jsonTag != "" {
			jsonName = strings.Split(jsonTag, ",")[0]
		}

		if jsonName == rootField || field.Name == rootField {
			fieldValue := v.Field(i)
			if fieldValue.IsValid() && fieldValue.CanInterface() {
				current = fieldValue.Interface()
			}
			break
		}
	}

	if current == nil {
		return nil
	}

	// 遍历嵌套路径
	for i := 1; i < len(parts); i++ {
		key := parts[i]

		switch val := current.(type) {
		case map[string]interface{}:
			if nextVal, exists := val[key]; exists {
				current = nextVal
			} else {
				return nil
			}
		default:
			return nil
		}
	}

	return current
}

// parseFieldPath 解析字段路径，如 ComponentVersion["kube-apiserver"]["tag"] -> ["ComponentVersion", "kube-apiserver", "tag"]
func (c *Cache) parseFieldPath(fieldPath string) []string {
	var parts []string

	// 找到第一个方括号之前的部分作为根字段
	firstBracket := strings.Index(fieldPath, "[")
	if firstBracket == -1 {
		return []string{fieldPath}
	}

	rootField := fieldPath[:firstBracket]
	parts = append(parts, rootField)

	// 解析方括号中的键
	remaining := fieldPath[firstBracket:]

	for {
		start := strings.Index(remaining, "[")
		if start == -1 {
			break
		}

		end := strings.Index(remaining[start:], "]")
		if end == -1 {
			break
		}

		// 提取方括号中的内容
		keyWithQuotes := remaining[start+1 : start+end]

		// 移除引号
		key := strings.Trim(keyWithQuotes, "\"'")
		parts = append(parts, key)

		// 继续处理剩余部分
		remaining = remaining[start+end+1:]
	}

	return parts
}

// convertToCSV 将数据转换为CSV格式
func (c *Cache) convertToCSV(data []map[string]interface{}, fields []string) (string, error) {
	if len(data) == 0 {
		return "", nil
	}

	var csvData [][]string

	// 添加表头
	csvData = append(csvData, fields)

	// 添加数据行
	for _, row := range data {
		record := make([]string, len(fields))
		for i, field := range fields {
			if value, exists := row[field]; exists && value != nil {
				// 处理复杂类型
				switch v := value.(type) {
				case map[string]interface{}, []interface{}:
					jsonBytes, _ := json.Marshal(v)
					record[i] = string(jsonBytes)
				case time.Time:
					record[i] = v.Format("2006-01-02 15:04:05")
				default:
					record[i] = fmt.Sprintf("%v", v)
				}
			} else {
				record[i] = ""
			}
		}
		csvData = append(csvData, record)
	}

	// 转换为CSV字符串
	var result strings.Builder
	writer := csv.NewWriter(&result)

	for _, record := range csvData {
		if err := writer.Write(record); err != nil {
			return "", err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return "", err
	}

	return result.String(), nil
}

// 版本比较函数，用于go-expr表达式中

// versionLt 版本小于比较
func (c *Cache) versionLt(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("versionLt requires exactly 2 parameters")
	}
	return c.compareVersions(params[0], params[1]) < 0, nil
}

// versionLte 版本小于等于比较
func (c *Cache) versionLte(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("versionLte requires exactly 2 parameters")
	}
	return c.compareVersions(params[0], params[1]) <= 0, nil
}

// versionGt 版本大于比较
func (c *Cache) versionGt(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("versionGt requires exactly 2 parameters")
	}
	return c.compareVersions(params[0], params[1]) > 0, nil
}

// versionGte 版本大于等于比较
func (c *Cache) versionGte(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("versionGte requires exactly 2 parameters")
	}
	return c.compareVersions(params[0], params[1]) >= 0, nil
}

// versionEq 版本等于比较
func (c *Cache) versionEq(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("versionEq requires exactly 2 parameters")
	}
	return c.compareVersions(params[0], params[1]) == 0, nil
}

// versionCompare 版本比较，返回 -1, 0, 1
func (c *Cache) versionCompare(params ...any) (any, error) {
	if len(params) != 2 {
		return 0, fmt.Errorf("versionCompare requires exactly 2 parameters")
	}
	return c.compareVersions(params[0], params[1]), nil
}

// compareVersions 内部版本比较实现
func (c *Cache) compareVersions(v1, v2 interface{}) int {
	str1 := c.extractVersionString(v1)
	str2 := c.extractVersionString(v2)

	if str1 == "" && str2 == "" {
		return 0
	}
	if str1 == "" {
		return -1
	}
	if str2 == "" {
		return 1
	}

	// 清理版本字符串，移除前缀v
	str1 = strings.TrimPrefix(str1, "v")
	str2 = strings.TrimPrefix(str2, "v")

	// 解析版本
	version1, err1 := version.NewVersion(str1)
	version2, err2 := version.NewVersion(str2)

	if err1 != nil && err2 != nil {
		// 如果都解析失败，按字符串比较
		return strings.Compare(str1, str2)
	}
	if err1 != nil {
		// v1解析失败，认为v1更小
		return -1
	}
	if err2 != nil {
		// v2解析失败，认为v1更大
		return 1
	}

	return version1.Compare(version2)
}

// extractVersionString 从各种类型中提取版本字符串
func (c *Cache) extractVersionString(v interface{}) string {
	if v == nil {
		return ""
	}

	switch val := v.(type) {
	case string:
		return val
	case map[string]interface{}:
		// 尝试从map中提取版本信息
		if tag, ok := val["tag"].(string); ok {
			return tag
		}
		if version, ok := val["version"].(string); ok {
			return version
		}
		// 如果map只有一个值，尝试使用它
		if len(val) == 1 {
			for _, value := range val {
				if str, ok := value.(string); ok {
					return str
				}
			}
		}
	default:
		return fmt.Sprintf("%v", v)
	}

	return ""
}

// 级别比较函数，用于ClusterLevel字段比较

// levelLt 级别小于比较
func (c *Cache) levelLt(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("levelLt requires exactly 2 parameters")
	}
	return c.compareLevels(params[0], params[1]) < 0, nil
}

// levelLte 级别小于等于比较
func (c *Cache) levelLte(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("levelLte requires exactly 2 parameters")
	}
	return c.compareLevels(params[0], params[1]) <= 0, nil
}

// levelGt 级别大于比较
func (c *Cache) levelGt(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("levelGt requires exactly 2 parameters")
	}
	return c.compareLevels(params[0], params[1]) > 0, nil
}

// levelGte 级别大于等于比较
func (c *Cache) levelGte(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("levelGte requires exactly 2 parameters")
	}
	return c.compareLevels(params[0], params[1]) >= 0, nil
}

// levelEq 级别等于比较
func (c *Cache) levelEq(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("levelEq requires exactly 2 parameters")
	}
	return c.compareLevels(params[0], params[1]) == 0, nil
}

// levelCompare 级别比较，返回 -1, 0, 1
func (c *Cache) levelCompare(params ...any) (any, error) {
	if len(params) != 2 {
		return 0, fmt.Errorf("levelCompare requires exactly 2 parameters")
	}
	return c.compareLevels(params[0], params[1]), nil
}

// compareLevels 内部级别比较实现
func (c *Cache) compareLevels(l1, l2 interface{}) int {
	str1 := c.extractLevelString(l1)
	str2 := c.extractLevelString(l2)

	if str1 == "" && str2 == "" {
		return 0
	}
	if str1 == "" {
		return -1
	}
	if str2 == "" {
		return 1
	}

	// 解析级别数值
	num1 := c.parseLevelNumber(str1)
	num2 := c.parseLevelNumber(str2)

	if num1 < num2 {
		return -1
	} else if num1 > num2 {
		return 1
	}
	return 0
}

// extractLevelString 从各种类型中提取级别字符串
func (c *Cache) extractLevelString(l interface{}) string {
	if l == nil {
		return ""
	}

	switch val := l.(type) {
	case string:
		return val
	default:
		return fmt.Sprintf("%v", l)
	}
}

// parseLevelNumber 解析级别字符串中的数值
func (c *Cache) parseLevelNumber(level string) int {
	// 移除L前缀并转换为数字
	if len(level) > 1 && (level[0] == 'L' || level[0] == 'l') {
		numStr := level[1:]
		if num, err := strconv.Atoi(numStr); err == nil {
			return num
		}
	}

	// 如果不是L开头，尝试直接解析数字
	if num, err := strconv.Atoi(level); err == nil {
		return num
	}

	// 解析失败，返回0
	return 0
}

// 时间比较函数，用于CreatedAt、UpdatedAt等时间字段比较

// timeLt 时间小于比较
func (c *Cache) timeLt(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("timeLt requires exactly 2 parameters")
	}
	return c.compareTimes(params[0], params[1]) < 0, nil
}

// timeLte 时间小于等于比较
func (c *Cache) timeLte(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("timeLte requires exactly 2 parameters")
	}
	return c.compareTimes(params[0], params[1]) <= 0, nil
}

// timeGt 时间大于比较
func (c *Cache) timeGt(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("timeGt requires exactly 2 parameters")
	}
	return c.compareTimes(params[0], params[1]) > 0, nil
}

// timeGte 时间大于等于比较
func (c *Cache) timeGte(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("timeGte requires exactly 2 parameters")
	}
	return c.compareTimes(params[0], params[1]) >= 0, nil
}

// timeEq 时间等于比较
func (c *Cache) timeEq(params ...any) (any, error) {
	if len(params) != 2 {
		return false, fmt.Errorf("timeEq requires exactly 2 parameters")
	}
	return c.compareTimes(params[0], params[1]) == 0, nil
}

// timeCompare 时间比较，返回 -1, 0, 1
func (c *Cache) timeCompare(params ...any) (any, error) {
	if len(params) != 2 {
		return 0, fmt.Errorf("timeCompare requires exactly 2 parameters")
	}
	return c.compareTimes(params[0], params[1]), nil
}

// compareTimes 内部时间比较实现
func (c *Cache) compareTimes(t1, t2 interface{}) int {
	time1 := c.parseTime(t1)
	time2 := c.parseTime(t2)

	if time1.IsZero() && time2.IsZero() {
		return 0
	}
	if time1.IsZero() {
		return -1
	}
	if time2.IsZero() {
		return 1
	}

	if time1.Before(time2) {
		return -1
	} else if time1.After(time2) {
		return 1
	}
	return 0
}

// parseTime 解析时间，支持多种格式
func (c *Cache) parseTime(t interface{}) time.Time {
	if t == nil {
		return time.Time{}
	}

	switch val := t.(type) {
	case time.Time:
		return val
	case string:
		// 支持多种时间格式
		formats := []string{
			time.RFC3339,                    // "2006-01-02T15:04:05Z07:00"
			time.RFC3339Nano,                // "2006-01-02T15:04:05.999999999Z07:00"
			"2006-01-02T15:04:05Z",          // UTC格式
			"2006-01-02T15:04:05.999Z",      // 带毫秒的UTC格式
			"2006-01-02 15:04:05",           // 常见格式
			"2006-01-02",                    // 日期格式
			"2025-07-14T08:31:41.73701899Z", // 您提供的格式示例
		}

		for _, format := range formats {
			if parsed, err := time.Parse(format, val); err == nil {
				return parsed
			}
		}

		// 如果都解析失败，尝试自动检测格式
		if parsed, err := time.Parse(time.RFC3339Nano, val); err == nil {
			return parsed
		}
	}

	return time.Time{}
}
