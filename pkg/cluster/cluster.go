package cluster

import (
	"fmt"
	"strconv"

	"git.woa.com/kmetis/starship-engine/pkg/cache"
	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/elasticsearch"
	"git.woa.com/kmetis/starship-engine/pkg/util"
	"git.woa.com/tke/tops/cmd/misaka/common/cluster"
	"git.woa.com/tke/tops/conf"
	"git.woa.com/tke/tops/pkg/util/region"

	"k8s.io/klog/v2"
)

const (
	TKE = "tke"
	EKS = "eks"

	EKS_APP_ID = "eks.tke.cloud.tencent.com/appid"
	EKS_METAID = "eks.tke.cloud.tencent.com/metacluster"
	EKS_UIN    = "eks.tke.cloud.tencent.com/owner-uin"
	EKS_SUBUIN = "eks.tke.cloud.tencent.com/creator-uin"

	CauthServer = "http://logical.server.console.tencentyun.com:8080/interface.php"
)

type ClusterTable struct {
	config *conf.Config
}

func NewClustersTable(config *conf.Config) *ClusterTable {
	cluster.Config = config
	return &ClusterTable{
		config: config,
	}
}

func (c *ClusterTable) GetCluster(cluster *db.TaskClusterInfo) error {
	var err error
	err = c.buildCluster(cluster)
	if err != nil {
		return fmt.Errorf("get cluster (%s) info, error! %s", cluster.ClusterId, err.Error())
	}
	klog.Info("cluster info :", *cluster)

	return nil
}

// CacheError 缓存错误类型
type CacheError struct {
	Type    string // "unavailable" 或 "not_found"
	Message string
}

func (e *CacheError) Error() string {
	return e.Message
}

// getClusterInfoFromCache 从ES缓存获取集群信息
func (c *ClusterTable) getClusterInfoFromCache(clusterID string) (*elasticsearch.ClusterInfo, error) {
	// 获取全局缓存管理器
	cacheManager := cache.GetGlobalManager()

	// 检查ES缓存是否可用
	esCache := cacheManager.GetESCache()
	if esCache == nil {
		return nil, &CacheError{
			Type:    "unavailable",
			Message: "ES cache not available",
		}
	}

	// 从缓存中获取集群信息
	clusterInfo, exists := cacheManager.GetClusterFromCache(clusterID)
	if !exists {
		return nil, &CacheError{
			Type:    "not_found",
			Message: fmt.Sprintf("cluster %s not found in cache", clusterID),
		}
	}

	return clusterInfo, nil
}

// buildClusterFromCache 基于ES缓存构建集群信息
func (c *ClusterTable) buildClusterFromCache(task *db.TaskClusterInfo) error {
	clusterInfo, err := c.getClusterInfoFromCache(task.ClusterId)
	if err != nil {
		return err
	}

	// 从缓存数据填充任务信息
	task.Region = clusterInfo.Region
	task.Type = clusterInfo.ClusterType
	task.UIN = clusterInfo.UIN
	task.SubUIN = clusterInfo.SubAccountUIN
	task.MetaID = clusterInfo.MetaClusterID

	// 解析AppID
	if clusterInfo.AppID != "" {
		appID, err := strconv.ParseUint(clusterInfo.AppID, 10, 64)
		if err != nil {
			klog.Warningf("Failed to parse AppID %s for cluster %s: %v", clusterInfo.AppID, task.ClusterId, err)
			// 继续执行，不返回错误
		} else {
			task.AppID = appID
		}
	}

	// 如果MetaID为空，使用默认值
	if task.MetaID == "" && task.Type == TKE {
		task.MetaID = c.config.TKE.DefaultMetaCluster[task.Region]
	}

	klog.V(2).Infof("Successfully built cluster info from cache for %s: region=%s, type=%s, appID=%d",
		task.ClusterId, task.Region, task.Type, task.AppID)

	return nil
}

func (c *ClusterTable) buildCluster(task *db.TaskClusterInfo) (err error) {
	defer func() {
		if r := recover(); r != nil {
			klog.Errorf("Recovered from panic: %v", r)
			err = fmt.Errorf("failed to get cluster info. %v", r)
		}
	}()

	// 优先尝试从ES缓存获取集群信息
	if err := c.buildClusterFromCache(task); err != nil {
		// 检查错误类型，决定是否降级
		if cacheErr, ok := err.(*CacheError); ok {
			switch cacheErr.Type {
			case "unavailable":
				// ES缓存完全不可用时，降级到原有方法
				klog.V(2).Infof("ES cache unavailable for cluster %s, falling back to legacy method: %v", task.ClusterId, err)
				return c.buildClusterLegacy(task)
				//case "not_found":
				//	// ES缓存可用但找不到集群时，直接返回错误
				//	klog.Errorf("Cluster %s not found in ES cache, not falling back", task.ClusterId)
				//	return fmt.Errorf("cluster %s not found in ES cache", task.ClusterId)
			}
		}

		// 其他类型的错误，也尝试降级
		klog.V(2).Infof("Failed to build cluster from cache for %s: %v, falling back to legacy method", task.ClusterId, err)
		return c.buildClusterLegacy(task)
	}

	return nil
}

// buildClusterLegacy 原有的集群信息构建方法（保持向后兼容）
func (c *ClusterTable) buildClusterLegacy(task *db.TaskClusterInfo) error {
	rg, err := util.GetRegionAliasFromClusterID(task.ClusterId)
	if err != nil {
		return fmt.Errorf("GetRegionAliasFromClusterID(%s) error!%s", task.ClusterId, err.Error())
	}
	task.Region = rg
	clusterType, err := util.GetClusterTypeById(task.ClusterId)
	if err != nil {
		return fmt.Errorf("GetClusterTypeById(%s) error!%s", task.ClusterId, err.Error())
	}

	switch clusterType {
	case TKE:
		tkeCluster := cluster.GetTKECluster(region.ShortToLong(task.Region), task.ClusterId)
		if tkeCluster == nil {
			return fmt.Errorf("cluster %s not found", task.ClusterId)
		}
		uin, err := util.GetUinByAppId(CauthServer, tkeCluster.AppId)
		if err != nil {
			return err
		}
		if tkeCluster.MetaClusterID == "" {
			// 如果没有metaClusterID，则使用默认的metaClusterID
			task.MetaID = c.config.TKE.DefaultMetaCluster[task.Region]
		} else {
			task.MetaID = tkeCluster.MetaClusterID
		}
		task.AppID = tkeCluster.AppId
		task.Type = TKE
		task.UIN = uin
		task.SubUIN = uin
	case EKS:
		var appId, metaId, uin, subUin string
		var ok bool
		eksCluster, err := cluster.GetEKSCluster(region.ShortToLong(task.Region), task.ClusterId)
		if err != nil || eksCluster == nil {
			return fmt.Errorf("GetEKSCluster(%s) error!%s", task.ClusterId, err.Error())
		}
		appId = eksCluster.Spec.TenantID
		appIdConv, err := strconv.ParseUint(appId, 10, 64)
		if err != nil {
			return fmt.Errorf("converted uint64 value: convert appId %d error!%s", appIdConv, err.Error())
		}
		if metaId, ok = eksCluster.GetAnnotations()[EKS_METAID]; !ok {
			return fmt.Errorf("GetEKSCluster(%s) metaId error!%s", task.ClusterId, err.Error())
		}
		if uin, ok = eksCluster.GetAnnotations()[EKS_UIN]; !ok {
			return fmt.Errorf("GetEKSCluster(%s) metaId error!%s", task.ClusterId, err.Error())
		}
		if uin == "" {
			uin, err = util.GetUinByAppId(CauthServer, appIdConv)
			if err != nil {
				return err
			}
		}

		if subUin, ok = eksCluster.GetAnnotations()[EKS_SUBUIN]; !ok {
			subUin = uin
		}

		task.AppID = appIdConv
		task.Type = EKS
		task.MetaID = metaId
		task.UIN = uin
		task.SubUIN = subUin
	default:
		return fmt.Errorf("%s no cluster type", task.ClusterId)
	}

	return nil
}
