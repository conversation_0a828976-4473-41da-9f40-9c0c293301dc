/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	"git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/scheme"
	rest "k8s.io/client-go/rest"
)

type AutopilotV1beta1Interface interface {
	RESTClient() rest.Interface
	ClusterScalersGetter
	ScalerTasksGetter
}

// AutopilotV1beta1Client is used to interact with features provided by the autopilot.k8s.io group.
type AutopilotV1beta1Client struct {
	restClient rest.Interface
}

func (c *AutopilotV1beta1Client) ClusterScalers(namespace string) ClusterScalerInterface {
	return newClusterScalers(c, namespace)
}

func (c *AutopilotV1beta1Client) ScalerTasks(namespace string) ScalerTaskInterface {
	return newScalerTasks(c, namespace)
}

// NewForConfig creates a new AutopilotV1beta1Client for the given config.
func NewForConfig(c *rest.Config) (*AutopilotV1beta1Client, error) {
	config := *c
	if err := setConfigDefaults(&config); err != nil {
		return nil, err
	}
	client, err := rest.RESTClientFor(&config)
	if err != nil {
		return nil, err
	}
	return &AutopilotV1beta1Client{client}, nil
}

// NewForConfigOrDie creates a new AutopilotV1beta1Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *AutopilotV1beta1Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new AutopilotV1beta1Client for the given RESTClient.
func New(c rest.Interface) *AutopilotV1beta1Client {
	return &AutopilotV1beta1Client{c}
}

func setConfigDefaults(config *rest.Config) error {
	gv := v1beta1.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = scheme.Codecs.WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}

	return nil
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *AutopilotV1beta1Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
