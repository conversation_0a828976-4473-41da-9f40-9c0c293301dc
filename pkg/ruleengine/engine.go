package ruleengine

import (
	"errors"
	"fmt"

	"github.com/expr-lang/expr"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/pkg/elasticsearch"
	"git.woa.com/kmetis/starship-engine/pkg/ruleengine/hook"
)

const (
	// 默认subbatch间隔10分钟
	DefaultSubBatchInterval = 600
	// 默认batch间隔1小时
	DefaultBatchInterval = 3600
)

var (
	// 保留的Env Key
	ForbiddenEnvKey = map[string]bool{
		"cluster": true,
	}
)

// CheckFunc is a function that checks a condition.
type CheckFunc func(args ...interface{}) (bool, error)

type RuleEngine struct {
	// CustomFuncMap is a map of custom functions that can be used in the rule.
	CustomFuncMap map[string]interface{}
	// BaseEnv is a map of base environment variables that can be used in the rule.
	BaseEnv map[string]interface{}
}

// NewRuleEngine creates a new RuleEngine.
func NewRuleEngine() *RuleEngine {
	engine := &RuleEngine{
		CustomFuncMap: map[string]interface{}{},
		BaseEnv:       map[string]interface{}{},
	}
	engine.loadBuiltinUserFunc()
	return engine
}

func (e *RuleEngine) loadBuiltinUserFunc() {
	// 预加载用户自定义函数
	if e.CustomFuncMap == nil {
		e.CustomFuncMap = map[string]interface{}{}
	}
	for k, v := range GetUserFuncs() {
		if _, ok := ForbiddenEnvKey[k]; !ok {
			e.CustomFuncMap[k] = v
		}
	}
}

func (e *RuleEngine) buildCompileEnv() map[string]interface{} {
	env := map[string]interface{}{}
	for k, v := range e.BaseEnv {
		env[k] = v
	}
	for k, v := range e.CustomFuncMap {
		env[k] = v
	}
	return env
}

// RunBatch applies the given rule to a list of clusters and returns the matching ones.
func (e *RuleEngine) RunBatch(config []BatchConfig, pool []*elasticsearch.ClusterInfo) ([]*ClusterBatch, error) {
	if len(pool) == 0 {
		return []*ClusterBatch{}, nil
	}
	if len(config) == 0 {
		return nil, fmt.Errorf("config is empty")
	}

	clusterBatches := make([]*ClusterBatch, 0)
	var batchId int64 = 0
	var subBatchId int64 = 0
	for i, batch := range config {
		if len(batch.SubBatch) == 0 {
			return nil, fmt.Errorf("batch %d subBatch is empty", batch.BatchId)
		}

		// 处理batch级别过滤
		var batchPool []*elasticsearch.ClusterInfo
		if len(batch.Rule.Rules) != 0 {
			batchSql, err := buildMatchSql(batch.Rule)
			if err != nil {
				return nil, err
			}
			// 应用batch级别过滤出该batch级别的数据池
			batchPool, err = e.RunRule(pool, batchSql)
			if err != nil {
				return nil, err
			}
		} else {
			batchPool = pool
		}
		if len(batchPool) == 0 {
			continue
		}

		// 当config中batchId变化时，subBatchId重置为0
		if i == 0 || config[i-1].BatchId != batch.BatchId {
			batchId++
			subBatchId = 0
		}

		// 处理subbatch级别过滤
		for j, subBatch := range batch.SubBatch {
			subBatchSql, err := buildMatchSql(subBatch.Match)
			if err != nil {
				return nil, err
			}
			fmt.Printf("batchId: %d, subBatchId: %d, subBatchSql: %s\n", batch.BatchId, subBatch.SubBatchId, subBatchSql)
			//应用subbatch过滤
			var subBatchPool []*elasticsearch.ClusterInfo
			subBatchPool, err = e.RunRule(batchPool, subBatchSql)
			if err != nil {
				return nil, err
			}
			// 处理postCall
			// 获取postCall的func
			for _, postCall := range subBatch.PostCall {
				if postCall.CheckPoint == "BATCH" {
					postCallHook, err := hook.GetEngineHook(postCall.Name)
					if err != nil {
						return nil, fmt.Errorf("postCall func %s not found", postCall.Name)
					}
					if err = postCallHook.Execute(&subBatchPool, postCall.Name, postCall.Params); err != nil {
						return nil, err
					}
				}
			}

			if len(subBatchPool) == 0 {
				continue
			}
			subBatchId++

			//更新batchPool
			if err = RemoveMatchedFromPool(&subBatchPool, &batchPool); err != nil {
				return nil, err
			}
			// 更新pool
			if err = RemoveMatchedFromPool(&subBatchPool, &pool); err != nil {
				return nil, err
			}

			interval := subBatch.Interval
			if interval <= 0 {
				if j >= len(batch.SubBatch)-1 {
					// 如果是最后一个subbatch，interval默认为1
					interval = DefaultBatchInterval
				} else {
					interval = DefaultSubBatchInterval
				}
			}
			clusterBatches = append(clusterBatches, &ClusterBatch{
				BatchId:    batchId,
				SubBatchId: subBatchId,
				Clusters:   subBatchPool,
				BatchName:  subBatch.SubBatchName,
			})
		}
	}
	return clusterBatches, nil
}

func (e *RuleEngine) RunRule(clusters []*elasticsearch.ClusterInfo, rule string) ([]*elasticsearch.ClusterInfo, error) {
	if rule == "" {
		return nil, errors.New("rule cannot be empty")
	}

	program, err := expr.Compile(rule, expr.AsBool())
	if err != nil {
		klog.Errorf("failed to compile rule '%s': %v", rule, err)
		return nil, err
	}

	env := e.buildCompileEnv()
	matchedClusters := make([]*elasticsearch.ClusterInfo, 0)
	for _, cluster := range clusters {
		env["cluster"] = cluster
		output, _ := expr.Run(program, env)
		//if err != nil {
		//	return nil, fmt.Errorf("failed to run rule for cluster %s: %v", cluster.ClusterID, err)
		//}
		if matched, ok := output.(bool); ok && matched {
			matchedClusters = append(matchedClusters, cluster)
		}
	}
	return matchedClusters, nil
}

func (e *RuleEngine) RunFilter(pool []*elasticsearch.ClusterInfo, config FilterConfig) ([]*elasticsearch.ClusterInfo, error) {
	if len(config.Rules) == 0 {
		return nil, fmt.Errorf("rules is empty")
	}
	if len(pool) == 0 {
		return nil, fmt.Errorf("pool is empty")
	}
	rule, err := buildMatchSql(Matcher(config))
	if err != nil {
		return nil, err
	}

	matches, err := e.RunRule(pool, rule)
	return matches, err
}

func RemoveMatchedFromPool(matchedPool *[]*elasticsearch.ClusterInfo, targetPool *[]*elasticsearch.ClusterInfo) error {
	if len(*matchedPool) == 0 {
		return nil
	}

	matchedPoolKeyMap := make(map[string]bool)
	for _, cluster := range *matchedPool {
		matchedPoolKeyMap[cluster.ClusterID] = true
	}

	newTargetPool := make([]*elasticsearch.ClusterInfo, 0)
	for _, cluster := range *targetPool {
		if _, ok := matchedPoolKeyMap[cluster.ClusterID]; !ok {
			newTargetPool = append(newTargetPool, cluster)
		}
	}
	if len(newTargetPool) != len(*targetPool) {
		*targetPool = newTargetPool
	}
	return nil
}
