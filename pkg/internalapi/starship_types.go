package internalapi

import "git.woa.com/tke/tops/pkg/coredns/pb"

type ErrorCode string

type RequestV3Common struct {
	RequestID     string `json:"RequestId"`
	Action        string `json:"Action"`
	AppID         uint64 `json:"AppId,omitempty"`
	UIN           string `json:"Uin,omitempty"`
	SubAccountUIN string `json:"SubAccountUin,omitempty"`
	ClientIP      string `json:"ClientIp,omitempty"`
	APIModule     string `json:"ApiModule,omitempty"`
	Region        string `json:"Region,omitempty"`
	Token         string `json:"Token,omitempty"`
	CamContext    string `json:"CamContext,omitempty"`
	Version       string `json:"Version,omitempty"`
	RequestSource string `json:"RequestSource,omitempty"`
	Language      string `json:"Language,omitempty"`
}

type UpdateAppReq struct {
	RequestV3Common
	ClusterId      string     `json:"ClusterId"`
	AppName        string     `json:"AppName"`
	AppVersion     AppVersion `json:"AppVersion"`
	ProductName    string     `json:"ProductName"`
	Values         Values     `json:"Values"`
	AppStrategy    string     `json:"AppStrategy"`
	AppAction      string     `json:"AppAction"`
	UpdateStrategy string     `json:"UpdateStrategy"`
}

type RollbackAppReq struct {
	RequestV3Common
	TaskId int64 `json:"TaskId"`
}

type AppVersion struct {
	AppVersion    string `json:"AppVersion"`
	PluginVersion string `json:"PluginVersion"`
	ImageTag      string `json:"ImageTag"`
}

type Values struct {
	Env           []map[string]string    `json:"Env"`
	ExtendInfo    string                 `json:"ExtendInfo"`
	Token         string                 `json:"Token"`
	Addon         map[string]interface{} `json:"Addon"`
	User          string                 `json:"User"`
	ChangeId      string                 `json:"ChangeId"`
	BatchId       int                    `json:"BatchId"`
	TaskName      string                 `json:"TaskName"`
	MetaClusterId string                 `json:"MetaClusterId"`
}

type UpdateAppResponseImp struct {
	RequestId string                `json:"RequestId"`
	TaskId    string                `json:"TaskId"`
	Error     ResponseV3ErrorDetail `json:"Error,omitempty"`
}

type UpdateAppResponse struct {
	Response UpdateAppResponseImp `json:"Response"`
}

type DescribeAppUpgradingProgressReq struct {
	RequestV3Common
	AppName   string `json:"AppName"`
	ClusterId string `json:"ClusterId"`
	ChangeId  string `json:"ChangeId"`
	AppAction string `json:"AppAction"`
}

type DescribeAppUpgradingProgressResponse struct {
	Response DescribeAppUpgradingProgressResponseImp `json:"Response"`
}

type DescribeAppUpgradingProgressResponseImp struct {
	RequestId string                `json:"RequestId"`
	Task      pb.DescribeTaskReply  `json:"Task"`
	Error     ResponseV3ErrorDetail `json:"Error,omitempty"`
}

type ResponseV3ErrorDetail struct {
	Code    ErrorCode `json:"Code,omitempty"`
	Message string    `json:"Message,omitempty"`
}
