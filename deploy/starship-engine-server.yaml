apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: starship-engine-server
  name: starship-engine-server
  namespace: starship
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: starship-engine-server
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: starship-engine-server
    spec:
      containers:
        - command:
            - starship-engine
          image: mirrors.tencent.com/starship/starship-engine-serve:v1.0.1
          imagePullPolicy: Always
          name: starship-engine
          ports:
            - containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 200Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
            - mountPath: /etc/tops/conf/tops-readonly.yaml
              name: tops-readonly-config
              subPath: tops-readonly.yaml
            - mountPath: /etc/tops/conf/eks-platform-cert/eks-platform.pem
              name: eks-platform-secret
              subPath: eks-platform.pem
            - mountPath: /etc/tops/conf/eks-platform-cert/eks-platform-key.pem
              name: eks-platform-secret
              subPath: eks-platform-key.pem
            - mountPath: /root/conf
              name: starship-engine-config
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccountName: starship-engine
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: tz-config
        - name: tops-readonly-config
          configMap:
            defaultMode: 420
            name: tops-readonly-config
        - name: eks-platform-secret
          secret:
            defaultMode: 420
            secretName: eks-platform-secret
        - name: starship-engine-config
          configMap:
            defaultMode: 420
            name: starship-engine-config
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.kubernetes.io/qcloud-loadbalancer-internal-subnetid: subnet-oujto1ru
  labels:
    app: starship-engine-server
  name: starship-engine-service
  namespace: starship
spec:
  selector:
    app: starship-engine-server
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: LoadBalancer