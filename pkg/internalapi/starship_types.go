package internalapi

import (
	"encoding/json"
	"fmt"
	"strconv"
)

type ErrorCode string

type RequestV3Common struct {
	RequestID     string `json:"RequestId"`
	Action        string `json:"Action"`
	AppID         uint64 `json:"AppId,omitempty"`
	UIN           string `json:"Uin,omitempty"`
	SubAccountUIN string `json:"SubAccountUin,omitempty"`
	ClientIP      string `json:"ClientIp,omitempty"`
	APIModule     string `json:"ApiModule,omitempty"`
	Region        string `json:"Region,omitempty"`
	Token         string `json:"Token,omitempty"`
	CamContext    string `json:"CamContext,omitempty"`
	Version       string `json:"Version,omitempty"`
	RequestSource string `json:"RequestSource,omitempty"`
	Language      string `json:"Language,omitempty"`
}

type UpdateAppReq struct {
	RequestV3Common
	ClusterId      string     `json:"ClusterId"`
	AppName        string     `json:"AppName"`
	AppVersion     AppVersion `json:"AppVersion"`
	ProductName    string     `json:"ProductName"`
	Values         Values     `json:"Values"`
	AppStrategy    string     `json:"AppStrategy"`
	AppAction      string     `json:"AppAction"`
	UpdateStrategy string     `json:"UpdateStrategy"`
}

type RollbackAppReq struct {
	RequestV3Common
	TaskId int64 `json:"TaskId"`
}

type AppVersion struct {
	AppVersion    string `json:"AppVersion"`
	PluginVersion string `json:"PluginVersion"`
	ImageTag      string `json:"ImageTag"`
}

type Values struct {
	Env           []map[string]string    `json:"Env"`
	ExtendInfo    string                 `json:"ExtendInfo"`
	Token         string                 `json:"Token"`
	Addon         map[string]interface{} `json:"Addon"`
	User          string                 `json:"User"`
	ChangeId      string                 `json:"ChangeId"`
	BatchId       int                    `json:"BatchId"`
	TaskName      string                 `json:"TaskName"`
	MetaClusterId string                 `json:"MetaClusterId"`
}

type UpdateAppResponseImp struct {
	RequestId string                `json:"RequestId"`
	TaskId    string                `json:"TaskId"`
	Error     ResponseV3ErrorDetail `json:"Error,omitempty"`
}

type UpdateAppResponse struct {
	Response UpdateAppResponseImp `json:"Response"`
}

type DescribeAppUpgradingProgressReq struct {
	RequestV3Common
	AppName   string `json:"AppName"`
	ClusterId string `json:"ClusterId"`
	ChangeId  string `json:"ChangeId"`
	AppAction string `json:"AppAction"`
}

type DescribeAppUpgradingProgressResponse struct {
	Response DescribeAppUpgradingProgressResponseImp `json:"Response"`
}

type DescribeAppUpgradingProgressResponseImp struct {
	RequestId string                `json:"RequestId"`
	Task      TaskWrapper           `json:"Task"`
	Error     ResponseV3ErrorDetail `json:"Error,omitempty"`
}

type ResponseV3ErrorDetail struct {
	Code    ErrorCode `json:"Code,omitempty"`
	Message string    `json:"Message,omitempty"`
}

// TaskWrapper 提供兼容protobuf和云API标准的任务信息结构体
// 可以处理snake_case（protobuf）和PascalCase（云API）两种JSON格式
type TaskWrapper struct {
	TaskId     int64             `json:"TaskId"`
	Name       string            `json:"Name"`
	AppName    string            `json:"AppName"`
	Type       string            `json:"Type"`
	Stage      string            `json:"Stage"`
	Envs       string            `json:"Envs"`
	ExtendInfo string            `json:"ExtendInfo"`
	Region     string            `json:"Region"`
	ClusterId  string            `json:"ClusterId"`
	Timeout    int32             `json:"Timeout"`
	ChangeId   string            `json:"ChangeId"`
	Batch      int32             `json:"Batch"`
	Status     string            `json:"Status"`
	Reason     string            `json:"Reason"`
	UpdateTime string            `json:"UpdateTime"`
	CreateTime string            `json:"CreateTime"`
	Subtasks   []*SubTaskWrapper `json:"SubTasks"`
}

// SubTaskWrapper 提供兼容protobuf和云API标准的子任务信息结构体
type SubTaskWrapper struct {
	SubtaskId    int64          `json:"SubTaskId"`
	ParentTaskId int64          `json:"ParentTaskId"`
	AppName      string         `json:"AppName"`
	Action       string         `json:"Action"`
	Status       string         `json:"Status"`
	Reason       string         `json:"Reason"`
	CostTime     int32          `json:"CostTime"`
	UpdateTime   string         `json:"UpdateTime"`
	CreateTime   string         `json:"CreateTime"`
	Risks        []*RiskWrapper `json:"Risks"`
}

// RiskWrapper 提供兼容protobuf和云API标准的风险信息结构体
type RiskWrapper struct {
	Id              int64  `json:"Id"`
	SubtaskId       int64  `json:"SubTaskId"`
	AppName         string `json:"AppName"`
	Name            string `json:"Name"`
	Code            string `json:"Code"`
	Detail          string `json:"Detail"`
	Level           string `json:"Level"`
	Solution        string `json:"Solution"`
	CreateTime      string `json:"CreateTime"`
	HealthCheckName string `json:"HealthCheckName"`
}

// getFieldValue 从map中获取字段值，支持多种命名格式
func getFieldValue(data map[string]interface{}, fieldNames ...string) interface{} {
	for _, name := range fieldNames {
		if value, exists := data[name]; exists {
			return value
		}
	}
	return nil
}

// getStringField 获取字符串字段值
func getStringField(data map[string]interface{}, fieldNames ...string) string {
	if value := getFieldValue(data, fieldNames...); value != nil {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// getInt64Field 获取int64字段值
func getInt64Field(data map[string]interface{}, fieldNames ...string) int64 {
	if value := getFieldValue(data, fieldNames...); value != nil {
		switch v := value.(type) {
		case int64:
			return v
		case int:
			return int64(v)
		case float64:
			return int64(v)
		case string:
			if i, err := strconv.ParseInt(v, 10, 64); err == nil {
				return i
			}
		}
	}
	return 0
}

// getInt32Field 获取int32字段值
func getInt32Field(data map[string]interface{}, fieldNames ...string) int32 {
	if value := getFieldValue(data, fieldNames...); value != nil {
		switch v := value.(type) {
		case int32:
			return v
		case int:
			return int32(v)
		case int64:
			return int32(v)
		case float64:
			return int32(v)
		case string:
			if i, err := strconv.ParseInt(v, 10, 32); err == nil {
				return int32(i)
			}
		}
	}
	return 0
}

// UnmarshalJSON 实现TaskWrapper的灵活JSON解析，支持protobuf和云API两种格式
func (t *TaskWrapper) UnmarshalJSON(data []byte) error {
	// 先解析为map以便灵活处理字段名
	var rawData map[string]interface{}
	if err := json.Unmarshal(data, &rawData); err != nil {
		return fmt.Errorf("failed to unmarshal JSON to map: %w", err)
	}

	// 映射各个字段，支持多种命名格式
	t.TaskId = getInt64Field(rawData, "TaskId", "taskId", "task_id")
	t.Name = getStringField(rawData, "Name", "name", "task_name")
	t.AppName = getStringField(rawData, "AppName", "appName", "app_name")
	t.Type = getStringField(rawData, "Type", "type", "task_type")
	t.Stage = getStringField(rawData, "Stage", "stage", "task_stage")
	t.Envs = getStringField(rawData, "Envs", "envs", "environments")
	t.ExtendInfo = getStringField(rawData, "ExtendInfo", "extendInfo", "extend_info")
	t.Region = getStringField(rawData, "Region", "region")
	t.ClusterId = getStringField(rawData, "ClusterId", "clusterId", "cluster_id")
	t.Timeout = getInt32Field(rawData, "Timeout", "timeout")
	t.ChangeId = getStringField(rawData, "ChangeId", "changeId", "change_id")
	t.Batch = getInt32Field(rawData, "Batch", "batch")
	t.Status = getStringField(rawData, "Status", "status", "task_status")
	t.Reason = getStringField(rawData, "Reason", "reason", "task_reason")
	t.UpdateTime = getStringField(rawData, "UpdateTime", "updateTime", "update_time")
	t.CreateTime = getStringField(rawData, "CreateTime", "createTime", "create_time")

	// 处理子任务数组
	if subTasksData := getFieldValue(rawData, "SubTasks", "subTasks", "sub_tasks", "subtasks"); subTasksData != nil {
		if subTasksArray, ok := subTasksData.([]interface{}); ok {
			t.Subtasks = make([]*SubTaskWrapper, len(subTasksArray))
			for i, subTaskData := range subTasksArray {
				if subTaskMap, ok := subTaskData.(map[string]interface{}); ok {
					subTask := &SubTaskWrapper{}
					if err := subTask.unmarshalFromMap(subTaskMap); err != nil {
						return fmt.Errorf("failed to unmarshal subtask at index %d: %w", i, err)
					}
					t.Subtasks[i] = subTask
				}
			}
		}
	}

	return nil
}

// unmarshalFromMap 从map数据中解析SubTaskWrapper
func (st *SubTaskWrapper) unmarshalFromMap(data map[string]interface{}) error {
	st.SubtaskId = getInt64Field(data, "SubTaskId", "subTaskId", "sub_task_id", "subtask_id")
	st.ParentTaskId = getInt64Field(data, "ParentTaskId", "parentTaskId", "parent_task_id")
	st.AppName = getStringField(data, "AppName", "appName", "app_name")
	st.Action = getStringField(data, "Action", "action", "task_action")
	st.Status = getStringField(data, "Status", "status", "task_status")
	st.Reason = getStringField(data, "Reason", "reason", "task_reason")
	st.CostTime = getInt32Field(data, "CostTime", "costTime", "cost_time")
	st.UpdateTime = getStringField(data, "UpdateTime", "updateTime", "update_time")
	st.CreateTime = getStringField(data, "CreateTime", "createTime", "create_time")

	// 处理风险数组
	if risksData := getFieldValue(data, "Risks", "risks"); risksData != nil {
		if risksArray, ok := risksData.([]interface{}); ok {
			st.Risks = make([]*RiskWrapper, len(risksArray))
			for i, riskData := range risksArray {
				if riskMap, ok := riskData.(map[string]interface{}); ok {
					risk := &RiskWrapper{}
					if err := risk.unmarshalFromMap(riskMap); err != nil {
						return fmt.Errorf("failed to unmarshal risk at index %d: %w", i, err)
					}
					st.Risks[i] = risk
				}
			}
		}
	}

	return nil
}

// unmarshalFromMap 从map数据中解析RiskWrapper
func (r *RiskWrapper) unmarshalFromMap(data map[string]interface{}) error {
	r.Id = getInt64Field(data, "Id", "id", "risk_id")
	r.SubtaskId = getInt64Field(data, "SubTaskId", "subTaskId", "sub_task_id", "subtask_id")
	r.AppName = getStringField(data, "AppName", "appName", "app_name")
	r.Name = getStringField(data, "Name", "name", "risk_name")
	r.Code = getStringField(data, "Code", "code", "risk_code")
	r.Detail = getStringField(data, "Detail", "detail", "risk_detail")
	r.Level = getStringField(data, "Level", "level", "risk_level")
	r.Solution = getStringField(data, "Solution", "solution", "risk_solution")
	r.CreateTime = getStringField(data, "CreateTime", "createTime", "create_time")
	r.HealthCheckName = getStringField(data, "HealthCheckName", "healthCheckName", "health_check_name")

	return nil
}
