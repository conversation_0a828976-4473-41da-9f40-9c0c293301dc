package batchStrategy

import (
	"fmt"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

type EksPodStrategy struct{}

func (e *EksPodStrategy) DivideBatches(task model.Task, appAction string, pods []util.Pod) ([]BatchConfig, error) {
	if len(pods) == 0 {
		return nil, nil
	}

	// 创建集群到Pod的映射
	clusterPodMap := make(map[string][]util.Pod)
	clusterSet := make(map[string]bool)
	var clusters []string

	// 遍历所有Pod，创建集群映射并提取唯一集群列表
	for _, pod := range pods {
		clusterPodMap[pod.ClusterId] = append(clusterPodMap[pod.ClusterId], pod)
		if !clusterSet[pod.ClusterId] {
			clusterSet[pod.ClusterId] = true
			clusters = append(clusters, pod.ClusterId)
		}
	}

	// 使用RegionBatchStrategy处理集群列表
	regionStrategy := NewRegionBatchStrategy()
	clusterBatches := []BatchConfig{
		{
			Clusters: clusters,
		},
	}

	// 执行地域批处理策略
	processedBatches, err := regionStrategy.DivideBatches(clusterBatches, true, BatchStrategyOptions{
		AppAction: appAction,
		AppName:   task.Component.Name,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to divide batches by region: %v", err)
	}

	// 重新分配BatchId以确保连续性和唯一性
	// 这对于queryUnplayedTask的正确执行至关重要
	var podBatches []BatchConfig
	batchIdCounter := int64(1)

	for _, batch := range processedBatches {
		// 收集该批次中所有集群的Pod
		var batchPods []util.Pod
		for _, clusterId := range batch.Clusters {
			if pods, exists := clusterPodMap[clusterId]; exists {
				batchPods = append(batchPods, pods...)
			}
		}

		// 设置批次间隔时间
		interval := RegionInterval20Min
		// 第三批和第五批使用1小时间隔
		if batch.RegionPriority == 3 || batch.RegionPriority == 5 {
			interval = RegionInterval1Hour
		}

		if appAction != consts.PUBLISH {
			interval = RegionInterval3Min
		}

		podBatches = append(podBatches, BatchConfig{
			Region:         batch.Region,
			RegionPriority: batch.RegionPriority,
			Pods:           batchPods,
			Clusters:       batch.Clusters,
			BatchId:        batchIdCounter,
			SubBatchId:     1,
			Interval:       int64(interval),
		})

		batchIdCounter++
	}

	return podBatches, nil
}
