/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	rest "k8s.io/client-go/rest"
	"tkestack.io/tke/api/client/clientset/versioned/scheme"
	v1 "tkestack.io/tke/api/platform/v1"
)

type PlatformV1Interface interface {
	RESTClient() rest.Interface
	CSIOperatorsGetter
	ClustersGetter
	ClusterAddonsGetter
	ClusterAddonTypesGetter
	ClusterCredentialsGetter
	ClusterGroupAPIResourceItemsesGetter
	ConfigMapsGetter
	CronHPAsGetter
	MachinesGetter
	PersistentEventsGetter
	RegistriesGetter
	TappControllersGetter
}

// PlatformV1Client is used to interact with features provided by the platform.tkestack.io group.
type PlatformV1Client struct {
	restClient rest.Interface
}

func (c *PlatformV1Client) CSIOperators() CSIOperatorInterface {
	return newCSIOperators(c)
}

func (c *PlatformV1Client) Clusters() ClusterInterface {
	return newClusters(c)
}

func (c *PlatformV1Client) ClusterAddons() ClusterAddonInterface {
	return newClusterAddons(c)
}

func (c *PlatformV1Client) ClusterAddonTypes() ClusterAddonTypeInterface {
	return newClusterAddonTypes(c)
}

func (c *PlatformV1Client) ClusterCredentials() ClusterCredentialInterface {
	return newClusterCredentials(c)
}

func (c *PlatformV1Client) ClusterGroupAPIResourceItemses() ClusterGroupAPIResourceItemsInterface {
	return newClusterGroupAPIResourceItemses(c)
}

func (c *PlatformV1Client) ConfigMaps() ConfigMapInterface {
	return newConfigMaps(c)
}

func (c *PlatformV1Client) CronHPAs() CronHPAInterface {
	return newCronHPAs(c)
}

func (c *PlatformV1Client) Machines() MachineInterface {
	return newMachines(c)
}

func (c *PlatformV1Client) PersistentEvents() PersistentEventInterface {
	return newPersistentEvents(c)
}

func (c *PlatformV1Client) Registries() RegistryInterface {
	return newRegistries(c)
}

func (c *PlatformV1Client) TappControllers() TappControllerInterface {
	return newTappControllers(c)
}

// NewForConfig creates a new PlatformV1Client for the given config.
func NewForConfig(c *rest.Config) (*PlatformV1Client, error) {
	config := *c
	if err := setConfigDefaults(&config); err != nil {
		return nil, err
	}
	client, err := rest.RESTClientFor(&config)
	if err != nil {
		return nil, err
	}
	return &PlatformV1Client{client}, nil
}

// NewForConfigOrDie creates a new PlatformV1Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *PlatformV1Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new PlatformV1Client for the given RESTClient.
func New(c rest.Interface) *PlatformV1Client {
	return &PlatformV1Client{c}
}

func setConfigDefaults(config *rest.Config) error {
	gv := v1.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = scheme.Codecs.WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}

	return nil
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *PlatformV1Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
