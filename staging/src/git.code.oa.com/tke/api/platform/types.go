/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package platform

import (
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Cluster is a Kubernetes cluster in TKE.
type Cluster struct {
	metav1.TypeMeta
	metav1.ObjectMeta

	Spec   ClusterSpec
	Status ClusterStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterList is the whole list of all clusters which owned by a tenant.
type ClusterList struct {
	metav1.TypeMeta
	metav1.ListMeta

	Items []Cluster
}

// ClusterSpec is a description of a cluster.
type ClusterSpec struct {
	// +optional
	Finalizers  []FinalizerName
	TenantID    string
	DisplayName string
	Type        ClusterType
	// +optional
	ClusterCIDR string
	// +optional
	PublicAlternativeNames []string
	// +optional
	Features ClusterFeature
	// +optional
	Properties ClusterProperty
	// +optional
	CreatorUIN string
	// +optional
	UseRBAC *bool
}

// ClusterStatus represents information about the status of a cluster.
type ClusterStatus struct {
	// +optional
	Locked *bool
	// +optional
	Version string
	// +optional
	Phase ClusterPhase
	// +optional
	Conditions []ClusterCondition
	// +optional
	Message string
	// A brief CamelCase message indicating details about why the pod is in this state.
	// +optional
	Reason string

	// List of addresses reachable to the cluster.
	// +optional
	Addresses []ClusterAddress
	// +optional
	Credential ClusterCredential
	// Deprecated
	// +optional
	AddOns ClusterAddOn
	// +optional
	Resource ClusterResource
	// +optional
	Components []ClusterComponent
}

// Deprecated
// ClusterAddOn contains the AddOn component for the current kubernetes cluster
type ClusterAddOn struct {
}

// FinalizerName is the name identifying a finalizer during cluster lifecycle.
type FinalizerName string

const (
	ClusterFinalize FinalizerName = "cluster"
)

// ClusterType defines the type of cluster
type ClusterType string

const (
	ClusterImported ClusterType = "imported"
)

// ClusterPhase defines the phase of cluster constructor
type ClusterPhase string

const (
	// ClusterRunning is the normal running phase
	ClusterRunning ClusterPhase = "running"
	// ClusterInitializing is the initialize phase
	ClusterInitializing ClusterPhase = "initializing"
	// ClusterFailed is the failed phase
	ClusterFailed ClusterPhase = "failed"
	// ClusterTerminating means the cluster is undergoing graceful termination
	ClusterTerminating ClusterPhase = "terminating"
)

// ClusterCondition contains details for the current condition of this cluster.
type ClusterCondition struct {
	// Type is the type of the condition.
	Type string
	// Status is the status of the condition.
	// Can be True, False, Unknown.
	Status ConditionStatus
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string
	// Human-readable message indicating details about last transition.
	// +optional
	Message string
}

type ClusterAddressType string

// These are valid address type of node.
const (
	ClusterPublic    ClusterAddressType = "public"
	ClusterAdvertise ClusterAddressType = "advertise"
	ClusterInternal  ClusterAddressType = "internal"
)

// ClusterAddress contains information for the cluster's address.
type ClusterAddress struct {
	// Cluster address type, one of Public, ExternalIP or InternalIP.
	Type ClusterAddressType
	// The cluster address.
	IP    string
	Port  int32
	Proxy string
}

type ClusterCredential struct {
	// +optional
	CACert []byte
	// +optional
	CAKey []byte
	// +optional
	APIServerCert []byte
	// +optional
	APIServerKey []byte
	// +optional
	ClientCert []byte
	// +optional
	ClientKey []byte
	// +optional
	Privileged bool
	// +optional
	Token *string
	// +optional
	KubeletToken *string
	// +optional
	KubeProxyToken *string
}

type ClusterFeature struct {
	// +optional
	IPVS *bool
	// +optional
	Public *bool
}

type ClusterProperty struct {
	// +optional
	MaxClusterServiceNum *int32
	// +optional
	MaxNodePodNum *int32
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type Registry struct {
	metav1.TypeMeta
	// Standard object's metadata.
	// +optional
	metav1.ObjectMeta
	// +optional
	Spec RegistrySpec
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// RegistryList is a resource containing a list of Registry objects.
type RegistryList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// +optional
	Items []Registry
}

type RegistrySpec struct {
	// +optional
	TenantID string
	// +optional
	DisplayName string
	// +optional
	ClusterName string
	// +optional
	URL string
	// +optional
	UserName *string
	// +optional
	Password *string
}

// ResourceName is the name identifying various resources in a ResourceList.
type ResourceName string

// ResourceList is a set of (resource name, quantity) pairs.
type ResourceList map[ResourceName]resource.Quantity

type ClusterResource struct {
	// Capacity represents the total resources of a cluster.
	// +optional
	Capacity ResourceList
	// Allocatable represents the resources of a cluster that are available for scheduling.
	// Defaults to Capacity.
	// +optional
	Allocatable ResourceList
	// +optional
	Allocated ResourceList
}

type ClusterComponent struct {
	Type     string
	Replicas ClusterComponentReplicas
}

type ClusterComponentReplicas struct {
	Desired   int32
	Current   int32
	Available int32
	Updated   int32
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterApplyOptions is the query options to a kube-apiserver proxy call for cluster object.
type ClusterApplyOptions struct {
	metav1.TypeMeta
	// +optional
	NotUpdate bool
	// +optional
	IsMultiClusterResources bool
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Project is a logical top-level container for a set of kubernetes resources.
type Project struct {
	metav1.TypeMeta
	metav1.ObjectMeta
	// Spec defines the desired identities of project in this set.
	Spec   ProjectSpec
	Status ProjectStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ProjectList is the whole list of all projects which owned by a tenant.
type ProjectList struct {
	metav1.TypeMeta
	metav1.ListMeta
	// List of projects.
	Items []Project
}

// ProjectSpec is a description of a project.
type ProjectSpec struct {
	TenantID    string
	DisplayName string
	Description string
	Managers    []Manager
}

// Manager includes the username.
type Manager struct {
	Name        string
	DisplayName string
}

// ProjectStatus represents information about the status of a project.
type ProjectStatus struct {
	Locked *bool
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NamespaceSet is a namespace in cluster for project.
type NamespaceSet struct {
	metav1.TypeMeta
	metav1.ObjectMeta

	// Spec defines the desired identities of projects in this set.
	Spec   NamespaceSetSpec
	Status NamespaceSetStatus
}

// NamespaceSetSpec is a description of a namespace set.
type NamespaceSetSpec struct {
	TenantID      string
	ProjectName   string
	ClusterName   string
	NamespaceName string
}

// NamespaceSetPhase defines the phase of namespace set constructor
type NamespaceSetPhase string

const (
	// NamespaceSetPending means the namespace is wait initializing
	NamespaceSetPending NamespaceSetPhase = "pending"
	// NamespaceSetActive means the namespace is available for use in the system
	NamespaceSetActive NamespaceSetPhase = "active"
	// NamespaceSetFailed means the namespace is failed
	NamespaceSetFailed NamespaceSetPhase = "failed"
)

// NamespaceSetStatus represents information about the status of a namespace set.
type NamespaceSetStatus struct {
	// Phase is the current lifecycle phase of the namespace of cluster.
	Phase  NamespaceSetPhase
	Reason string
	Locked *bool
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NamespaceSetList is the whole list of all namespaces in clusters which
// owned by a project.
type NamespaceSetList struct {
	metav1.TypeMeta
	metav1.ListMeta
	// List of namespaces
	Items []NamespaceSet
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// PersistentEvent is a recorder of kubernetes event.
type PersistentEvent struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec PersistentEventSpec
	// +optional
	Status PersistentEventStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// PersistentEventList is the whole list of all clusters which owned by a tenant.
type PersistentEventList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of PersistentEvents
	Items []PersistentEvent
}

type PersistentEventSpec struct {
	TenantID          string
	ClusterName       string
	PersistentBackEnd PersistentBackEnd
	Version           string
}

type PersistentEventStatus struct {
	// Phase is the current lifecycle phase of the persistent event of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
	// +optional
	Version string
}

type PersistentBackEnd struct {
	CLS *StorageBackEndCLS
	ES  *StorageBackEndES
}

type StorageBackEndCLS struct {
	LogSetID string
	TopicID  string
}

type StorageBackEndES struct {
	IP        string
	Port      int
	Scheme    string
	IndexName string
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HelmProxyOptions is the query options to a helm-api's proxy call.
type HelmProxyOptions struct {
	metav1.TypeMeta

	// Path is the URL path to use for the current proxy request to helm-api.
	// +optional
	Path string
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Helm is a kubernetes package manager.
type Helm struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec HelmSpec
	// +optional
	Status HelmStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HelmList is the whole list of all helms which owned by a tenant.
type HelmList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of Helms
	Items []Helm
}

type HelmSpec struct {
	TenantID          string
	ClusterName       string
	VersionDeprecated HelmVersion
	Version           string
}

type HelmVersion struct {
	Tiller  string
	Swift   string
	HelmAPI string
}

type HelmStatus struct {
	// Phase is the current lifecycle phase of the helm of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
	// +optional
	Version string
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GameAppProxyOptions is the query options to a gameapp proxy call.
type GameAppProxyOptions struct {
	metav1.TypeMeta
	// +optional
	Namespace string
	// +optional
	Name string
	// +optional
	Action string
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GameApp is a kubernetes package manager.
type GameApp struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec GameAppSpec
	// +optional
	Status GameAppStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GameAppList is the whole list of all GameApps which owned by a tenant.
type GameAppList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of GameApps
	Items []GameApp
}

type GameAppSpec struct {
	TenantID          string
	ClusterName       string
	VersionDeprecated GameAppVersion
	Version           string
}

type GameAppVersion struct {
	GameAppOperator string
}

type GameAppStatus struct {
	// Phase is the current lifecycle phase of the GameApp of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
	// +optional
	Version string
}

// +genclient
// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ConfigMap holds configuration data for tke to consume.
type ConfigMap struct {
	metav1.TypeMeta
	// Standard object's metadata.
	// +optional
	metav1.ObjectMeta

	// Data contains the configuration data.
	// Each key must consist of alphanumeric characters, '-', '_' or '.'.
	// Values with non-UTF-8 byte sequences must use the BinaryData field.
	// The keys stored in Data must not overlap with the keys in
	// the BinaryData field, this is enforced during validation process.
	// +optional
	Data map[string]string

	// BinaryData contains the binary data.
	// Each key must consist of alphanumeric characters, '-', '_' or '.'.
	// BinaryData can contain byte sequences that are not in the UTF-8 range.
	// The keys stored in BinaryData must not overlap with the ones in
	// the Data field, this is enforced during validation process.
	// +optional
	BinaryData map[string][]byte
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ConfigMapList is a resource containing a list of ConfigMap objects.
type ConfigMapList struct {
	metav1.TypeMeta

	// +optional
	metav1.ListMeta

	// Items is the list of ConfigMaps.
	Items []ConfigMap
}

type ConditionStatus string

// These are valid condition statuses.
// "ConditionTrue" means a resource is in the condition.
// "ConditionFalse" means a resource is not in the condition.
// "ConditionUnknown" means server can't decide if a resource is in the condition
// or not.
const (
	ConditionTrue    ConditionStatus = "True"
	ConditionFalse   ConditionStatus = "False"
	ConditionUnknown ConditionStatus = "Unknown"
)

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// EniIpamd is a kubernetes eni ipamd.
type EniIpamd struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec EniIpamdSpec
	// +optional
	Status EniIpamdStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// EniIpamdList is the whole list of all eni ipamd which owned by a tenant.
type EniIpamdList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of eni ipamd
	Items []EniIpamd
}

type EniIpamdSpec struct {
	TenantID            string
	ClusterName         string
	VpcID               string
	SubnetID            string
	SubnetCidr          string
	Zone                string
	VirtualScale        int32
	Version             string
	ClaimExpiredSeconds int32
}

// EniIpamdCondition contains details for the current condition of this eniipamd.
type EniIpamdCondition struct {
	// Type is the type of the condition.
	Type string
	// Status is the status of the condition.
	// Can be True, False, Unknown.
	Status ConditionStatus
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string
	// Human-readable message indicating details about last transition.
	// +optional
	Message string
}

type EniIpamdStatus struct {
	// Phase is the current lifecycle phase of the eni ipamd of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Conditions []EniIpamdCondition
	// +optional
	Version string
}

const (
	FinalizerEniIpamd FinalizerName = "tke.cloud.tencent.com/eni-ipamd"
)

// AddonLevel indicates the level of cluster addon.
type AddonLevel string

// These are valid level of addon.
const (
	// LevelBasic is level for basic of cluster.
	LevelBasic AddonLevel = "Basic"
	// LevelEnhance is level for enhance of cluster.
	LevelEnhance AddonLevel = "Enhance"
)

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// AddonType records the all addons of platform available.
type AddonType struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of addons in this set.
	// +optional
	Spec AddonTypeSpec
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// AddonTypeList is a resource containing a list of AddonType objects.
type AddonTypeList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// +optional
	Items []AddonType
}

// AddonTypeSpec indicates the specifications of the AddonType.
type AddonTypeSpec struct {
	// Addon type, one of Helm, PersistentEvent or LogCollector etc.
	Type string
	// TenantIDs are whitelist of the addonType
	TenantIDs     []string
	Level         AddonLevel
	LatestVersion string
	Description   string
	VersionSpec   []VersionMap
}

// VersionMap indicates the specifications of every componet version in the addon.
type VersionMap struct {
	Name string
	Tag  string
	// FullImagePath can be used for overwrite addon componet .Spec.Image
	FullImagePath string
}

// +genclient
// +genclient:nonNamespaced
// +genclient:onlyVerbs=list,get
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAPIResource contains the GKV for the current kubernetes cluster
type ClusterAPIResource struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// groupVersion is the group and version this APIResourceList is for.
	GroupVersion string
	// resources contains the name of the resources and if they are namespaced.
	APIResources []ClusterGroupAPIResource
}

// APIResource specifies the name of a resource and whether it is namespaced.
type ClusterGroupAPIResource struct {
	// name is the plural name of the resource.
	Name string
	// singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely.
	// The singularName is more correct for reporting status on a single item and both singular and plural are allowed
	// from the kubectl CLI interface.
	SingularName string
	// namespaced indicates if a resource is namespaced or not.
	Namespaced bool
	// group is the preferred group of the resource.  Empty implies the group of the containing resource list.
	// For subresources, this may have a different value, for example: Scale".
	Group string
	// version is the preferred version of the resource.  Empty implies the version of the containing resource list
	// For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)".
	Version string
	// kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')
	Kind string
	// verbs is a list of supported kube verbs (this includes get, list, watch, create,
	// update, patch, delete, deletecollection, and proxy)
	Verbs []string
	// shortNames is a list of suggested short names of the resource.
	ShortNames []string
	// categories is a list of the grouped resources this resource belongs to (e.g. 'all')
	Categories []string
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAPIResourceList is the whole list of all ClusterAPIResource.
type ClusterAPIResourceList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of ClusterAPIResource
	Items []ClusterAPIResource
	// Failed Group Error
	FailedGroupError string
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAPIResourceOptions is the query options.
type ClusterAPIResourceOptions struct {
	metav1.TypeMeta
	// +optional
	OnlySecure bool
}

// +genclient
// +genclient:nonNamespaced
// +genclient:onlyVerbs=list,get
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddon contains the Addon component for the current kubernetes cluster
type ClusterAddon struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of addons in this set.
	// +optional
	Spec ClusterAddonSpec
	// +optional
	Status ClusterAddonStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonList is the whole list of all ClusterAddon.
type ClusterAddonList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of ClusterAddon
	Items []ClusterAddon
}

// ClusterAddonSpec indicates the specifications of the ClusterAddon.
type ClusterAddonSpec struct {
	// Addon type, one of Helm, PersistentEvent or LogCollector etc.
	Type string
	// AddonLevel is level of cluster addon.
	Level AddonLevel
	// Version
	Version string
}

// ClusterAddonStatus is information about the current status of a ClusterAddon.
type ClusterAddonStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of the addon of cluster.
	// +optional
	Phase string
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
}

// +genclient
// +genclient:nonNamespaced
// +genclient:onlyVerbs=list
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonType records the all addons of cluster available.
type ClusterAddonType struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Addon type, one of Helm, PersistentEvent or LogCollector etc.
	Type string
	// AddonLevel is level of cluster addon.
	Level AddonLevel
	// LatestVersion is latest version of the addon.
	LatestVersion string
	Description   string
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonTypeList is a resource containing a list of ClusterAddonType objects.
type ClusterAddonTypeList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// +optional
	Items []ClusterAddonType
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GPUManager is a kind of device plugin for kubelet to help manage GPUs.
type GPUManager struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec GPUManagerSpec
	// +optional
	Status GPUManagerStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// GPUManagerList is the whole list of all GPUManager which owned by a tenant.
type GPUManagerList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of Helms
	Items []GPUManager
}

// GPUManagerSpec describes the attributes of a GPUManager.
type GPUManagerSpec struct {
	TenantID    string
	ClusterName string
	Version     string
}

// GPUManagerStatus is information about the current status of a GPUManager.
type GPUManagerStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of the GPUManager of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LogCollectorProxyOptions is the query options to a kube-apiserver proxy call for LogCollector crd object.
type LogCollectorProxyOptions struct {
	metav1.TypeMeta

	Namespace string
	Name      string
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CLSLogConfigProxyOptions is the query options to a kube-apiserver proxy call for CLS LogConfig crd object.
type CLSLogConfigProxyOptions struct {
	metav1.TypeMeta

	// +optional
	Name string

	// +optional
	Action string

	// A selector to restrict the list of returned objects by their fields.
	// Defaults to everything.
	// +optional
	FieldSelector string

	// Limit specifies the maximum number of results to return from the server. The server may
	// not support this field on all resource types, but if it does and more results remain it
	// will set the continue field on the returned list object.
	// +optional
	Limit int64

	// Continue is a token returned by the server that lets a client retrieve chunks of results
	// from the server by specifying limit. The server may reject requests for continuation tokens
	// it does not recognize and will return a 410 error if the token can no longer be used because
	// it has expired.
	// +optional
	Continue string
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// RecommendationProxyOptions is the query options to a kube-apiserver proxy call for Recommendation object.
type RecommendationProxyOptions struct {
	metav1.TypeMeta

	Path string
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LogCollector is a manager to collect logs of workload.
type LogCollector struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of LogCollector.
	// +optional
	Spec LogCollectorSpec
	// +optional
	Status LogCollectorStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LogCollectorList is the whole list of all LogCollector which owned by a tenant.
type LogCollectorList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of volume decorators.
	Items []LogCollector
}

// LogCollectorSpec describes the attributes of a LogCollector.
type LogCollectorSpec struct {
	TenantID    string
	ClusterName string
	Version     string
}

// LogCollectorStatus is information about the current status of a LogCollector.
type LogCollectorStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of the LogCollector of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
}

// AddonPhase defines the phase of addon
type AddonPhase string

const (
	// AddonPhaseInitializing means is wait initializing.
	AddonPhaseInitializing AddonPhase = "initializing"
	// AddonPhaseReinitializing means is reinitializing.
	AddonPhaseReinitializing AddonPhase = "reinitializing"
	// AddonPhaseChecking means is wait checking.
	AddonPhaseChecking AddonPhase = "checking"
	// AddonPhaseRunning means is running.
	AddonPhaseRunning AddonPhase = "running"
	// AddonPhaseUpgrading means is upgrading.
	AddonPhaseUpgrading AddonPhase = "upgrading"
	// AddonPhaseFailed means has been failed.
	AddonPhaseFailed      AddonPhase = "failed"
	AddonPhaseTerminating AddonPhase = "terminating"
	AddonPhaseUnknown     AddonPhase = "unknown"
	AddonPhaseUnhealthy   AddonPhase = "unhealthy"
	AddonPhasePending     AddonPhase = "pending"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LBCFProxyOptions is the query options to a kube-apiserver proxy call.
type LBCFProxyOptions struct {
	metav1.TypeMeta

	Namespace string
	Name      string
	Action    string
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LBCF is a kubernetes load balancer manager
type LBCF struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of LBCF.
	// +optional
	Spec LBCFSpec
	// +optional
	Status LBCFStatus
}

// LBCFSpec defines the desired identities of LBCF.
type LBCFSpec struct {
	TenantID    string
	ClusterName string
	Version     string
}

// LBCFStatus is information about the current status of a LBCF.
type LBCFStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of the CronHPA of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// LBCFList is the whole list of all LBCF which owned by a tenant.
type LBCFList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of CronHPAs
	Items []LBCF
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CFS is a kubernetes csi for using CFS.
type CFS struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec CFSSpec
	// +optional
	Status CFSStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CFSList is the whole list of all CFSs.
type CFSList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of CFSs
	Items []CFS
}

// CFSSpec describes the attributes on a CFS.
type CFSSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	Rootdir     string
}

// CFSStatus is information about the current status of a CFS.
type CFSStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of cfs.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// COS is a kubernetes csi for using COS.
type COS struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec COSSpec
	// +optional
	Status COSStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// COSList is the whole list of all COSs.
type COSList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of COSs
	Items []COS
}

// COSSpec describes the attributes on a COS.
type COSSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	Rootdir     string
}

// COSStatus is information about the current status of a COS.
type COSStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of cos.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
}

// +genclient
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAuthentication is the resource that record the users tencentcloud accounts.
type ClusterAuthentication struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of clusters in this set.
	// +optional
	TenantID string
	// +optional
	ClusterName string
	// +optional
	OwnerUIN string
	// +optional
	SubAccountUIN string
	// +optional
	ServiceRole bool
	// +optional
	AuthenticationInfo AuthenticationInfo
}

type AuthenticationInfo struct {
	ClientCertificate []byte
	ClientKey         []byte
	CommonName        string
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAuthenticationList is the whole list of all ClusterAuthentications.
type ClusterAuthenticationList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of ClusterAuthentications
	Items []ClusterAuthentication
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeProblemDetector is a detect problem tool for cluster node.
type NodeProblemDetector struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec NodeProblemDetectorSpec
	// +optional
	Status NodeProblemDetectorStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeProblemDetectorList is the whole list of all NodeProblemDetectors.
type NodeProblemDetectorList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of NPDs
	Items []NodeProblemDetector
}

// NodeProblemDetectorSpec describes the attributes on a NPD.
type NodeProblemDetectorSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	SelfCure    bool
	UIN         string
	SubUin      string
	Policys     []Policy
}
type Policy struct {
	ConditionType string
	Actions       Actions
}
type Actions struct {
	Runtime RunTime
	NodePod NodePod
	CVM     CVM
}
type NodePod struct {
	RetryCounts int32
	Evict       bool
}
type RunTime struct {
	RetryCounts    int32
	ReStartDokcer  bool
	ReStartKubelet bool
}
type CVM struct {
	RetryCounts int32
	ReBootCVM   bool
}

// NodeProblemDetectorStatus is information about the current status of a NPD.
type NodeProblemDetectorStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of npd.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ImageP2P is a P2P based distribution image system for Kubernetes.
type ImageP2P struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec ImageP2PSpec
	// +optional
	Status ImageP2PStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ImageP2PList is the whole list of all P2Ps.
type ImageP2PList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of P2Ps
	Items []ImageP2P
}

// ImageP2PSpec describes the attributes on a P2P.
type ImageP2PSpec struct {
	TenantID          string
	ClusterName       string
	RegistryAddress   string
	AgentDownloadRate int64
	ProxyReplicas     int64
	TrackerReplicas   int64
	Version           string
	P2PAgentWorkDir   string
	P2PProxyWorkDir   string
}

// ImageP2PStatus is information about the current status of a P2P.
type ImageP2PStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of p2p.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DNSAutoscaler is a kubernetes Horizontal cluster-proportional-autoscaler
type DNSAutoscaler struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of DNSAutoscaler.
	// +optional
	Spec DNSAutoscalerSpec
	// +optional
	Status DNSAutoscalerStatus
}

// DNSAutoscalerSpec defines the desired identities of DNSAutoscaler.
type DNSAutoscalerSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	Target      string
}

// DNSAutoscaler Status is information about the current status of a DNSAutoscaler.
type DNSAutoscalerStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of the CronHPA of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time

	// Components is a generic status holder for the top level resource
	// +optional
	Components []ComponentStatus
}

// ComponentStatus is a generic status holder for objects
type ComponentStatus struct {
	// Link to object
	// +optional
	Link string
	// Name of object
	Name string
	// Kind of object
	Kind string
	// Object group
	// +optional
	Group string
	// Status. Values: InProgress, Ready, Unknown
	Status string
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DNSAutoscalerList is the whole list of all DNSAutoscaler which owned by a tenant.
type DNSAutoscalerList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of DNSAutoscalers
	Items []DNSAutoscaler
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeLocalDNSCache is a kubernetes node local dns cache
type NodeLocalDNSCache struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of NodeLocalDNSCache
	// +optional
	Spec NodeLocalDNSCacheSpec
	// +optional
	Status NodeLocalDNSCacheStatus
}

// NodeLocalDNSCacheSpec defines the desired identities of NodeLocalDNSCache.
type NodeLocalDNSCacheSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	Target      string
}

// NodeLocalDNSCache Status is information about the current status of a NodeLocalDNSCache.
type NodeLocalDNSCacheStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of the CronHPA of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time

	// Components is a generic status holder for the top level resource
	// +optional
	Components []ComponentStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeLocalDNSCacheList is the whole list of all NodeLocalDNSCache which owned by a tenant.
type NodeLocalDNSCacheList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of NodeLocalDNSCache
	Items []NodeLocalDNSCache
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OOMGuard is a addon to avoid frequently cgroup OOM for Kubernetes.
type OOMGuard struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec OOMGuardSpec
	// +optional
	Status OOMGuardStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OOMGuardList is the whole list of all OOMGuardList.
type OOMGuardList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of OOMGuard
	Items []OOMGuard
}

// OOMGuardSpec describes the attributes on a OOMGuard.
type OOMGuardSpec struct {
	TenantID    string
	ClusterName string
	Version     string
}

// OOMGuardStatus is information about the current status of a OOMGuard.
type OOMGuardStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of OOMGuard.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Tcr is a enterprise private docker registry.
type Tcr struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec TcrSpec
	// +optional
	Status TcrStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// TcrList is the whole list of all TCRs.
type TcrList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of TCRs
	Items []Tcr
}

// HostItem ...
type HostItem struct {
	Domain   string
	IP       string
	Disabled bool
	V6       bool
}

// TcrItem ...
type TcrItem struct {
	Name                  string
	RegistrySource        string
	Username              string
	Password              string
	Server                string
	NamespacesString      string
	ServiceAccountsString string
}

// TcrSpec describes the attributes of the Tcr.
type TcrSpec struct {
	TenantID    string
	ClusterName string
	Version     string

	Hosts []HostItem
	Tcrs  []TcrItem

	Accelerated           bool
	AcceleratedNamespaces string
}

// TcrStatus is information about the current status of a TCR.
type TcrStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of TCR.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonKind returns a kind name
type ClusterAddonKind struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
}

// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterAddonKindList saves the addons grouped by kind
type ClusterAddonKindList struct {
	metav1.TypeMeta

	// +optional
	metav1.ListMeta

	// +optional
	Items []ClusterAddonKind
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HPCProxyOptions is the query options to a hpc proxy call.
type HPCProxyOptions struct {
	metav1.TypeMeta
	// +optional
	Namespace string
	// +optional
	Name string
	// +optional
	Action string
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HPC is a kubernetes Horizontal pod cron autoscaler
type HPC struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec HPCSpec
	// +optional
	Status HPCStatus
}

// HPCSpec defines the desired identities of HPC.
type HPCSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	ChartInfo   ChartSpec
	UIN         string
	SubUin      string
}

// ChartSpec defines the desired identities of Chart.
type ChartSpec struct {
	Chart           string
	ChartFrom       string
	ChartVersion    string
	ChartNamespace  string
	ChartInstanceID string
}

// HPCStatus is information about the current status of a HPC.
type HPCStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of cos.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time

	// Components is a generic status holder for the top level resource
	// +optional
	Components []ComponentStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// HPCList is the whole list of all HPC which owned by a tenant.
type HPCList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of HPCs
	Items []HPC
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NginxIngressProxyOptions is the query options to a kube-apiserver proxy call for LogCollector crd object.
type NginxIngressProxyOptions struct {
	metav1.TypeMeta `json:",inline"`

	Name string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NginxIngress is a set of kubernetes nginx ingress controller.
type NginxIngress struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// +optional
	Spec NginxIngressSpec

	// +optional
	Status NginxIngressStatus
}

type NginxIngressSpec struct {
	TenantID          string
	ClusterName       string
	Version           string
	VersionDeprecated NginxControllerVersion
}

type NginxControllerVersion struct {
	Controller string
}

type NginxIngressStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of the LogCollector of cluster.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type NginxIngressList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of NginxIngress
	Items []NginxIngress
}

type FilterThreshold struct {
	CpuAvgUsage5M float32
	CpuMaxUsage1H float32
	MemAvgUsage5M float32
	MemMaxUsage1H float32
}

type ScoreThreshold struct {
	CpuAvgUsage5M float32
	CpuMaxUsage1H float32
	CpuMaxUsage1D float32
	MemAvgUsage5M float32
	MemMaxUsage1H float32
	MemMaxUsage1D float32
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DynamicScheduler is a addon to extend native kube-scheduler functions
type DynamicScheduler struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec DynamicSchedulerSpec
	// +optional
	Status DynamicSchedulerStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DynamicSchedulerList is the whole list of all DynamicScheduler addon.
type DynamicSchedulerList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of DynamicScheduler
	Items []DynamicScheduler
}

// DynamicSchedulerSpec describes the attributes on a DynamicScheduler addon.
type DynamicSchedulerSpec struct {
	TenantID          string
	ClusterName       string
	Version           string
	PrometheusBackEnd string
	FilterThreshold   FilterThreshold
	ScoreThreshold    ScoreThreshold
}

// DynamicSchedulerStatus is information about the current status of a DynamicScheduler addon.
type DynamicSchedulerStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of DynamicScheduler.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CBS is a kubernetes csi for using CBS.
type CBS struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec CBSSpec
	// +optional
	Status CBSStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// CBSList is the whole list of all CBSs.
type CBSList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of CBSs
	Items []CBS
}

// CBSSpec describes the attributes on a CBS.
type CBSSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	Rootdir     string
}

// CBSStatus is information about the current status of a CBS.
type CBSStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of cbs.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NetworkPolicy is a addon to set network policy for Kubernetes.
type NetworkPolicy struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec NetworkPolicySpec
	// +optional
	Status NetworkPolicyStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NetworkPolicyList is the whole list of all NetworkPolicyList.
type NetworkPolicyList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of NetworkPolicy
	Items []NetworkPolicy
}

// NetworkPolicySpec describes the attributes on a NetworkPolicy.
type NetworkPolicySpec struct {
	TenantID    string
	ClusterName string
	Version     string
}

// NetworkPolicyStatus is information about the current status of a NetworkPolicy.
type NetworkPolicyStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of NetworkPolicy.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
}

type LoadThreshold struct {
	CpuAvgUsage5M  int32
	CpuTargetUsage int32
	MemAvgUsage5M  int32
	MemTargetUsage int32
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DynamicScheduler is a addon to extend native kube-scheduler functions
type DeScheduler struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec DeSchedulerSpec
	// +optional
	Status DeSchedulerStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// DeSchedulerList is the whole list of all DeScheduler addon.
type DeSchedulerList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of DeScheduler
	Items []DeScheduler
}

// DeSchedulerSpec describes the attributes on a DeScheduler addon.
type DeSchedulerSpec struct {
	TenantID          string
	ClusterName       string
	Version           string
	PrometheusBackEnd string
	LoadThreshold     LoadThreshold
}

// DeSchedulerStatus is information about the current status of a DeScheduler addon.
type DeSchedulerStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of DeScheduler.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OLMProxyOptions is the query options to a OLM proxy call.
type OLMProxyOptions struct {
	metav1.TypeMeta
	// +optional
	Namespace string
	// +optional
	Name string
	// +optional
	Action string
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OLM is a kubernetes Horizontal pod cron autoscaler
type OLM struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta

	// Spec defines the desired identities of clusters in this set.
	// +optional
	Spec OLMSpec
	// +optional
	Status OLMStatus
}

// OLMSpec defines the desired identities of OLM.
type OLMSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	ChartInfo   ChartSpec
	UIN         string
	SubUin      string
}

// OLMStatus is information about the current status of a OLM.
type OLMStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of cos.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
	// LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
	// +optional
	LastReInitializingTimestamp metav1.Time

	// Components is a generic status holder for the top level resource
	// +optional
	Components []ComponentStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// OLMList is the whole list of all OLM which owned by a tenant.
type OLMList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta

	// List of OLMs
	Items []OLM
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// Environment defines a context associating cluster and a bundle of selected apps to provide
// a environment for specific tasks, such like a.i. jobs, big data jobs, etc.
type Environment struct {
	metav1.TypeMeta
	metav1.ObjectMeta

	Spec   EnvironmentSpec
	Status EnvironmentStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type EnvironmentList struct {
	metav1.TypeMeta
	metav1.ListMeta

	Items []Environment
}

// EnvironmentType is the type of environment, which at this moment we only support AI environment
type EnvironmentType string

const (
	AIEnvironment EnvironmentType = "ai"
)

type AppSpec struct {
	Chart          string
	ChartFrom      string
	ChartNamespace string
	ReleaseName    string
}

type EnvironmentSpec struct {
	// EnvType defines what kind of environment is defined
	EnvType EnvironmentType

	// TenantID and ClusterName are used to locate the cluster that is bound to this Environment
	TenantID      string
	ClusterName   string
	UIN           string
	SubAccountUin string

	// Users customized the environment setting by customized what Apps shall be installed
	Apps []AppSpec
}

type EnvironmentStatus struct {
	// Supports maps the name of Apps to the kind of resources the environment
	// is now supporting due to the installation of the Apps
	Supports map[string]metav1.GroupVersionKind
}

// +genclient
// +genclient:nonNamespaced
// +genclient:skipVerbs=deleteCollection
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// QGPU is a better GPU manager for GPU sharing and isolation.
type QGPU struct {
	metav1.TypeMeta
	// +optional
	metav1.ObjectMeta
	// +optional
	Spec QGPUSpec
	// +optional
	Status QGPUStatus
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// QGPUList is the whole list of all QGPU.
type QGPUList struct {
	metav1.TypeMeta
	// +optional
	metav1.ListMeta
	// List of QGPU
	Items []QGPU
}

// QGPUSpec describes the attributes on a QGPUSpec.
type QGPUSpec struct {
	TenantID    string
	ClusterName string
	Version     string
	Priority    string
}

// QGPUStatus is information about the current status of a QGPUStatus.
type QGPUStatus struct {
	// +optional
	Version string
	// Phase is the current lifecycle phase of qgpu.
	// +optional
	Phase AddonPhase
	// Reason is a brief CamelCase string that describes any failure.
	// +optional
	Reason string
	// RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
	// +optional
	RetryCount int32
}
