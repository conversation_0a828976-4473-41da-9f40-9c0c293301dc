/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeScalerTasks implements ScalerTaskInterface
type FakeScalerTasks struct {
	Fake *FakeAutopilotV1beta1
	ns   string
}

var scalertasksResource = schema.GroupVersionResource{Group: "autopilot.k8s.io", Version: "v1beta1", Resource: "scalertasks"}

var scalertasksKind = schema.GroupVersionKind{Group: "autopilot.k8s.io", Version: "v1beta1", Kind: "ScalerTask"}

// Get takes name of the scalerTask, and returns the corresponding scalerTask object, and an error if there is any.
func (c *FakeScalerTasks) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.ScalerTask, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(scalertasksResource, c.ns, name), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}

// List takes label and field selectors, and returns the list of ScalerTasks that match those selectors.
func (c *FakeScalerTasks) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.ScalerTaskList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(scalertasksResource, scalertasksKind, c.ns, opts), &v1beta1.ScalerTaskList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1beta1.ScalerTaskList{ListMeta: obj.(*v1beta1.ScalerTaskList).ListMeta}
	for _, item := range obj.(*v1beta1.ScalerTaskList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested scalerTasks.
func (c *FakeScalerTasks) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(scalertasksResource, c.ns, opts))

}

// Create takes the representation of a scalerTask and creates it.  Returns the server's representation of the scalerTask, and an error, if there is any.
func (c *FakeScalerTasks) Create(ctx context.Context, scalerTask *v1beta1.ScalerTask, opts v1.CreateOptions) (result *v1beta1.ScalerTask, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(scalertasksResource, c.ns, scalerTask), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}

// Update takes the representation of a scalerTask and updates it. Returns the server's representation of the scalerTask, and an error, if there is any.
func (c *FakeScalerTasks) Update(ctx context.Context, scalerTask *v1beta1.ScalerTask, opts v1.UpdateOptions) (result *v1beta1.ScalerTask, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(scalertasksResource, c.ns, scalerTask), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeScalerTasks) UpdateStatus(ctx context.Context, scalerTask *v1beta1.ScalerTask, opts v1.UpdateOptions) (*v1beta1.ScalerTask, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(scalertasksResource, "status", c.ns, scalerTask), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}

// Delete takes name of the scalerTask and deletes it. Returns an error if one occurs.
func (c *FakeScalerTasks) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(scalertasksResource, c.ns, name), &v1beta1.ScalerTask{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeScalerTasks) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(scalertasksResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1beta1.ScalerTaskList{})
	return err
}

// Patch applies the patch and returns the patched scalerTask.
func (c *FakeScalerTasks) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.ScalerTask, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(scalertasksResource, c.ns, name, pt, data, subresources...), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}
