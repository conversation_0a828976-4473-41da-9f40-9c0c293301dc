/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2020 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package tkestack.io.tke.api.platform.v1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";
import "k8s.io/apimachinery/pkg/util/intstr/generated.proto";
import "tkestack.io/tke/api/application/v1/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1";

// AddonSpec describes the attributes on a Addon.
message AddonSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;
}

message App {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // +optional
  optional tkestack.io.tke.api.application.v1.AppSpec spec = 2;
}

message AuthzWebhookAddr {
  // +optional
  optional BuiltinAuthzWebhookAddr builtin = 1;

  // +optional
  optional ExternalAuthzWebhookAddr external = 2;
}

message BootstrapApp {
  optional App app = 1;
}

message BuiltinAuthzWebhookAddr {
}

// CSIOperator is a operator to manages CSI external components.
message CSIOperator {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of storage operator.
  // +optional
  optional CSIOperatorSpec spec = 2;

  // +optional
  optional CSIOperatorStatus status = 3;
}

message CSIOperatorFeature {
  optional string version = 1;
}

// CSIOperatorList is the whole list of all storage operators which owned by a tenant.
message CSIOperatorList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of storage operators.
  repeated CSIOperator items = 2;
}

// CSIOperatorSpec describes the attributes of a storage operator.
message CSIOperatorSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  // Version of the CSI operator.
  optional string version = 3;
}

// CSIOperatorStatus is information about the current status of a storage operator.
message CSIOperatorStatus {
  // +optional
  optional string version = 1;

  // StorageVendorVersion will be set to the config version of the storage vendor.
  // +optional
  optional string storageVendorVersion = 2;

  // Phase is the current lifecycle phase of the tapp controller of cluster.
  // +optional
  optional string phase = 3;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 4;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 5;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 6;
}

// CSIProxyOptions is the query options to a kube-apiserver proxy call for CSI crd object.
message CSIProxyOptions {
  optional string namespace = 1;

  optional string name = 2;
}

// Cluster is a Kubernetes cluster in
message Cluster {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional ClusterSpec spec = 2;

  // +optional
  optional ClusterStatus status = 3;
}

// ClusterAddon contains the Addon component for the current kubernetes cluster
message ClusterAddon {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of addons in this set.
  // +optional
  optional ClusterAddonSpec spec = 2;

  // +optional
  optional ClusterAddonStatus status = 3;
}

// ClusterAddonList is the whole list of all ClusterAddon.
message ClusterAddonList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of ClusterAddon
  repeated ClusterAddon items = 2;
}

// ClusterAddonSpec indicates the specifications of the ClusterAddon.
message ClusterAddonSpec {
  // Addon type, one of PersistentEvent or LogCollector etc.
  optional string type = 1;

  // AddonLevel is level of cluster addon.
  optional string level = 2;

  // Version
  optional string version = 3;
}

// ClusterAddonStatus is information about the current status of a ClusterAddon.
message ClusterAddonStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the addon of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;
}

// ClusterAddonType records the all addons of cluster available.
message ClusterAddonType {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Addon type, one of Helm, PersistentEvent or LogCollector etc.
  optional string type = 2;

  // AddonLevel is level of cluster addon.
  optional string level = 3;

  // LatestVersion is latest version of the addon.
  optional string latestVersion = 4;

  // Description is desc of the addon.
  optional string description = 5;

  repeated string compatibleClusterType = 6;
}

// ClusterAddonTypeList is a resource containing a list of ClusterAddonType objects.
message ClusterAddonTypeList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // +optional
  repeated ClusterAddonType items = 2;
}

// ClusterAddress contains information for the cluster's address.
message ClusterAddress {
  // Cluster address type, one of Public, ExternalIP or InternalIP.
  optional string type = 1;

  // The cluster address.
  optional string host = 2;

  optional int32 port = 3;

  optional string path = 4;
}

// ClusterApplyOptions is the query options to a kube-apiserver proxy call for cluster object.
message ClusterApplyOptions {
  // +optional
  optional bool notUpdate = 1;
}

// ClusterComponent records the number of copies of each component of the
// cluster master.
message ClusterComponent {
  optional string type = 1;

  optional ClusterComponentReplicas replicas = 2;
}

// ClusterComponentReplicas records the number of copies of each state of each
// component of the cluster master.
message ClusterComponentReplicas {
  optional int32 desired = 1;

  optional int32 current = 2;

  optional int32 available = 3;

  optional int32 updated = 4;
}

// ClusterCondition contains details for the current condition of this cluster.
message ClusterCondition {
  // Type is the type of the condition.
  optional string type = 1;

  // Status is the status of the condition.
  // Can be True, False, Unknown.
  optional string status = 2;

  // Last time we probed the condition.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastProbeTime = 3;

  // Last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // Unique, one-word, CamelCase reason for the condition's last transition.
  // +optional
  optional string reason = 5;

  // Human-readable message indicating details about last transition.
  // +optional
  optional string message = 6;
}

// ClusterCredential records the credential information needed to access the cluster.
message ClusterCredential {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  optional string tenantID = 2;

  optional string clusterName = 3;

  // For TKE in global reuse
  // +optional
  optional bytes etcdCACert = 4;

  // +optional
  optional bytes etcdCAKey = 5;

  // +optional
  optional bytes etcdAPIClientCert = 6;

  // +optional
  optional bytes etcdAPIClientKey = 7;

  // For connect the cluster
  // +optional
  optional bytes caCert = 8;

  // +optional
  optional bytes caKey = 9;

  // For kube-apiserver X509 auth
  // +optional
  optional bytes clientCert = 10;

  // For kube-apiserver X509 auth
  // +optional
  optional bytes clientKey = 11;

  // For kube-apiserver token auth
  // +optional
  optional string token = 12;

  // For kubeadm init or join
  // +optional
  optional string bootstrapToken = 13;

  // For kubeadm init or join
  // +optional
  optional string certificateKey = 14;

  // Username is the username for basic authentication to the kubernetes cluster.
  // +optional
  optional string username = 15;

  // Impersonate is the username to act-as.
  // +optional
  optional string as = 16;

  // ImpersonateGroups is the groups to imperonate.
  // +optional
  repeated string asGroups = 17;

  // ImpersonateUserExtra contains additional information for impersonated user.
  // +optional
  map<string, string> asUserExtra = 18;
}

// ClusterCredentialList is the whole list of all ClusterCredential which owned by a tenant.
message ClusterCredentialList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of clusters
  repeated ClusterCredential items = 2;
}

// ClusterFeature records the features that are enabled by the cluster.
message ClusterFeature {
  // +optional
  optional bool ipvs = 1;

  // +optional
  optional bool publicLB = 2;

  // +optional
  optional bool internalLB = 3;

  // +optional
  optional string gpuType = 4;

  // +optional
  optional bool enableMasterSchedule = 5;

  // +optional
  optional HA ha = 6;

  // +optional
  repeated string skipConditions = 7;

  // +optional
  repeated File files = 8;

  // +optional
  map<string, string> hooks = 9;

  // +optional
  optional CSIOperatorFeature csiOperator = 10;

  // For kube-apiserver authorization webhook
  // +optional
  optional AuthzWebhookAddr authzWebhookAddr = 11;

  // +optional
  optional bool enableMetricsServer = 12;

  // +optional
  optional bool ipv6DualStack = 13;

  // +optional
  optional bool enableCilium = 14;

  optional string containerRuntime = 15;

  // Upgrade control upgrade process.
  // +optional
  optional Upgrade upgrade = 22;
}

// ClusterGroupAPIResourceItem specifies the name of a resource and whether it is namespaced.
message ClusterGroupAPIResourceItem {
  // name is the plural name of the resource.
  optional string name = 1;

  // singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely.
  // The singularName is more correct for reporting status on a single item and both singular and plural are allowed
  // from the kubectl CLI interface.
  optional string singularName = 2;

  // namespaced indicates if a resource is namespaced or not.
  optional bool namespaced = 3;

  // group is the preferred group of the resource.  Empty implies the group of the containing resource list.
  // For subresources, this may have a different value, for example: Scale".
  optional string group = 4;

  // version is the preferred version of the resource.  Empty implies the version of the containing resource list
  // For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)".
  optional string version = 5;

  // kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')
  optional string kind = 6;

  // verbs is a list of supported kube verbs (this includes get, list, watch, create,
  // update, patch, delete, deletecollection, and proxy)
  repeated string verbs = 7;

  // shortNames is a list of suggested short names of the resource.
  repeated string shortNames = 8;

  // categories is a list of the grouped resources this resource belongs to (e.g. 'all')
  repeated string categories = 9;
}

// ClusterGroupAPIResourceItems contains the GKV for the current kubernetes cluster
message ClusterGroupAPIResourceItems {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // groupVersion is the group and version this APIResourceList is for.
  optional string groupVersion = 2;

  // resources contains the name of the resources and if they are namespaced.
  repeated ClusterGroupAPIResourceItem resources = 3;
}

// ClusterGroupAPIResourceItemsList is the whole list of all ClusterAPIResource.
message ClusterGroupAPIResourceItemsList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 2;

  // List of ClusterGroupAPIResourceItems
  repeated ClusterGroupAPIResourceItems items = 3;

  // Failed Group Error
  optional string failedGroupError = 4;
}

// ClusterGroupAPIResourceOptions is the query options.
message ClusterGroupAPIResourceOptions {
}

// ClusterList is the whole list of all clusters which owned by a tenant.
message ClusterList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of clusters
  repeated Cluster items = 2;
}

// ClusterMachine is the master machine definition of cluster.
message ClusterMachine {
  optional string ip = 1;

  optional int32 port = 2;

  optional string username = 3;

  // +optional
  optional bytes password = 4;

  // +optional
  optional bytes privateKey = 5;

  // +optional
  optional bytes passPhrase = 6;

  // +optional
  map<string, string> labels = 7;

  // If specified, the node's taints.
  // +optional
  repeated k8s.io.api.core.v1.Taint taints = 8;
}

// ClusterProperty records the attribute information of the cluster.
message ClusterProperty {
  // +optional
  optional int32 maxClusterServiceNum = 1;

  // +optional
  optional int32 maxNodePodNum = 2;

  // +optional
  map<string, string> oversoldRatio = 3;
}

// ClusterResource records the current available and maximum resource quota
// information for the cluster.
message ClusterResource {
  // Capacity represents the total resources of a cluster.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> capacity = 1;

  // Allocatable represents the resources of a cluster that are available for scheduling.
  // Defaults to Capacity.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> allocatable = 2;

  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> allocated = 3;
}

// ClusterSpec is a description of a cluster.
message ClusterSpec {
  // Finalizers is an opaque list of values that must be empty to permanently remove object from storage.
  // +optional
  repeated string finalizers = 1;

  optional string tenantID = 2;

  // +optional
  optional string displayName = 3;

  optional string type = 4;

  optional string version = 5;

  // +optional
  optional string networkType = 6;

  // +optional
  optional string networkDevice = 7;

  // +optional
  optional string clusterCIDR = 8;

  // ServiceCIDR is used to set a separated CIDR for k8s service, it's exclusive with MaxClusterServiceNum.
  // +optional
  optional string serviceCIDR = 19;

  // DNSDomain is the dns domain used by k8s services. Defaults to "cluster.local".
  optional string dnsDomain = 9;

  // +optional
  repeated string publicAlternativeNames = 10;

  // +optional
  optional ClusterFeature features = 11;

  // +optional
  optional ClusterProperty properties = 12;

  // +optional
  repeated ClusterMachine addresses = 13;

  // +optional
  map<string, string> dockerExtraArgs = 14;

  // +optional
  map<string, string> kubeletExtraArgs = 15;

  // +optional
  map<string, string> apiServerExtraArgs = 16;

  // +optional
  map<string, string> controllerManagerExtraArgs = 17;

  // +optional
  map<string, string> schedulerExtraArgs = 18;

  // ClusterCredentialRef for isolate sensitive information.
  // If not specified, cluster controller will create one;
  // If specified, provider must make sure is valid.
  // +optional
  optional k8s.io.api.core.v1.LocalObjectReference clusterCredentialRef = 20;

  // Etcd holds configuration for etcd.
  // +optional
  optional Etcd etcd = 21;

  // If true will use hostname as nodename, if false will use machine IP as nodename.
  // +optional
  optional bool hostnameAsNodename = 23;

  // +optional
  map<string, string> networkArgs = 24;

  // +optional
  repeated ClusterMachine scalingMachines = 25;

  // BootstrapApps will install apps during creating cluster
  // +optional
  repeated BootstrapApp bootstrapApps = 26;
}

// ClusterStatus represents information about the status of a cluster.
message ClusterStatus {
  // +optional
  optional bool locked = 1;

  // +optional
  optional string version = 2;

  // +optional
  optional string phase = 3;

  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ClusterCondition conditions = 4;

  // A human readable message indicating details about why the cluster is in this condition.
  // +optional
  optional string message = 5;

  // A brief CamelCase message indicating details about why the cluster is in this state.
  // +optional
  optional string reason = 6;

  // List of addresses reachable to the cluster.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ClusterAddress addresses = 7;

  // +optional
  optional ClusterResource resource = 9;

  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ClusterComponent components = 10;

  // +optional
  optional string serviceCIDR = 11;

  // +optional
  optional int32 nodeCIDRMaskSize = 12;

  // +optional
  optional string dnsIP = 13;

  // +optional
  repeated string registryIPs = 14;

  // +optional
  optional string secondaryServiceCIDR = 15;

  // +optional
  optional string clusterCIDR = 16;

  // +optional
  optional string secondaryClusterCIDR = 17;

  // +optional
  optional int32 nodeCIDRMaskSizeIPv4 = 18;

  // +optional
  optional int32 nodeCIDRMaskSizeIPv6 = 19;

  // +optional
  optional string kubeVendor = 20;
}

// ConfigMap holds configuration data for tke to consume.
message ConfigMap {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Data contains the configuration data.
  // Each key must consist of alphanumeric characters, '-', '_' or '.'.
  // Values with non-UTF-8 byte sequences must use the BinaryData field.
  // The keys stored in Data must not overlap with the keys in
  // the BinaryData field, this is enforced during validation process.
  // +optional
  map<string, string> data = 2;

  // BinaryData contains the binary data.
  // Each key must consist of alphanumeric characters, '-', '_' or '.'.
  // BinaryData can contain byte sequences that are not in the UTF-8 range.
  // The keys stored in BinaryData must not overlap with the ones in
  // the Data field, this is enforced during validation process.
  // +optional
  map<string, bytes> binaryData = 3;
}

// ConfigMapList is a resource containing a list of ConfigMap objects.
message ConfigMapList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of ConfigMaps.
  repeated ConfigMap items = 2;
}

// CronHPA is a new kubernetes workload.
message CronHPA {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of CronHPA.
  // +optional
  optional CronHPASpec spec = 2;

  // +optional
  optional CronHPAStatus status = 3;
}

// CronHPAList is the whole list of all CronHPAs which owned by a tenant.
message CronHPAList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of CronHPAs
  repeated CronHPA items = 2;
}

// CronHPAProxyOptions is the query options to a kube-apiserver proxy call.
message CronHPAProxyOptions {
  optional string namespace = 1;

  optional string name = 2;
}

// CronHPASpec describes the attributes on a CronHPA.
message CronHPASpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;
}

// CronHPAStatus is information about the current status of a CronHPA.
message CronHPAStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the CronHPA of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// Etcd contains elements describing Etcd configuration.
message Etcd {
  // Local provides configuration knobs for configuring the local etcd instance
  // Local and External are mutually exclusive
  optional LocalEtcd local = 1;

  // External describes how to connect to an external etcd cluster
  // Local and External are mutually exclusive
  optional ExternalEtcd external = 2;
}

message ExternalAuthzWebhookAddr {
  optional string ip = 1;

  optional int32 port = 2;
}

// ExternalEtcd describes an external etcd cluster.
// Kubeadm has no knowledge of where certificate files live and they must be supplied.
message ExternalEtcd {
  // Endpoints of etcd members. Required for ExternalEtcd.
  repeated string endpoints = 1;

  // CAFile is an SSL Certificate Authority file used to secure etcd communication.
  // Required if using a TLS connection.
  optional string caFile = 2;

  // CertFile is an SSL certification file used to secure etcd communication.
  // Required if using a TLS connection.
  optional string certFile = 3;

  // KeyFile is an SSL key file used to secure etcd communication.
  // Required if using a TLS connection.
  optional string keyFile = 4;
}

message File {
  optional string src = 1;

  // Only support regular file
  optional string dst = 2;
}

message HA {
  optional TKEHA tke = 1;

  optional ThirdPartyHA thirdParty = 2;
}

// LocalEtcd describes that kubeadm should run an etcd cluster locally
message LocalEtcd {
  // DataDir is the directory etcd will place its data.
  // Defaults to "/var/lib/etcd".
  optional string dataDir = 1;

  // ExtraArgs are extra arguments provided to the etcd binary
  // when run inside a static pod.
  map<string, string> extraArgs = 2;

  // ServerCertSANs sets extra Subject Alternative Names for the etcd server signing cert.
  repeated string serverCertSANs = 3;

  // PeerCertSANs sets extra Subject Alternative Names for the etcd peer signing cert.
  repeated string peerCertSANs = 4;
}

// Machine instance in Kubernetes cluster
message Machine {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of the Machine.
  // +optional
  optional MachineSpec spec = 2;

  // +optional
  optional MachineStatus status = 3;
}

// MachineAddress contains information for the machine's address.
message MachineAddress {
  // Machine address type, one of Public, ExternalIP or InternalIP.
  optional string type = 1;

  // The machine address.
  optional string address = 2;
}

// MachineCondition contains details for the current condition of this Machine.
message MachineCondition {
  // Type is the type of the condition.
  optional string type = 1;

  // Status is the status of the condition.
  // Can be True, False, Unknown.
  optional string status = 2;

  // Last time we probed the condition.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastProbeTime = 3;

  // Last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // Unique, one-word, CamelCase reason for the condition's last transition.
  // +optional
  optional string reason = 5;

  // Human-readable message indicating details about last transition.
  // +optional
  optional string message = 6;
}

// MachineList is the whole list of all machine in an cluster.
message MachineList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of clusters
  repeated Machine items = 2;
}

// MachineSpec is a description of machine.
message MachineSpec {
  // Finalizers is an opaque list of values that must be empty to permanently remove object from storage.
  // +optional
  repeated string finalizers = 1;

  optional string tenantID = 2;

  optional string clusterName = 3;

  optional string type = 4;

  optional string ip = 5;

  optional int32 port = 6;

  optional string username = 7;

  // +optional
  optional bytes password = 8;

  // +optional
  optional bytes privateKey = 9;

  // +optional
  optional bytes passPhrase = 10;

  // +optional
  map<string, string> labels = 11;

  // If specified, the node's taints.
  // +optional
  repeated k8s.io.api.core.v1.Taint taints = 12;
}

// MachineStatus represents information about the status of an machine.
message MachineStatus {
  // +optional
  optional bool locked = 1;

  // +optional
  optional string phase = 2;

  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated MachineCondition conditions = 3;

  // A human readable message indicating details about why the machine is in this condition.
  // +optional
  optional string message = 4;

  // A brief CamelCase message indicating details about why the machine is in this state.
  // +optional
  optional string reason = 5;

  // List of addresses reachable to the machine.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated MachineAddress addresses = 6;

  // Set of ids/uuids to uniquely identify the node.
  // +optional
  optional MachineSystemInfo machineInfo = 7;
}

// MachineSystemInfo is a set of ids/uuids to uniquely identify the node.
message MachineSystemInfo {
  // MachineID reported by the node. For unique machine identification
  // in the cluster this field is preferred. Learn more from man(5)
  // machine-id: http://man7.org/linux/man-pages/man5/machine-id.5.html
  optional string machineID = 1;

  // SystemUUID reported by the node. For unique machine identification
  // MachineID is preferred. This field is specific to Red Hat hosts
  // https://access.redhat.com/documentation/en-US/Red_Hat_Subscription_Management/1/html/RHSM/getting-system-uuid.html
  optional string systemUUID = 2;

  // Boot ID reported by the node.
  optional string bootID = 3;

  // Kernel Version reported by the node.
  optional string kernelVersion = 4;

  // OS Image reported by the node.
  optional string osImage = 5;

  // ContainerRuntime Version reported by the node.
  optional string containerRuntimeVersion = 6;

  // Kubelet Version reported by the node.
  optional string kubeletVersion = 7;

  // KubeProxy Version reported by the node.
  optional string kubeProxyVersion = 8;

  // The Operating System reported by the node
  optional string operatingSystem = 9;

  // The Architecture reported by the node
  optional string architecture = 10;
}

// PersistentBackEnd indicates the backend type and attributes of the persistent
// log store.
message PersistentBackEnd {
  optional StorageBackEndCLS cls = 1;

  optional StorageBackEndES es = 2;
}

// PersistentEvent is a recorder of kubernetes event.
message PersistentEvent {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional PersistentEventSpec spec = 2;

  // +optional
  optional PersistentEventStatus status = 3;
}

// PersistentEventList is the whole list of all clusters which owned by a tenant.
message PersistentEventList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of PersistentEvents
  repeated PersistentEvent items = 2;
}

// PersistentEventSpec describes the attributes on a PersistentEvent.
message PersistentEventSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional PersistentBackEnd persistentBackEnd = 3;

  optional string version = 4;
}

// PersistentEventStatus is information about the current status of a
// PersistentEvent.
message PersistentEventStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the persistent event of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// ProxyOptions is the query options to a proxy call.
message ProxyOptions {
  // Path is the URL path to use for the current proxy request.
  // +optional
  optional string path = 1;
}

// Registry records the third-party image repository information stored by the
// user.
message Registry {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // +optional
  optional RegistrySpec spec = 2;
}

// RegistryList is a resource containing a list of Registry objects.
message RegistryList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // +optional
  repeated Registry items = 2;
}

// RegistrySpec indicates the specifications of the third-party image repository.
message RegistrySpec {
  // +optional
  optional string tenantID = 1;

  // +optional
  optional string displayName = 2;

  // +optional
  optional string clusterName = 3;

  // +optional
  optional string url = 4;

  // +optional
  optional string userName = 5;

  // +optional
  optional string password = 6;
}

// ResourceRequirements describes the compute resource requirements.
message ResourceRequirements {
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> limits = 1;

  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> requests = 2;
}

// StorageBackEndCLS records the attributes required when the backend storage
// type is CLS.
message StorageBackEndCLS {
  optional string logSetID = 1;

  optional string topicID = 2;
}

// StorageBackEndES records the attributes required when the backend storage
// type is ElasticSearch.
message StorageBackEndES {
  optional string ip = 1;

  optional int32 port = 2;

  optional string scheme = 3;

  optional string indexName = 4;

  optional string user = 5;

  optional string password = 6;

  optional int32 reserveDays = 7;
}

message TKEHA {
  optional string vip = 1;

  optional int32 vrid = 2;
}

// TappController is a new kubernetes workload.
message TappController {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of tapp controller.
  // +optional
  optional TappControllerSpec spec = 2;

  // +optional
  optional TappControllerStatus status = 3;
}

// TappControllerList is the whole list of all tapp controllers which owned by a tenant.
message TappControllerList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of tapp controllers
  repeated TappController items = 2;
}

// TappControllerProxyOptions is the query options to a kube-apiserver proxy call.
message TappControllerProxyOptions {
  optional string namespace = 1;

  optional string name = 2;

  optional string action = 3;
}

// TappControllerSpec describes the attributes on a tapp controller.
message TappControllerSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;
}

// TappControllerStatus is information about the current status of a tapp controller.
message TappControllerStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the tapp controller of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

message ThirdPartyHA {
  optional string vip = 1;

  optional int32 vport = 2;
}

message Upgrade {
  // Upgrade mode, default value is Auto.
  // +optional
  optional string mode = 1;

  // Upgrade strategy config.
  // +optional
  optional UpgradeStrategy strategy = 2;
}

// UpgradeStrategy used to control the upgrade process.
message UpgradeStrategy {
  // The maximum number of pods that can be unready during the upgrade.
  // 0% means all pods need to be ready after evition.
  // 100% means ignore any pods unready which may be used in one worker node, use this carefully!
  // default value is 0%.
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxUnready = 1;

  // Whether drain node before upgrade.
  // Draining node before upgrade is recommended.
  // But not all pod running as cows, a few running as pets.
  // If your pod can not accept be expelled from current node, this value should be false.
  // +optional
  optional bool drainNodeBeforeUpgrade = 2;
}

