package batchStrategy

import (
	"context"
	"strings"

	tadclientset "git.woa.com/tad-dev/api/generated/clientset/versioned"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

type AppFabricStrategy struct{}

func (a *AppFabricStrategy) DivideBatches(task model.Task, applications []util.Application) ([]BatchConfig, error) {
	// 对于AppFabric组件，取第一个集群ID
	var clusterId string
	if len(task.Cluster) > 0 {
		// 提取纯集群ID（去掉可能的命名空间部分）
		firstCluster := strings.TrimSpace(task.Cluster[0])
		parts := strings.Fields(firstCluster)
		if len(parts) > 0 {
			clusterId = parts[0]
		}
	}

	restConfig, err := util.GetRestConfig(clusterId, task.UserData.Token, task.UserData.User)
	if err != nil {
		return nil, err
	}
	client, err := tadclientset.NewForConfig(restConfig)
	if err != nil {
		return nil, err
	}
	appClusters := make(map[string][]util.Application)

	for _, application := range applications {
		app, err := client.CoreV1().Applications(application.Namespace).Get(context.TODO(), application.Name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		var region string
		for _, policy := range app.Spec.Policies {
			if policy.Type == "placement" {
				region = policy.Name
				break
			}
		}
		appClusters[region] = append(appClusters[region], application)
	}

	if len(appClusters) == 0 {
		return nil, nil
	}

	var batches []BatchConfig
	// 按照地域优先级对应用进行分组和排序
	priorityRegionGroups := make(map[int]map[string][]util.Application)
	for region, apps := range appClusters {
		shortRegion := util.GetShortRegion(region)
		// 获取地域的优先级，如果未定义则默认为最低优先级6
		priority := RegionBatchMap[shortRegion]
		if priority == 0 {
			priority = 6
		}

		// 初始化该优先级的地域map
		if priorityRegionGroups[priority] == nil {
			priorityRegionGroups[priority] = make(map[string][]util.Application)
		}

		// 按地域分组存储应用
		priorityRegionGroups[priority][shortRegion] = apps
	}

	// 按优先级从高到低排序（1->5）并设置不同的间隔时间
	for priority := 1; priority <= 6; priority++ {
		if regionGroups, exists := priorityRegionGroups[priority]; exists {
			interval := RegionInterval20Min
			// 第三批和第五批使用1小时间隔
			if priority == 3 || priority == 5 {
				interval = RegionInterval1Hour
			}

			// 将每个地域的应用单独作为一个批次
			for region, apps := range regionGroups {
				batches = append(batches, BatchConfig{
					Region:       region,
					Applications: apps,
					Interval:     int64(interval),
				})
			}
		}
	}
	return batches, nil
}
