package batchStrategy

import (
	"fmt"
	"sort"

	"git.woa.com/kmetis/starship-engine/pkg/clusterinfo"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

const (
	EksRunningPodsStrategyName = "EksRunningPodsStrategy"
)

var (
	defaultEksPodThresholds = []int{5, 20, 50}
)

type EksRunningPodsStrategy struct {
	Name string
	ID   int64
}

func NewEksRunningPodsStrategy() *EksRunningPodsStrategy {
	return &EksRunningPodsStrategy{
		Name: EksRunningPodsStrategyName,
		ID:   EksRunningPodsStrategyID,
	}
}

func (e *EksRunningPodsStrategy) DivideBatches(batches []BatchConfig, isMainBatch bool, options BatchStrategyOptions) ([]BatchConfig, error) {
	if isMainBatch && len(batches) > 1 {
		return nil, fmt.Errorf("main batch should have at most one batch")
	}
	// eks pod分批阈值
	thresholds := options.EksPodThresholds
	if len(thresholds) == 0 {
		thresholds = defaultEksPodThresholds
	}
	sort.Ints(thresholds)

	newBatches := make([]BatchConfig, 0)
	var err error
	for _, batch := range batches {
		if len(batch.Clusters) == 0 {
			continue
		}
		batchPriorityGroups := make(map[int][]string)
		for _, cluster := range batch.Clusters {
			region := batch.Region
			if region == "" {
				// 优先使用缓存版本获取region
				region, err = util.GetRegionAliasFromClusterIDWithCache(cluster)
				if err != nil {
					// 如果缓存版本失败，降级到原版本
					region, err = util.GetRegionAliasFromClusterID(cluster)
					if err != nil {
						return nil, fmt.Errorf("failed to get region for cluster %s: %v", cluster, err)
					}
				}
			}

			runningPods, err := e.GetClusterEksPods(cluster, region)
			if err != nil {
				return nil, fmt.Errorf("get eks running pods failed, cluster: %s, err: %v", cluster, err)
			}
			for i, v := range thresholds {
				if runningPods <= int64(v) {
					if _, ok := batchPriorityGroups[i]; !ok {
						batchPriorityGroups[i] = make([]string, 0)
					}
					batchPriorityGroups[i] = append(batchPriorityGroups[i], cluster)
					break
				}
				if i == len(thresholds)-1 {
					if _, ok := batchPriorityGroups[i+1]; !ok {
						batchPriorityGroups[i+1] = make([]string, 0)
					}
					batchPriorityGroups[i+1] = append(batchPriorityGroups[i+1], cluster)
				}
			}
		}

		index := 0
		for i := 0; i < len(thresholds)+1; i++ {
			if len(batchPriorityGroups[i]) > 0 {
				index++
				batchId := batch.BatchId
				if isMainBatch {
					batchId = int64(index)
				}
				newBatches = append(newBatches, BatchConfig{
					Region:   batch.Region,
					Clusters: batchPriorityGroups[i],
					BatchId:  batchId,
				})
			}
		}
	}

	return newBatches, nil
}

func (e *EksRunningPodsStrategy) GetClusterEksPods(cluster, region string) (int64, error) {
	helper, err := clusterinfo.GetClsInfoHelper()
	if err != nil {
		return 0, err
	}
	return helper.GetEksPodNumber(cluster)
}

func (e *EksRunningPodsStrategy) GetName() string {
	return EksRunningPodsStrategyName
}

func (e *EksRunningPodsStrategy) GetID() int64 {
	return EksRunningPodsStrategyID
}
