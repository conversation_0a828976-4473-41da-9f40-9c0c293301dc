package handler

import (
	"strings"
	"testing"
	"time"

	"git.woa.com/kmetis/starship-engine/api/v1/task"
)

func TestGenerateCSV(t *testing.T) {
	// 创建测试数据
	clusters := []*task.ClusterInfo{
		{
			ClusterId:   "cls-test123",
			MetaId:      "meta-test123",
			Type:        "tke",
			Region:      "ap-guangzhou",
			Status:      "success",
			Stage:       "publish",
			Reason:      "操作成功",
			ClusterLink: "https://tke.woa.com/cluster/cls-test123",
			MonitorLink: "https://panel.woa.com/monitor/cls-test123",
			LogLink:     "https://tke.woa.com/logs/cls-test123",
			CreateTime:  time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			UpdateTime:  time.Date(2024, 1, 1, 11, 0, 0, 0, time.UTC),
		},
		{
			ClusterId:   "cls-test456",
			MetaId:      "meta-test456",
			Type:        "eks",
			Region:      "ap-shanghai",
			Status:      "failed",
			Stage:       "precheck",
			Reason:      "预检查失败",
			ClusterLink: "https://tke.woa.com/cluster/cls-test456",
			MonitorLink: "https://panel.woa.com/monitor/cls-test456",
			LogLink:     "https://tke.woa.com/logs/cls-test456",
			CreateTime:  time.Date(2024, 1, 2, 10, 0, 0, 0, time.UTC),
			UpdateTime:  time.Date(2024, 1, 2, 11, 0, 0, 0, time.UTC),
		},
	}

	// 生成CSV
	csvContent, err := generateCSV(clusters)
	if err != nil {
		t.Fatalf("generateCSV failed: %v", err)
	}

	// 验证CSV内容
	lines := strings.Split(csvContent, "\n")
	if len(lines) < 3 { // 头部 + 2行数据 + 空行
		t.Fatalf("Expected at least 3 lines, got %d", len(lines))
	}

	// 验证头部
	expectedHeader := "集群ID,Meta ID,集群类型,地域,状态,阶段,原因,集群链接,监控链接,日志链接,创建时间,更新时间"
	if lines[0] != expectedHeader {
		t.Errorf("Expected header: %s, got: %s", expectedHeader, lines[0])
	}

	// 验证第一行数据
	expectedFirstLine := "cls-test123,meta-test123,tke,ap-guangzhou,success,publish,操作成功,https://tke.woa.com/cluster/cls-test123,https://panel.woa.com/monitor/cls-test123,https://tke.woa.com/logs/cls-test123,2024-01-01 10:00:00,2024-01-01 11:00:00"
	if lines[1] != expectedFirstLine {
		t.Errorf("Expected first line: %s, got: %s", expectedFirstLine, lines[1])
	}

	// 验证第二行数据
	expectedSecondLine := "cls-test456,meta-test456,eks,ap-shanghai,failed,precheck,预检查失败,https://tke.woa.com/cluster/cls-test456,https://panel.woa.com/monitor/cls-test456,https://tke.woa.com/logs/cls-test456,2024-01-02 10:00:00,2024-01-02 11:00:00"
	if lines[2] != expectedSecondLine {
		t.Errorf("Expected second line: %s, got: %s", expectedSecondLine, lines[2])
	}
}

func TestGenerateCSV_EmptyData(t *testing.T) {
	// 测试空数据
	clusters := []*task.ClusterInfo{}

	csvContent, err := generateCSV(clusters)
	if err != nil {
		t.Fatalf("generateCSV failed: %v", err)
	}

	// 验证只有头部
	lines := strings.Split(csvContent, "\n")
	if len(lines) < 2 { // 头部 + 空行
		t.Fatalf("Expected at least 2 lines, got %d", len(lines))
	}

	// 验证头部存在
	expectedHeader := "集群ID,Meta ID,集群类型,地域,状态,阶段,原因,集群链接,监控链接,日志链接,创建时间,更新时间"
	if lines[0] != expectedHeader {
		t.Errorf("Expected header: %s, got: %s", expectedHeader, lines[0])
	}
}

func TestGenerateCSV_SpecialCharacters(t *testing.T) {
	// 测试包含特殊字符的数据
	clusters := []*task.ClusterInfo{
		{
			ClusterId:   "cls-test123",
			MetaId:      "meta-test123",
			Type:        "tke",
			Region:      "ap-guangzhou",
			Status:      "failed",
			Stage:       "publish",
			Reason:      "错误信息包含逗号,引号\"和换行\n符号",
			ClusterLink: "https://tke.woa.com/cluster/cls-test123",
			MonitorLink: "https://panel.woa.com/monitor/cls-test123",
			LogLink:     "https://tke.woa.com/logs/cls-test123",
			CreateTime:  time.Date(2024, 1, 1, 10, 0, 0, 0, time.UTC),
			UpdateTime:  time.Date(2024, 1, 1, 11, 0, 0, 0, time.UTC),
		},
	}

	// 生成CSV
	csvContent, err := generateCSV(clusters)
	if err != nil {
		t.Fatalf("generateCSV failed: %v", err)
	}

	// 验证CSV内容包含特殊字符处理（CSV会自动转义特殊字符）
	if !strings.Contains(csvContent, "错误信息包含逗号") {
		t.Error("CSV should contain the reason text")
	}

	// 打印实际内容以便调试
	t.Logf("Generated CSV content: %s", csvContent)
}
