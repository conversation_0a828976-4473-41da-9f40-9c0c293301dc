package db

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/api/v1/task"
	"git.woa.com/kmetis/starship-engine/pkg/batchStrategy"
	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/ruleengine"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

// BatchTerminateTasksResult 批量终止任务的结果
type BatchTerminateTasksResult struct {
	SuccessCount int
	SkippedCount int
	FailedCount  int
	Results      []TaskTerminateResult
}

// TaskTerminateResult 单个任务终止结果
type TaskTerminateResult struct {
	TaskId   int64
	TaskName string
	Status   string
	Message  string
}

func CreateTask(task *Task) error {
	// 检查任务名称是否已存在
	exists, err := checkTaskNameExists(task.AppAction, task.TaskName)
	if err != nil {
		return err
	}
	if exists {
		return errors.New("task name already exists, cannot create duplicate task")
	}

	err = createOrUpdateTasks([]*Task{task})
	if err != nil {
		return err
	}
	return nil
}

func createOrUpdateTasks(task []*Task) error {
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	tx := DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	result := tx.Save(task)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func checkTaskNameExists(appAction, taskName string) (bool, error) {
	var count int64
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	if err := DB.WithContext(ctx).Model(&Task{}).Where("app_action =? AND task_name =?", appAction, taskName).
		Count(&count).Error; err != nil {
		klog.V(1).ErrorS(err, "failed to check task name existence.")
		return false, err
	}

	if count > 0 {
		fmt.Println(fmt.Sprintf("%s %s is already exists", appAction, taskName))
		return true, nil
	}

	return false, nil
}

func UpdateTaskByTaskId(TaskId int64, updates map[string]interface{}) error {
	// 开始事务
	tx := DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 更新 Task 表中相应 TaskId 的多个字段
	result := tx.Model(&Task{}).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id =?", TaskId).
		Updates(updates)
	if result.Error != nil {
		tx.Rollback()
		klog.Errorf("Failed to update status for TaskId: %d: %v", TaskId, result.Error)
		return result.Error
	}

	if err := tx.Commit().Error; err != nil {
		klog.Errorf("Failed to commit transaction for TaskId: %d: %v", TaskId, err)
		return err
	}
	return nil
}

func GetTaskByFields(fields map[string]interface{}) ([]*Task, error) {
	var tasks []*Task
	// 使用 gorm 的 Where 方法根据多个字段获取任务信息
	result := DB.Where(fields).Where("batch_type > 0").Find(&tasks)
	if result.Error != nil {
		klog.Errorf("Failed to fetch tasks by fields: %v: %v", fields, result.Error)
		return nil, result.Error
	}
	return tasks, nil
}

func GetTaskByTaskId(TaskId int64) (*Task, error) {
	var task Task
	// 使用 gorm 的 First 方法根据 TaskId 获取任务信息
	result := DB.First(&task, TaskId)
	if result.Error != nil {
		klog.Errorf("Failed to fetch task with TaskId: %d: %v", TaskId, result.Error)
		return nil, result.Error
	}
	return &task, nil
}

func GetTaskIDByTaskName(taskName string) (int64, error) {
	var task Task
	// 使用 First 方法查找符合条件的第一个任务
	result := DB.Where("task_name =?", taskName).First(&task)
	if result.Error != nil {
		klog.ErrorS(result.Error, "failed to get task id by task name.")
		return 0, result.Error
	}
	return task.ID, nil
}

func GetTaskByTaskName(taskName string) (*Task, error) {
	var task Task
	// 使用 First 方法查找符合条件的第一个任务
	result := DB.Where("task_name =?", taskName).First(&task)
	if result.Error != nil {
		klog.ErrorS(result.Error, "failed to get task id by task name.")
		return nil, result.Error
	}
	return &task, nil
}

func UpdateStatusByName(appAction, taskName, status string) error {
	// 先查找是否存在该记录
	var task *Task
	err := DB.Where("app_action =? AND task_name =?", appAction, taskName).Find(&task).Error
	if err != nil {
		return err
	}
	if task == nil || task.ID == 0 {
		fmt.Println("No resource found.")
		return nil
	}

	// 如果当前状态与目标状态相同，直接返回
	if task.Status == status {
		return nil
	}

	return UpdateStatusByID(task.ID, status)
}

func UpdateStatusByID(taskId int64, status string) error {
	// 先查找是否存在该记录
	var t *Task
	err := DB.Where(" id = ?", taskId).Find(&t).Error
	if err != nil {
		return err
	}
	if t == nil || t.ID == 0 {
		fmt.Println("No resource found.")
		return nil
	}

	// 如果当前状态与目标状态相同，直接返回
	if t.Status == status {
		return nil
	}

	canUpdate := false
	switch status {

	case consts.PREPARE:
		canUpdate = t.Status == consts.CREATED

	case consts.RUNNING:
		canUpdate = t.Status == consts.PAUSE_STA || t.Status == consts.BATCH_COMPLETED

	case consts.PAUSE_STA:
		canUpdate = t.Status == consts.RUNNING

	case consts.COMPLETED:
		canUpdate = t.Status == consts.RUNNING

	case consts.BATCH_FAILED:
		canUpdate = t.Status == consts.PREPARE

	case consts.TERMINATED:
		// 可以从任何状态终止任务，除了已经完成的状态
		canUpdate = t.Status != consts.COMPLETED && t.Status != consts.TERMINATED

	default:

	}

	if !canUpdate {
		return fmt.Errorf("Can not update status from %s to %s", t.Status, status)
	}

	// 开始事务
	tx := DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	result := tx.Model(&Task{}).
		Where(" id = ?", taskId).
		Update("status", status)
	if result.Error != nil {
		tx.Rollback()
		klog.V(1).ErrorS(result.Error, "failed to update status for TaskId.")
		return result.Error
	}

	if err = tx.Commit().Error; err != nil {
		klog.V(1).ErrorS(err, "failed to commit transaction for TaskId.")
		return err
	}
	return nil
}
func GetTaskList(req task.ListRequest, offset, limit int) ([]task.DetailResponse, int64, error) {
	var tasks []*Task
	var total int64

	query := DB.Model(&Task{})

	if req.TaskName != "" {
		query = query.Where("task_name LIKE ?", "%"+req.TaskName+"%")
	}
	if req.AppAction != "" {
		query = query.Where("app_action = ?", req.AppAction)
	}
	if req.Component != "" {
		query = query.Where("component = ?", req.Component)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.ClusterId != "" {
		var taskIDs []string
		if err := DB.Model(&Cluster{}).Where("cluster_id = ?", req.ClusterId).Pluck("task_id", &taskIDs).Error; err != nil {
			klog.Errorf("Failed to fetch task IDs from Cluster table: %v", err)
			return nil, 0, err
		}
		if len(taskIDs) > 0 {
			query = query.Where("id IN (?)", taskIDs)
		} else {
			return []task.DetailResponse{}, 0, nil
		}
	}

	loadOpsTeamMap, err := util.LoadMap("/etc/config/OpsTeam")
	if err != nil {
		fmt.Printf("Failed to load OpsTeam map: %v\n", err)
		return []task.DetailResponse{}, 0, err
	}

	if req.User != "" {
		var userTeams []string
		for team, users := range loadOpsTeamMap {
			for _, user := range users {
				if user == req.User {
					userTeams = append(userTeams, team)
					break
				}
			}
		}
		if len(userTeams) == 0 {
			return []task.DetailResponse{}, 0, nil
		}
		query = query.Where("component IN (?)", userTeams)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		klog.Errorf("Failed to count tasks: %v", err)
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Order("id DESC").
		Offset(offset).
		Limit(limit).
		Find(&tasks).Error; err != nil {
		klog.Errorf("Failed to fetch task list: %v", err)
		return nil, 0, err
	}

	progressMap, err := getTaskProgress(tasks)
	if err != nil {
		klog.Errorf("Failed to get task progress: %v", err)
		return nil, 0, err
	}

	var resp []task.DetailResponse
	for _, t := range tasks {
		resp = append(resp, task.DetailResponse{
			TaskName:     t.TaskName,
			Description:  t.Description,
			AppAction:    t.AppAction,
			Component:    t.Component,
			ImageVersion: t.ImageTag,
			AddonVersion: t.AppVersion,
			User:         t.User,
			Status:       t.Status,
			CreateTime:   t.CreateTime,
			ChangeID:     t.ChangeId,
			TaskID:       t.ID,
			Progress:     progressMap[t.ID],
		})
	}

	return resp, total, nil
}

// 查询分批中的任务列表
func GetInBatchTask() (*Task, error) {
	var t Task
	tx := DB.Model(&Task{}).Where("status = ?", consts.PREPARE).First(&t)
	if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &t, tx.Error
}

func SaveBatchesAndUpdateTaskStatus(taskId int64, batches []batchStrategy.BatchConfig) error {
	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	taskStatus := consts.COMPLETED
	if len(batches) > 0 {
		dbBatches := make([]*Batch, 0)
		allDbClusters := make([]*Cluster, 0)

		for _, batchConfig := range batches {
			batch := &Batch{
				TaskId:     taskId,
				BatchId:    batchConfig.BatchId,
				SubBatchId: batchConfig.SubBatchId,
				Region:     batchConfig.Region,
				StartTime:  nil,
				EndTime:    nil,
				Interval:   batchConfig.Interval,
			}
			dbBatches = append(dbBatches, batch)
			for _, clusterId := range batchConfig.Clusters {
				cluster := &Cluster{
					TaskId:     taskId,
					ClusterId:  clusterId,
					BatchId:    batchConfig.BatchId,
					SubBatchId: batchConfig.SubBatchId,
					Region:     batchConfig.Region,
				}
				allDbClusters = append(allDbClusters, cluster)
			}
		}

		// 创建批次记录
		if err := tx.Create(&dbBatches).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create batches: %v", err)
		}

		// 创建集群记录
		if err := tx.Create(&allDbClusters).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create clusters: %v", err)
		}
		taskStatus = consts.BATCH_COMPLETED
	}
	// 更新任务状态
	result := tx.Model(&Task{}).
		Where(" id = ?", taskId).
		Update("status", taskStatus)
	if result.Error != nil {
		tx.Rollback()
		klog.V(1).ErrorS(result.Error, "failed to update status for TaskId.")
		return result.Error
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to commit transaction: %v", err)
	}
	return nil
}

func SaveAppStrategyBatchesAndUpdateTaskStatus(taskId int64, batches []*ruleengine.ClusterBatch) error {
	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	taskStatus := consts.COMPLETED
	if len(batches) > 0 {
		dbBatches := make([]*Batch, 0)
		allDbClusters := make([]*Cluster, 0)

		for _, batchConfig := range batches {
			batch := &Batch{
				TaskId:     taskId,
				BatchId:    batchConfig.BatchId,
				SubBatchId: batchConfig.SubBatchId,
				Region:     batchConfig.BatchName,
				StartTime:  nil,
				EndTime:    nil,
				Interval:   batchConfig.Interval,
			}
			dbBatches = append(dbBatches, batch)
			for _, cls := range batchConfig.Clusters {
				appid, _ := strconv.ParseInt(cls.AppID, 10, 64)
				cluster := &Cluster{
					TaskId:     taskId,
					ClusterId:  cls.ClusterID,
					BatchId:    batchConfig.BatchId,
					SubBatchId: batchConfig.SubBatchId,
					MetaID:     cls.MetaClusterID,
					Region:     cls.Region,
					Type:       cls.ClusterType,
					AppID:      uint64(appid),
					UIN:        cls.UIN,
					SubUIN:     cls.SubAccountUIN,
					//ClusterVersion:   cls.K8sVersion,
					//ClusterLevel:     cls.ClusterLevel,
					//ClusterCreatedAt: cls.ClusterCreatedAt,
				}
				allDbClusters = append(allDbClusters, cluster)
			}
		}

		// 创建批次记录
		if err := tx.Create(&dbBatches).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create batches: %v", err)
		}

		// 创建集群记录
		if err := tx.CreateInBatches(&allDbClusters, 1000).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create clusters: %v", err)
		}
		taskStatus = consts.BATCH_COMPLETED
	}
	// 更新任务状态
	result := tx.Model(&Task{}).
		Where(" id = ?", taskId).
		Update("status", taskStatus)
	if result.Error != nil {
		tx.Rollback()
		klog.V(1).ErrorS(result.Error, "failed to update status for TaskId.")
		return result.Error
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to commit transaction: %v", err)
	}
	return nil
}

// 是否应该暂停下一批发布
func ShouldSuspendNextBatch(taskId, batchId, subBatchId int64) (bool, error) {
	var t Task
	err := DB.Where("id = ?", taskId).First(&t).Error
	if err != nil {
		return false, fmt.Errorf("failed to get task %d: %v", taskId, err)
	}
	if t.AppAction != consts.PUBLISH || t.AutoConfirm {
		return false, nil
	}
	var n int64
	if err = DB.Model(&Batch{}).Where("task_id = ? AND batch_id = ? AND sub_batch_id = ?", taskId, batchId, subBatchId+1).Count(&n).Error; err != nil {
		return false, fmt.Errorf("failed to count subbatch, taskId: %d, batchId %d, subBatchId: %d, err: %v", taskId, batchId, subBatchId+1, err)
	} else if n > 0 {
		return false, nil
	}

	if err = DB.Model(&Batch{}).Where("task_id = ? AND batch_id = ?", taskId, batchId+1).Count(&n).Error; err != nil {
		return false, fmt.Errorf("failed to count subbatch, taskId: %d, batchId %d, subBatchId: %d, err: %v", taskId, batchId+1, subBatchId, err)
	}
	return n > 0, nil
}

func getTaskProgress(tasks []*Task) (map[int64]float32, error) {
	progress := make(map[int64]float32)
	if len(tasks) == 0 {
		return progress, nil
	}

	// 提取 taskid 列表
	taskIDs := make([]int64, 0, len(tasks))
	for _, task := range tasks {
		taskIDs = append(taskIDs, task.ID)
	}

	// 批量查询 Cluster 表，直接计算每个 taskid 的进度
	var rows []struct {
		TaskID   int64
		Progress float32
	}
	err := DB.Model(&Cluster{}).
		Select("task_id, SUM(CASE WHEN status IN ('success', 'failed') THEN 1.0 ELSE 0.0 END) / COUNT(*) AS progress").
		Where("task_id IN (?)", taskIDs).
		Group("task_id").
		Find(&rows).Error
	if err != nil {
		klog.ErrorS(err, "Failed to query cluster progress")
		return progress, err
	}

	// 填充结果
	for _, row := range rows {
		progress[row.TaskID] = row.Progress
	}

	return progress, nil
}

// BatchTerminateTasksByIds 批量终止任务
func BatchTerminateTasksByIds(taskIds []int64) (*BatchTerminateTasksResult, error) {
	result := &BatchTerminateTasksResult{
		Results: make([]TaskTerminateResult, 0, len(taskIds)),
	}

	// 开始事务
	tx := DB.Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 批量查询任务信息
	var tasks []*Task
	err := tx.Where("id IN (?)", taskIds).Find(&tasks).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to query tasks: %v", err)
	}

	// 创建任务ID到任务的映射
	taskMap := make(map[int64]*Task)
	for _, task := range tasks {
		taskMap[task.ID] = task
	}

	// 处理每个任务ID
	for _, taskId := range taskIds {
		taskResult := TaskTerminateResult{
			TaskId: taskId,
		}

		task, exists := taskMap[taskId]
		if !exists {
			taskResult.Status = "not_found"
			taskResult.Message = "Task not found"
			result.Results = append(result.Results, taskResult)
			result.FailedCount++
			continue
		}

		taskResult.TaskName = task.TaskName

		// 检查任务状态
		if task.Status == consts.COMPLETED {
			taskResult.Status = "skipped"
			taskResult.Message = "Task already completed"
			result.Results = append(result.Results, taskResult)
			result.SkippedCount++
			continue
		}

		if task.Status == consts.TERMINATED {
			taskResult.Status = "skipped"
			taskResult.Message = "Task already terminated"
			result.Results = append(result.Results, taskResult)
			result.SkippedCount++
			continue
		}

		// 更新任务状态为终止
		updateResult := tx.Model(&Task{}).
			Where("id = ?", taskId).
			Update("status", consts.TERMINATED)
		if updateResult.Error != nil {
			taskResult.Status = "failed"
			taskResult.Message = fmt.Sprintf("Failed to update status: %v", updateResult.Error)
			result.Results = append(result.Results, taskResult)
			result.FailedCount++
			klog.Errorf("Failed to terminate task %d: %v", taskId, updateResult.Error)
			continue
		}

		taskResult.Status = "success"
		taskResult.Message = "Task terminated successfully"
		result.Results = append(result.Results, taskResult)
		result.SuccessCount++
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	return result, nil
}
