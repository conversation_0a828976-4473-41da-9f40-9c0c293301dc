package db

import (
	"context"
	"gorm.io/gorm/clause"
	"k8s.io/klog/v2"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
)

type BatchDetail struct {
	BatchId    int64
	SubBatchId int64
	BatchType  int64
	Region     string
	Status     string
}

func CreateBatches(batches []*Batch) error {
	err := createOrUpdateBatch(batches)
	if err != nil {
		return err
	}
	return nil
}

func createOrUpdateBatch(batches []*Batch) error {
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	tx := DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	result := tx.Save(batches)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func DealSubBatchCompleted(taskId int64, batchId int64, subBatchId int64) error {
	// 开始事务
	tx := DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 更新 Task 表中相应 TaskId 的多个字段
	result := tx.Model(&Batch{}).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("task_id = ? AND batch_id = ? AND sub_batch_id = ?", taskId, batchId, subBatchId).
		Updates(map[string]interface{}{
			"status":      consts.COMPLETED,
			"end_time":    time.Now(),
			"update_time": time.Now(),
		})
	if result.Error != nil {
		tx.Rollback()
		klog.Errorf("Failed to update status for TaskId: %d: %v", taskId, result.Error)
		return result.Error
	}

	if suspend, err := ShouldSuspendNextBatch(taskId, batchId, subBatchId); err != nil {
		klog.Errorf("Failed to check if should suspend next batch: task %d, batch %d, subbatch %d, err: %v", taskId, batchId, subBatchId, err)
		tx.Rollback()
		return err
	} else if suspend {
		if err := tx.Model(&Task{}).Clauses(clause.Locking{Strength: "UPDATE"}).Where("id = ?", taskId).Update("status", consts.PAUSE_STA).Error; err != nil {
			klog.Errorf("Failed to update status for TaskId: %d, err: %v", taskId, err)
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		klog.Errorf("Failed to commit transaction for TaskId: %d: %v", taskId, err)
		return err
	}
	return nil
}

func GetBatchByFields(fields map[string]interface{}) ([]*Batch, error) {
	var batches []*Batch
	// 使用 gorm 的 Where 方法根据多个字段获取任务信息
	result := DB.Where(fields).Find(&batches)
	if result.Error != nil {
		klog.Errorf("Failed to fetch tasks by fields: %v: %v", fields, result.Error)
		return nil, result.Error
	}
	return batches, nil
}

func GetBatchDetailsAndTaskInfoByTaskName(taskName string) ([]Batch, Task, error) {
	var taskInfo Task
	err := DB.Where("task_name = ?", taskName).First(&taskInfo).Error
	if err != nil {
		return nil, Task{}, err
	}

	var batches []Batch
	err = DB.Where("task_id = ?", taskInfo.ID).Find(&batches).Error
	if err != nil {
		return nil, Task{}, err
	}

	return batches, taskInfo, nil
}
