/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	v1 "tkestack.io/tke/api/application/v1"
)

// AppResourceLister helps list AppResources.
// All objects returned here must be treated as read-only.
type AppResourceLister interface {
	// List lists all AppResources in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.AppResource, err error)
	// AppResources returns an object that can list and get AppResources.
	AppResources(namespace string) AppResourceNamespaceLister
	AppResourceListerExpansion
}

// appResourceLister implements the AppResourceLister interface.
type appResourceLister struct {
	indexer cache.Indexer
}

// NewAppResourceLister returns a new AppResourceLister.
func NewAppResourceLister(indexer cache.Indexer) AppResourceLister {
	return &appResourceLister{indexer: indexer}
}

// List lists all AppResources in the indexer.
func (s *appResourceLister) List(selector labels.Selector) (ret []*v1.AppResource, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.AppResource))
	})
	return ret, err
}

// AppResources returns an object that can list and get AppResources.
func (s *appResourceLister) AppResources(namespace string) AppResourceNamespaceLister {
	return appResourceNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// AppResourceNamespaceLister helps list and get AppResources.
// All objects returned here must be treated as read-only.
type AppResourceNamespaceLister interface {
	// List lists all AppResources in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.AppResource, err error)
	// Get retrieves the AppResource from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1.AppResource, error)
	AppResourceNamespaceListerExpansion
}

// appResourceNamespaceLister implements the AppResourceNamespaceLister
// interface.
type appResourceNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all AppResources in the indexer for a given namespace.
func (s appResourceNamespaceLister) List(selector labels.Selector) (ret []*v1.AppResource, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.AppResource))
	})
	return ret, err
}

// Get retrieves the AppResource from the indexer for a given namespace and name.
func (s appResourceNamespaceLister) Get(name string) (*v1.AppResource, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("appresource"), name)
	}
	return obj.(*v1.AppResource), nil
}
