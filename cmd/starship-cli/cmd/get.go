package cmd

import (
	"fmt"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/task"
	"git.woa.com/kmetis/starship-engine/pkg/task/postcheck"
	"git.woa.com/kmetis/starship-engine/pkg/task/precheck"
	"git.woa.com/kmetis/starship-engine/pkg/task/publish"

	"github.com/spf13/cobra"
)

func NewGetCommand() *cobra.Command {
	getCmd := &cobra.Command{
		Use:   "get",
		Short: "Get information about tasks",
		Long:  "Get information about tasks, such as publish, precheck, or postcheck.",
	}

	getCmd.AddCommand(NewGetTaskCommand(consts.PUBLISH, &publish.PublishHandler{}))
	getCmd.AddCommand(NewGetTaskCommand(consts.PRECHECK, &precheck.PreCheckHandler{}))
	getCmd.AddCommand(NewGetTaskCommand(consts.POSTCHECK, &postcheck.PostCheckHandler{}))

	return getCmd
}

func NewGetTaskCommand(taskType string, handler task.TaskHandler) *cobra.Command {
	var taskName string
	cmd := &cobra.Command{
		Use:   taskType + " [task-name]",
		Short: fmt.Sprintf("Get %s task information", taskType),
		Long:  fmt.Sprintf("Get %s task information, including lists and cluster lists.", taskType),
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) == 0 {
				if err := getTaskList(handler, ""); err != nil {
					klog.V(1).ErrorS(err, taskType, "Error getting %s task list", taskType)
				} else {
					klog.V(2).InfoS(taskType, "%s task list retrieved successfully", taskType)
				}
			} else if len(args) == 1 {
				taskName = args[0]
				if err := getTaskList(handler, taskName); err != nil {
					klog.V(1).ErrorS(err, taskType, "Error getting cluster list for %s", taskType, "taskName", taskName)
				} else {
					klog.V(2).InfoS(taskType, "Cluster list for %s retrieved successfully", taskType, "taskName", taskName)
				}
			} else {
				fmt.Println("Invalid input for get", taskType, "command.")
				return
			}
		},
	}
	return cmd
}

func getTaskList(handler task.TaskHandler, taskName string) error {
	return handler.GetList(taskName)
}
