package batchStrategy

import (
	"fmt"
	"git.woa.com/kmetis/starship-engine/pkg/config"
	"git.woa.com/kmetis/starship-engine/pkg/consts"
)

type BatchStrategyOptions struct {
	AppAction                 string
	AppName                   string
	BatchSize                 int
	EksPodThresholds          []int
	NodeNumberThresholds      []int
	ClusterLevelThresholds    []int
	EksPodBatchInterval       int64
	RegionBatchInterval       int64
	NodeNumberBatchInterval   int64
	BigUserBatchInterval      int64
	ClusterLevelBatchInterval int64
}

type BatchStrategyChain struct {
	strategies []BatchStrategy
}

func NewBatchStrategyChain(strategies ...BatchStrategy) *BatchStrategyChain {
	return &BatchStrategyChain{strategies: strategies}
}

// 检测是否包含指定策略
func (b *BatchStrategyChain) mustInclude(id int64) bool {
	for _, s := range b.strategies {
		if id == s.GetID() {
			return true
		}
	}
	return false
}

func (b *BatchStrategyChain) Execute(clusters []string, options BatchStrategyOptions) ([]BatchConfig, error) {
	// 检查是否包含指定策略
	if !b.mustInclude(RegionBatchStrategyID) {
		return nil, fmt.Errorf("batch strategy chain must include region batch strategy")
	}

	batchConfigs := []BatchConfig{
		{
			Clusters: clusters,
		},
	}
	var err error
	for i, strategy := range b.strategies {
		batchConfigs, err = strategy.DivideBatches(batchConfigs, i == 0, options)
		if err != nil {
			return nil, err
		}
	}

	batchSize := options.BatchSize
	newBatches := make([]BatchConfig, 0)
	var batchId int64
	var subBatchId int64
	for _, batch := range batchConfigs {
		if batchId == 0 {
			batchId = batch.BatchId
		} else if batchId > 0 && batch.BatchId != batchId {
			batchId = batch.BatchId
			subBatchId = 0
		}
		subBatchId++
		if batchSize > 0 && len(batch.Clusters) > batchSize {
			// 计算需要分成多少个批次
			numBatches := (len(batch.Clusters) + batchSize - 1) / batchSize
			for i := 0; i < numBatches; i++ {
				start := i * batchSize
				end := (i + 1) * batchSize
				if end > len(batch.Clusters) {
					end = len(batch.Clusters)
				}
				subBatchId = subBatchId + int64(i)
				newBatches = append(newBatches, BatchConfig{
					Region:         batch.Region,
					RegionPriority: batch.RegionPriority,
					Clusters:       batch.Clusters[start:end],
					Interval:       batch.Interval,
					BatchId:        batchId,
					SubBatchId:     subBatchId,
				})
			}
		} else {
			// 不需要二次分批
			batch.SubBatchId = subBatchId
			newBatches = append(newBatches, batch)
		}
	}

	newBatchesWithInterval := make([]BatchConfig, len(newBatches))
	interval, err := GetComponentBatchInterval(options.AppName)
	if err != nil {
		return nil, err
	}
	for i, batch := range newBatches {
		var batchInterval int64 = 0
		region := batch.Region

		if options.AppAction != consts.PUBLISH {
			// 非发布组件，批次间隔为3分钟
			batchInterval = PreCheckRegionBatchInterval
		} else if i < len(newBatches)-1 &&
			(newBatches[i+1].RegionPriority != batch.RegionPriority ||
				(newBatches[i+1].Region != region &&
					(newBatches[i+1].Region == "bj" || newBatches[i+1].Region == "sh" || newBatches[i+1].Region == "gz"))) {
			// 如果当前批次和下一个批次的region优先级不一致，或者下一批次的region变为bj/sh/gz，则设置批次间隔
			batchInterval = interval.BatchInterval
		} else if i < len(newBatches)-1 {
			// 如果是小批次并且不是最后一批
			batchInterval = interval.SubBatchInterval
		}

		newBatchesWithInterval[i] = BatchConfig{
			Region:         batch.Region,
			RegionPriority: batch.RegionPriority,
			Clusters:       batch.Clusters,
			Interval:       batchInterval,
			BatchId:        batch.BatchId,
			SubBatchId:     batch.SubBatchId,
		}
	}
	return newBatchesWithInterval, nil
}

func GetComponentBatchInterval(appName string) (config.Interval, error) {
	// 组件批次间隔
	var interval config.Interval
	var ok bool
	intervalConfig, err := config.GetBatchIntervalConfig()
	if err != nil {
		return interval, err
	}
	if interval, ok = intervalConfig[appName]; !ok {
		if interval, ok = intervalConfig["default"]; !ok {
			return interval, fmt.Errorf("get %s batch interval config failed", appName)
		}
	}

	if interval.BatchInterval < MinLargeRegionBatchInterval {
		interval.BatchInterval = MinLargeRegionBatchInterval
	}
	if interval.SubBatchInterval < 0 {
		interval.SubBatchInterval = 0
	}
	return interval, nil
}
