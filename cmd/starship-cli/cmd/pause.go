package cmd

import (
	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/task"
	"git.woa.com/kmetis/starship-engine/pkg/task/postcheck"
	"git.woa.com/kmetis/starship-engine/pkg/task/precheck"
	"git.woa.com/kmetis/starship-engine/pkg/task/publish"

	"github.com/spf13/cobra"
)

func NewPauseCommand() *cobra.Command {
	pauseTaskCmd := &cobra.Command{
		Use:   "pause",
		Short: "Pause a task",
		Long:  "Pause a task, such as a publish, precheck, or postcheck task.",
	}

	pauseTaskCmd.AddCommand(NewPauseOrUnpauseTaskCommand(consts.PUBLISH, consts.PAUSE, &publish.PublishHandler{}))
	pauseTaskCmd.AddCommand(NewPauseOrUnpauseTaskCommand(consts.PRECHECK, consts.PAUSE, &precheck.PreCheckHandler{}))
	pauseTaskCmd.AddCommand(NewPauseOrUnpauseTaskCommand(consts.POSTCHECK, consts.PAUSE, &postcheck.PostCheckHandler{}))

	return pauseTaskCmd
}

func NewUnpauseCommand() *cobra.Command {
	unpauseTaskCmd := &cobra.Command{
		Use:   "unpause",
		Short: "Unpause a task",
		Long:  "Unpause a task, such as a publish, precheck, or postcheck task.",
	}

	unpauseTaskCmd.AddCommand(NewPauseOrUnpauseTaskCommand(consts.PUBLISH, consts.UNPAUSE, &publish.PublishHandler{}))
	unpauseTaskCmd.AddCommand(NewPauseOrUnpauseTaskCommand(consts.PRECHECK, consts.UNPAUSE, &precheck.PreCheckHandler{}))
	unpauseTaskCmd.AddCommand(NewPauseOrUnpauseTaskCommand(consts.POSTCHECK, consts.UNPAUSE, &postcheck.PostCheckHandler{}))

	return unpauseTaskCmd
}

func NewPauseOrUnpauseTaskCommand(taskType, pauseAction string, handler task.TaskHandler) *cobra.Command {
	cmd := &cobra.Command{
		Use:   taskType + " [taskName]",
		Short: pauseAction + " a " + taskType + " task",
		Long:  pauseAction + " a " + taskType + " task with the given name.",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			taskName := args[0]
			var err error
			if pauseAction == consts.PAUSE {
				err = handler.PauseTask(taskName)
			} else if pauseAction == consts.UNPAUSE {
				err = handler.UnpauseTask(taskName)
			}
			if err != nil {
				// 处理错误
				return
			}
		},
	}
	return cmd
}
