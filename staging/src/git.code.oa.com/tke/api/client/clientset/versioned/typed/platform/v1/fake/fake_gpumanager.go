/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeGPUManagers implements GPUManagerInterface
type FakeGPUManagers struct {
	Fake *FakePlatformV1
}

var gpumanagersResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "gpumanagers"}

var gpumanagersKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "GPUManager"}

// Get takes name of the gPUManager, and returns the corresponding gPUManager object, and an error if there is any.
func (c *FakeGPUManagers) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.GPUManager, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(gpumanagersResource, name), &platformv1.GPUManager{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GPUManager), err
}

// List takes label and field selectors, and returns the list of GPUManagers that match those selectors.
func (c *FakeGPUManagers) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.GPUManagerList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(gpumanagersResource, gpumanagersKind, opts), &platformv1.GPUManagerList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.GPUManagerList{ListMeta: obj.(*platformv1.GPUManagerList).ListMeta}
	for _, item := range obj.(*platformv1.GPUManagerList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested gPUManagers.
func (c *FakeGPUManagers) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(gpumanagersResource, opts))
}

// Create takes the representation of a gPUManager and creates it.  Returns the server's representation of the gPUManager, and an error, if there is any.
func (c *FakeGPUManagers) Create(ctx context.Context, gPUManager *platformv1.GPUManager, opts v1.CreateOptions) (result *platformv1.GPUManager, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(gpumanagersResource, gPUManager), &platformv1.GPUManager{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GPUManager), err
}

// Update takes the representation of a gPUManager and updates it. Returns the server's representation of the gPUManager, and an error, if there is any.
func (c *FakeGPUManagers) Update(ctx context.Context, gPUManager *platformv1.GPUManager, opts v1.UpdateOptions) (result *platformv1.GPUManager, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(gpumanagersResource, gPUManager), &platformv1.GPUManager{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GPUManager), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeGPUManagers) UpdateStatus(ctx context.Context, gPUManager *platformv1.GPUManager, opts v1.UpdateOptions) (*platformv1.GPUManager, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(gpumanagersResource, "status", gPUManager), &platformv1.GPUManager{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GPUManager), err
}

// Delete takes name of the gPUManager and deletes it. Returns an error if one occurs.
func (c *FakeGPUManagers) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(gpumanagersResource, name), &platformv1.GPUManager{})
	return err
}

// Patch applies the patch and returns the patched gPUManager.
func (c *FakeGPUManagers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.GPUManager, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(gpumanagersResource, name, pt, data, subresources...), &platformv1.GPUManager{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GPUManager), err
}
