/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	testing "k8s.io/client-go/testing"
	platformv1 "tkestack.io/tke/api/platform/v1"
)

// FakeClusterGroupAPIResourceItemses implements ClusterGroupAPIResourceItemsInterface
type FakeClusterGroupAPIResourceItemses struct {
	Fake *FakePlatformV1
}

var clustergroupapiresourceitemsesResource = schema.GroupVersionResource{Group: "platform.tkestack.io", Version: "v1", Resource: "clustergroupapiresourceitemses"}

var clustergroupapiresourceitemsesKind = schema.GroupVersionKind{Group: "platform.tkestack.io", Version: "v1", Kind: "ClusterGroupAPIResourceItems"}

// Get takes name of the clusterGroupAPIResourceItems, and returns the corresponding clusterGroupAPIResourceItems object, and an error if there is any.
func (c *FakeClusterGroupAPIResourceItemses) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.ClusterGroupAPIResourceItems, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(clustergroupapiresourceitemsesResource, name), &platformv1.ClusterGroupAPIResourceItems{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.ClusterGroupAPIResourceItems), err
}

// List takes label and field selectors, and returns the list of ClusterGroupAPIResourceItemses that match those selectors.
func (c *FakeClusterGroupAPIResourceItemses) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.ClusterGroupAPIResourceItemsList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(clustergroupapiresourceitemsesResource, clustergroupapiresourceitemsesKind, opts), &platformv1.ClusterGroupAPIResourceItemsList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.ClusterGroupAPIResourceItemsList{ListMeta: obj.(*platformv1.ClusterGroupAPIResourceItemsList).ListMeta}
	for _, item := range obj.(*platformv1.ClusterGroupAPIResourceItemsList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}
