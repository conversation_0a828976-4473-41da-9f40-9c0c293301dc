/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by informer-gen. DO NOT EDIT.

package externalversions

import (
	"fmt"

	schema "k8s.io/apimachinery/pkg/runtime/schema"
	cache "k8s.io/client-go/tools/cache"
	v1 "tkestack.io/tke/api/application/v1"
	platformv1 "tkestack.io/tke/api/platform/v1"
)

// GenericInformer is type of SharedIndexInformer which will locate and delegate to other
// sharedInformers based on type
type GenericInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() cache.GenericLister
}

type genericInformer struct {
	informer cache.SharedIndexInformer
	resource schema.GroupResource
}

// Informer returns the SharedIndexInformer.
func (f *genericInformer) Informer() cache.SharedIndexInformer {
	return f.informer
}

// Lister returns the GenericLister.
func (f *genericInformer) Lister() cache.GenericLister {
	return cache.NewGenericLister(f.Informer().GetIndexer(), f.resource)
}

// ForResource gives generic access to a shared informer of the matching type
// TODO extend this to unknown resources with a client pool
func (f *sharedInformerFactory) ForResource(resource schema.GroupVersionResource) (GenericInformer, error) {
	switch resource {
	// Group=application.tkestack.io, Version=v1
	case v1.SchemeGroupVersion.WithResource("apps"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Application().V1().Apps().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("configmaps"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Application().V1().ConfigMaps().Informer()}, nil

		// Group=platform.tkestack.io, Version=v1
	case platformv1.SchemeGroupVersion.WithResource("csioperators"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().CSIOperators().Informer()}, nil
	case platformv1.SchemeGroupVersion.WithResource("clusters"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Clusters().Informer()}, nil
	case platformv1.SchemeGroupVersion.WithResource("clustercredentials"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().ClusterCredentials().Informer()}, nil
	case platformv1.SchemeGroupVersion.WithResource("configmaps"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().ConfigMaps().Informer()}, nil
	case platformv1.SchemeGroupVersion.WithResource("cronhpas"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().CronHPAs().Informer()}, nil
	case platformv1.SchemeGroupVersion.WithResource("machines"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Machines().Informer()}, nil
	case platformv1.SchemeGroupVersion.WithResource("persistentevents"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().PersistentEvents().Informer()}, nil
	case platformv1.SchemeGroupVersion.WithResource("registries"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Registries().Informer()}, nil
	case platformv1.SchemeGroupVersion.WithResource("tappcontrollers"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().TappControllers().Informer()}, nil

	}

	return nil, fmt.Errorf("no informer found for %v", resource)
}
