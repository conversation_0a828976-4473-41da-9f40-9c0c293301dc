/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeDNSAutoscalers implements DNSAutoscalerInterface
type FakeDNSAutoscalers struct {
	Fake *FakePlatformV1
}

var dnsautoscalersResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "dnsautoscalers"}

var dnsautoscalersKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "DNSAutoscaler"}

// Get takes name of the dNSAutoscaler, and returns the corresponding dNSAutoscaler object, and an error if there is any.
func (c *FakeDNSAutoscalers) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.DNSAutoscaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(dnsautoscalersResource, name), &platformv1.DNSAutoscaler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DNSAutoscaler), err
}

// List takes label and field selectors, and returns the list of DNSAutoscalers that match those selectors.
func (c *FakeDNSAutoscalers) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.DNSAutoscalerList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(dnsautoscalersResource, dnsautoscalersKind, opts), &platformv1.DNSAutoscalerList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.DNSAutoscalerList{ListMeta: obj.(*platformv1.DNSAutoscalerList).ListMeta}
	for _, item := range obj.(*platformv1.DNSAutoscalerList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested dNSAutoscalers.
func (c *FakeDNSAutoscalers) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(dnsautoscalersResource, opts))
}

// Create takes the representation of a dNSAutoscaler and creates it.  Returns the server's representation of the dNSAutoscaler, and an error, if there is any.
func (c *FakeDNSAutoscalers) Create(ctx context.Context, dNSAutoscaler *platformv1.DNSAutoscaler, opts v1.CreateOptions) (result *platformv1.DNSAutoscaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(dnsautoscalersResource, dNSAutoscaler), &platformv1.DNSAutoscaler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DNSAutoscaler), err
}

// Update takes the representation of a dNSAutoscaler and updates it. Returns the server's representation of the dNSAutoscaler, and an error, if there is any.
func (c *FakeDNSAutoscalers) Update(ctx context.Context, dNSAutoscaler *platformv1.DNSAutoscaler, opts v1.UpdateOptions) (result *platformv1.DNSAutoscaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(dnsautoscalersResource, dNSAutoscaler), &platformv1.DNSAutoscaler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DNSAutoscaler), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeDNSAutoscalers) UpdateStatus(ctx context.Context, dNSAutoscaler *platformv1.DNSAutoscaler, opts v1.UpdateOptions) (*platformv1.DNSAutoscaler, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(dnsautoscalersResource, "status", dNSAutoscaler), &platformv1.DNSAutoscaler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DNSAutoscaler), err
}

// Delete takes name of the dNSAutoscaler and deletes it. Returns an error if one occurs.
func (c *FakeDNSAutoscalers) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(dnsautoscalersResource, name), &platformv1.DNSAutoscaler{})
	return err
}

// Patch applies the patch and returns the patched dNSAutoscaler.
func (c *FakeDNSAutoscalers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.DNSAutoscaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(dnsautoscalersResource, name, pt, data, subresources...), &platformv1.DNSAutoscaler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DNSAutoscaler), err
}
