package elasticsearch

import (
	"testing"
	"time"
)

func TestCache_UpdateClusters(t *testing.T) {
	cache := NewCache()

	// 创建测试数据
	clusters := []*ClusterInfo{
		{
			ClusterID:   "cls-tke-001",
			ClusterName: "test-tke-cluster",
			ClusterType: "tke",
			Region:      "gz",
			ComponentVersion: map[string]interface{}{
				"coredns": map[string]interface{}{
					"name":  "coredns",
					"image": "tpeccr.ccs.tencentyun.com/tkeimages/coredns",
					"tag":   "1.8.4",
				},
			},
			AddonInfo: map[string]interface{}{
				"cbs": map[string]interface{}{
					"addonName":    "cbs",
					"addonVersion": "1.1.5",
					"addonStatus":  "Succeeded",
				},
			},
			Tags: map[string]interface{}{
				"env": "test",
			},
		},
		{
			ClusterID:   "cls-eks-001",
			ClusterName: "test-eks-cluster",
			ClusterType: "eks",
			Region:      "jp",
			ComponentVersion: map[string]interface{}{
				"eklet": map[string]interface{}{
					"name":  "eklet",
					"image": "jpccr.ccs.tencentyun.com/tkeimages/eklet-amd64",
					"tag":   "v2.15.30",
				},
			},
			AddonInfo: map[string]interface{}{},
			Tags:      map[string]interface{}{},
		},
	}

	// 更新缓存
	cache.UpdateClusters(clusters)

	// 验证统计信息
	stats := cache.GetStats()
	if stats.TotalClusters != 2 {
		t.Errorf("Expected 2 total clusters, got %d", stats.TotalClusters)
	}
	if stats.TKEClusters != 1 {
		t.Errorf("Expected 1 TKE cluster, got %d", stats.TKEClusters)
	}
	if stats.EKSClusters != 1 {
		t.Errorf("Expected 1 EKS cluster, got %d", stats.EKSClusters)
	}

	// 验证分别存储
	tkeMap := cache.GetTKEClustersMap()
	eksMap := cache.GetEKSClustersMap()

	if len(tkeMap) != 1 {
		t.Errorf("Expected 1 TKE cluster in map, got %d", len(tkeMap))
	}
	if len(eksMap) != 1 {
		t.Errorf("Expected 1 EKS cluster in map, got %d", len(eksMap))
	}

	// 验证TKE集群
	if tkeCluster, exists := tkeMap["cls-tke-001"]; !exists {
		t.Error("TKE cluster not found in TKE map")
	} else {
		if tkeCluster.ClusterType != "tke" {
			t.Errorf("Expected TKE cluster type, got %s", tkeCluster.ClusterType)
		}

		// 验证组件版本可以直接访问
		components := tkeCluster.GetComponentVersion()
		if coredns, ok := components["coredns"].(map[string]interface{}); ok {
			if tag, ok := coredns["tag"].(string); !ok || tag != "1.8.4" {
				t.Errorf("Expected coredns tag 1.8.4, got %v", tag)
			}
		} else {
			t.Error("Failed to get coredns component")
		}
	}

	// 验证EKS集群
	if eksCluster, exists := eksMap["cls-eks-001"]; !exists {
		t.Error("EKS cluster not found in EKS map")
	} else {
		if eksCluster.ClusterType != "eks" {
			t.Errorf("Expected EKS cluster type, got %s", eksCluster.ClusterType)
		}
	}
}

func TestExtractNestedField(t *testing.T) {
	cache := &Cache{}
	cluster := createExpressionTestClusters()[0] // 使用第一个测试集群

	tests := []struct {
		name      string
		fieldPath string
		expected  interface{}
	}{
		{
			name:      "Extract kubernetes tag",
			fieldPath: `ComponentVersion["kubernetes"]["tag"]`,
			expected:  "v1.20.0-tke.9",
		},
		{
			name:      "Extract docker version",
			fieldPath: `ComponentVersion["docker"]`,
			expected:  "20.10.7",
		},
		{
			name:      "Extract CNI type",
			fieldPath: `AddonInfo["cni"]["type"]`,
			expected:  "flannel",
		},
		{
			name:      "Extract CNI version",
			fieldPath: `AddonInfo["cni"]["version"]`,
			expected:  "0.8.0",
		},
		{
			name:      "Non-existent field",
			fieldPath: `ComponentVersion["nonexistent"]["tag"]`,
			expected:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := cache.extractNestedField(cluster, tt.fieldPath)
			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestClusterToMapWithNestedFields(t *testing.T) {
	cache := &Cache{}
	cluster := createExpressionTestClusters()[0]

	fields := []string{
		"ClusterID",
		"ClusterName",
		`ComponentVersion["kubernetes"]["tag"]`,
		`ComponentVersion["docker"]`,
		`AddonInfo["cni"]["type"]`,
	}

	result := cache.clusterToMap(cluster, fields)

	// 验证基本字段
	if result["ClusterID"] != "cls-tke-001" {
		t.Errorf("Expected ClusterID to be cls-tke-001, got %v", result["ClusterID"])
	}

	// 验证嵌套字段
	if result[`ComponentVersion["kubernetes"]["tag"]`] != "v1.20.0-tke.9" {
		t.Errorf("Expected kubernetes tag to be v1.20.0-tke.9, got %v", result[`ComponentVersion["kubernetes"]["tag"]`])
	}

	if result[`ComponentVersion["docker"]`] != "20.10.7" {
		t.Errorf("Expected docker version to be 20.10.7, got %v", result[`ComponentVersion["docker"]`])
	}

	if result[`AddonInfo["cni"]["type"]`] != "flannel" {
		t.Errorf("Expected CNI type to be flannel, got %v", result[`AddonInfo["cni"]["type"]`])
	}
}

func TestCache_GetCluster(t *testing.T) {
	cache := NewCache()

	clusters := []*ClusterInfo{
		{
			ClusterID:   "cls-tke-001",
			ClusterType: "tke",
		},
		{
			ClusterID:   "cls-eks-001",
			ClusterType: "eks",
		},
	}

	cache.UpdateClusters(clusters)

	// 测试获取TKE集群
	if cluster, exists := cache.GetCluster("cls-tke-001"); !exists {
		t.Error("TKE cluster not found")
	} else if cluster.ClusterType != "tke" {
		t.Errorf("Expected TKE cluster, got %s", cluster.ClusterType)
	}

	// 测试获取EKS集群
	if cluster, exists := cache.GetCluster("cls-eks-001"); !exists {
		t.Error("EKS cluster not found")
	} else if cluster.ClusterType != "eks" {
		t.Errorf("Expected EKS cluster, got %s", cluster.ClusterType)
	}

	// 测试获取不存在的集群
	if _, exists := cache.GetCluster("cls-not-exist"); exists {
		t.Error("Non-existent cluster should not be found")
	}
}

func TestCache_GetClustersByType(t *testing.T) {
	cache := NewCache()

	clusters := []*ClusterInfo{
		{ClusterID: "cls-tke-001", ClusterType: "tke"},
		{ClusterID: "cls-tke-002", ClusterType: "tke"},
		{ClusterID: "cls-eks-001", ClusterType: "eks"},
	}

	cache.UpdateClusters(clusters)

	// 测试获取TKE集群
	tkeClusters := cache.GetClustersByType("tke")
	if len(tkeClusters) != 2 {
		t.Errorf("Expected 2 TKE clusters, got %d", len(tkeClusters))
	}

	// 测试获取EKS集群
	eksClusters := cache.GetClustersByType("eks")
	if len(eksClusters) != 1 {
		t.Errorf("Expected 1 EKS cluster, got %d", len(eksClusters))
	}

	// 测试获取不存在的类型
	unknownClusters := cache.GetClustersByType("unknown")
	if len(unknownClusters) != 0 {
		t.Errorf("Expected 0 unknown clusters, got %d", len(unknownClusters))
	}
}

func TestCache_SearchClusters(t *testing.T) {
	cache := NewCache()

	clusters := []*ClusterInfo{
		{
			ClusterID:   "cls-test-001",
			ClusterName: "test-cluster-1",
			ClusterType: "tke",
			Region:      "gz",
		},
		{
			ClusterID:   "cls-prod-001",
			ClusterName: "prod-cluster-1",
			ClusterType: "eks",
			Region:      "jp",
		},
	}

	cache.UpdateClusters(clusters)

	// 搜索包含"test"的集群
	results := cache.SearchClusters("test")
	if len(results) != 1 {
		t.Errorf("Expected 1 result for 'test', got %d", len(results))
	}

	// 搜索包含"cluster"的集群
	results = cache.SearchClusters("cluster")
	if len(results) != 2 {
		t.Errorf("Expected 2 results for 'cluster', got %d", len(results))
	}

	// 搜索不存在的关键词
	results = cache.SearchClusters("notfound")
	if len(results) != 0 {
		t.Errorf("Expected 0 results for 'notfound', got %d", len(results))
	}
}

func TestCache_FullSyncWithDeletion(t *testing.T) {
	cache := NewCache()

	// 第一次同步：添加3个集群
	initialTKE := []*ClusterInfo{
		{ClusterID: "cls-tke-001", ClusterType: "tke"},
		{ClusterID: "cls-tke-002", ClusterType: "tke"},
	}
	initialEKS := []*ClusterInfo{
		{ClusterID: "cls-eks-001", ClusterType: "eks"},
	}

	cache.UpdateClustersByType(initialTKE, initialEKS)

	// 验证初始状态
	if cache.GetStats().TotalClusters != 3 {
		t.Errorf("Expected 3 clusters after initial sync, got %d", cache.GetStats().TotalClusters)
	}

	// 第二次同步：删除一个TKE集群，添加一个新的EKS集群
	updatedTKE := []*ClusterInfo{
		{ClusterID: "cls-tke-001", ClusterType: "tke"}, // 保留
		// cls-tke-002 被删除
	}
	updatedEKS := []*ClusterInfo{
		{ClusterID: "cls-eks-001", ClusterType: "eks"}, // 保留
		{ClusterID: "cls-eks-002", ClusterType: "eks"}, // 新增
	}

	cache.UpdateClustersByType(updatedTKE, updatedEKS)

	// 验证更新后的状态
	stats := cache.GetStats()
	if stats.TotalClusters != 3 {
		t.Errorf("Expected 3 clusters after update, got %d", stats.TotalClusters)
	}
	if stats.TKEClusters != 1 {
		t.Errorf("Expected 1 TKE cluster after update, got %d", stats.TKEClusters)
	}
	if stats.EKSClusters != 2 {
		t.Errorf("Expected 2 EKS clusters after update, got %d", stats.EKSClusters)
	}

	// 验证被删除的集群不存在
	if _, exists := cache.GetCluster("cls-tke-002"); exists {
		t.Error("Deleted cluster cls-tke-002 should not exist")
	}

	// 验证新增的集群存在
	if _, exists := cache.GetCluster("cls-eks-002"); !exists {
		t.Error("New cluster cls-eks-002 should exist")
	}

	// 验证变化统计
	changes := cache.CompareWithPrevious()
	summary := changes["summary"].(map[string]interface{})

	if summary["tke_removed"].(int) != 1 {
		t.Errorf("Expected 1 TKE cluster removed, got %v", summary["tke_removed"])
	}
	if summary["eks_added"].(int) != 1 {
		t.Errorf("Expected 1 EKS cluster added, got %v", summary["eks_added"])
	}
}

func TestCache_CacheInfo(t *testing.T) {
	cache := NewCache()

	clusters := []*ClusterInfo{
		{ClusterID: "cls-tke-001", ClusterType: "tke"},
		{ClusterID: "cls-eks-001", ClusterType: "eks"},
	}

	cache.UpdateClusters(clusters)

	// 获取缓存信息
	info := cache.GetCacheInfo()

	active := info["active"].(map[string]interface{})
	if active["total"].(int) != 2 {
		t.Errorf("Expected 2 active clusters, got %v", active["total"])
	}
	if active["tke_clusters"].(int) != 1 {
		t.Errorf("Expected 1 active TKE cluster, got %v", active["tke_clusters"])
	}
	if active["eks_clusters"].(int) != 1 {
		t.Errorf("Expected 1 active EKS cluster, got %v", active["eks_clusters"])
	}
}

// 创建测试数据用于表达式过滤测试
func createExpressionTestClusters() []*ClusterInfo {
	return []*ClusterInfo{
		{
			ClusterID:     "cls-tke-001",
			ClusterName:   "prod-tke-beijing",
			ClusterType:   "tke",
			ClusterStatus: "Running",
			Region:        "ap-beijing",
			AppID:         "12345",
			UIN:           "100001",
			NodeCount:     15,
			CPUCount:      60,
			MemCount:      240,
			ClusterLevel:  "L500",
			ComponentVersion: map[string]interface{}{
				"kubernetes": map[string]interface{}{
					"tag": "v1.20.0-tke.9",
				},
				"docker": "20.10.7",
			},
			AddonInfo: map[string]interface{}{
				"cni": map[string]interface{}{
					"version": "0.8.0",
					"type":    "flannel",
				},
			},
			CreatedAt: time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC),
		},
		{
			ClusterID:     "cls-eks-001",
			ClusterName:   "test-eks-shanghai",
			ClusterType:   "eks",
			ClusterStatus: "Running",
			Region:        "ap-shanghai",
			AppID:         "12345",
			UIN:           "100002",
			NodeCount:     8,
			CPUCount:      32,
			MemCount:      128,
			ClusterLevel:  "L200",
			ComponentVersion: map[string]interface{}{
				"kubernetes": map[string]interface{}{
					"tag": "v1.21.0-eks.5",
				},
				"containerd": "1.4.6",
			},
			AddonInfo: map[string]interface{}{
				"cni": map[string]interface{}{
					"version": "0.9.0",
					"type":    "vpc-cni",
				},
			},
			CreatedAt: time.Date(2024, 2, 10, 14, 30, 0, 0, time.UTC),
		},
		{
			ClusterID:     "cls-tke-002",
			ClusterName:   "dev-tke-guangzhou",
			ClusterType:   "tke",
			ClusterStatus: "Stopped",
			Region:        "ap-guangzhou",
			AppID:         "12346",
			UIN:           "100003",
			NodeCount:     3,
			CPUCount:      12,
			MemCount:      48,
			ClusterLevel:  "L5000",
			ComponentVersion: map[string]interface{}{
				"kubernetes": map[string]interface{}{
					"tag": "v1.19.0-tke.3",
				},
				"docker": "19.03.15",
			},
			CreatedAt: time.Date(2024, 1, 20, 9, 15, 0, 0, time.UTC),
		},
	}
}

func TestFilterClustersByExpression(t *testing.T) {
	cache := &Cache{}
	clusters := createExpressionTestClusters()

	tests := []struct {
		name       string
		expression string
		expected   int
		shouldErr  bool
	}{
		{
			name:       "Filter by cluster type",
			expression: `ClusterType == "tke"`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Filter by region",
			expression: `Region == "ap-beijing"`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "Filter by status",
			expression: `ClusterStatus == "Running"`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Filter by node count",
			expression: `NodeCount > 10`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "Complex condition with AND",
			expression: `ClusterType == "tke" && ClusterStatus == "Running"`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "Complex condition with OR",
			expression: `Region == "ap-beijing" || Region == "ap-shanghai"`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "String contains",
			expression: `ClusterName contains "prod"`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "String startsWith",
			expression: `ClusterName startsWith "test"`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "Multiple conditions",
			expression: `ClusterType == "tke" && NodeCount >= 3 && ClusterStatus == "Running"`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "Map field access - ComponentVersion",
			expression: `ComponentVersion["kubernetes"]["tag"] == "v1.20.0-tke.9"`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "Empty expression",
			expression: ``,
			expected:   3,
			shouldErr:  false,
		},
		{
			name:       "Version comparison - greater than",
			expression: `versionGt(ComponentVersion["kubernetes"]["tag"], "v1.19.0")`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Version comparison - less than",
			expression: `versionLt(ComponentVersion["kubernetes"]["tag"], "v1.21.0")`,
			expected:   3,
			shouldErr:  false,
		},
		{
			name:       "Version comparison - equal (major.minor)",
			expression: `versionGte(ComponentVersion["kubernetes"]["tag"], "v1.20.0") && versionLt(ComponentVersion["kubernetes"]["tag"], "v1.21.0")`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "Version comparison with patch version",
			expression: `versionGte(ComponentVersion["kubernetes"]["tag"], "v1.20.0-tke.5")`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Level comparison - greater than",
			expression: `levelGt(ClusterLevel, "L200")`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Level comparison - less than",
			expression: `levelLt(ClusterLevel, "L1000")`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Level comparison - equal",
			expression: `levelEq(ClusterLevel, "L500")`,
			expected:   1,
			shouldErr:  false,
		},
		{
			name:       "Level comparison - greater than or equal",
			expression: `levelGte(ClusterLevel, "L500")`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Time comparison - greater than",
			expression: `timeGt(CreatedAt, "2024-01-20T00:00:00Z")`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Time comparison - less than",
			expression: `timeLt(CreatedAt, "2024-02-01T00:00:00Z")`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Time comparison - between dates",
			expression: `timeGte(CreatedAt, "2024-01-01T00:00:00Z") && timeLt(CreatedAt, "2024-02-01T00:00:00Z")`,
			expected:   2,
			shouldErr:  false,
		},
		{
			name:       "Time comparison with RFC3339Nano format",
			expression: `timeGte(CreatedAt, "2024-01-15T10:00:00.000000000Z")`,
			expected:   3,
			shouldErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := cache.filterClustersByExpression(clusters, tt.expression)

			if tt.shouldErr {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if len(result) != tt.expected {
				t.Errorf("Expected %d clusters, got %d", tt.expected, len(result))
			}
		})
	}
}
