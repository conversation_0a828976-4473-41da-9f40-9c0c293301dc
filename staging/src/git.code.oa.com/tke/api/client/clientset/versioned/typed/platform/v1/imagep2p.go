/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// ImageP2PsGetter has a method to return a ImageP2PInterface.
// A group's client should implement this interface.
type ImageP2PsGetter interface {
	ImageP2Ps() ImageP2PInterface
}

// ImageP2PInterface has methods to work with ImageP2P resources.
type ImageP2PInterface interface {
	Create(ctx context.Context, imageP2P *v1.ImageP2P, opts metav1.CreateOptions) (*v1.ImageP2P, error)
	Update(ctx context.Context, imageP2P *v1.ImageP2P, opts metav1.UpdateOptions) (*v1.ImageP2P, error)
	UpdateStatus(ctx context.Context, imageP2P *v1.ImageP2P, opts metav1.UpdateOptions) (*v1.ImageP2P, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.ImageP2P, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.ImageP2PList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.ImageP2P, err error)
	ImageP2PExpansion
}

// imageP2Ps implements ImageP2PInterface
type imageP2Ps struct {
	client rest.Interface
}

// newImageP2Ps returns a ImageP2Ps
func newImageP2Ps(c *PlatformV1Client) *imageP2Ps {
	return &imageP2Ps{
		client: c.RESTClient(),
	}
}

// Get takes name of the imageP2P, and returns the corresponding imageP2P object, and an error if there is any.
func (c *imageP2Ps) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.ImageP2P, err error) {
	result = &v1.ImageP2P{}
	err = c.client.Get().
		Resource("imagep2ps").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of ImageP2Ps that match those selectors.
func (c *imageP2Ps) List(ctx context.Context, opts metav1.ListOptions) (result *v1.ImageP2PList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.ImageP2PList{}
	err = c.client.Get().
		Resource("imagep2ps").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested imageP2Ps.
func (c *imageP2Ps) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("imagep2ps").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a imageP2P and creates it.  Returns the server's representation of the imageP2P, and an error, if there is any.
func (c *imageP2Ps) Create(ctx context.Context, imageP2P *v1.ImageP2P, opts metav1.CreateOptions) (result *v1.ImageP2P, err error) {
	result = &v1.ImageP2P{}
	err = c.client.Post().
		Resource("imagep2ps").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(imageP2P).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a imageP2P and updates it. Returns the server's representation of the imageP2P, and an error, if there is any.
func (c *imageP2Ps) Update(ctx context.Context, imageP2P *v1.ImageP2P, opts metav1.UpdateOptions) (result *v1.ImageP2P, err error) {
	result = &v1.ImageP2P{}
	err = c.client.Put().
		Resource("imagep2ps").
		Name(imageP2P.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(imageP2P).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *imageP2Ps) UpdateStatus(ctx context.Context, imageP2P *v1.ImageP2P, opts metav1.UpdateOptions) (result *v1.ImageP2P, err error) {
	result = &v1.ImageP2P{}
	err = c.client.Put().
		Resource("imagep2ps").
		Name(imageP2P.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(imageP2P).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the imageP2P and deletes it. Returns an error if one occurs.
func (c *imageP2Ps) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("imagep2ps").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched imageP2P.
func (c *imageP2Ps) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.ImageP2P, err error) {
	result = &v1.ImageP2P{}
	err = c.client.Patch(pt).
		Resource("imagep2ps").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
