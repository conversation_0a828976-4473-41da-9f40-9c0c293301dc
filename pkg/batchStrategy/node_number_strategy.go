package batchStrategy

import (
	"fmt"
	"sort"

	pkgcls "git.woa.com/kmetis/starship-engine/pkg/clusterinfo"
)

const (
	NodeNumberBatchStrategyName = "NodeNumberBatchStrategy"
)

var defaultNodeNumberBatchThreshold = []int{20, 200, 500}

type NodeNumberBatchStrategy struct {
	Name string
	ID   int64
}

func NewNodeNumberBatchStrategy() *NodeNumberBatchStrategy {
	return &NodeNumberBatchStrategy{
		Name: NodeNumberBatchStrategyName,
		ID:   NodeNumberBatchStrategyID,
	}
}

func (n *NodeNumberBatchStrategy) DivideBatches(batches []BatchConfig, isMainBatch bool, options BatchStrategyOptions) ([]BatchConfig, error) {
	if isMainBatch && len(batches) > 1 {
		return nil, fmt.Errorf("main batch should have at most one batch")
	}
	thresholds := options.NodeNumberThresholds
	if len(thresholds) == 0 {
		thresholds = defaultNodeNumberBatchThreshold
	}
	sort.Ints(thresholds)
	if options.AppName == "" {
		return nil, fmt.Errorf("appName is required")
	}

	newBatches := make([]BatchConfig, 0)
	helper, err := pkgcls.GetClsInfoHelper()
	if err != nil {
		return nil, err
	}
	for _, batch := range batches {
		if len(batch.Clusters) == 0 {
			continue
		}
		batchPriorityGroups := make(map[int][]string)
		for _, cluster := range batch.Clusters {
			nodeNum, err := helper.CountClusterNodes(options.AppName, cluster)
			if err != nil {
				return nil, err
			}
			for i, threshold := range thresholds {
				if nodeNum <= threshold {
					if _, ok := batchPriorityGroups[i]; !ok {
						batchPriorityGroups[i] = make([]string, 0)
					}
					batchPriorityGroups[i] = append(batchPriorityGroups[i], cluster)
					break
				}
				if i == len(thresholds)-1 {
					if _, ok := batchPriorityGroups[i+1]; !ok {
						batchPriorityGroups[i+1] = make([]string, 0)
					}
					batchPriorityGroups[i+1] = append(batchPriorityGroups[i+1], cluster)
				}
			}
		}

		index := 0
		for i := 0; i < len(thresholds)+1; i++ {
			if len(batchPriorityGroups[i]) > 0 {
				index++
				batchId := batch.BatchId
				if isMainBatch {
					batchId = int64(index)
				}
				newBatches = append(newBatches, BatchConfig{
					Region:   batch.Region,
					Clusters: batchPriorityGroups[i],
					BatchId:  batchId,
				})
			}
		}
	}
	return newBatches, nil
}

func (n *NodeNumberBatchStrategy) GetName() string {
	return NodeNumberBatchStrategyName
}

func (n *NodeNumberBatchStrategy) GetID() int64 {
	return NodeNumberBatchStrategyID
}
