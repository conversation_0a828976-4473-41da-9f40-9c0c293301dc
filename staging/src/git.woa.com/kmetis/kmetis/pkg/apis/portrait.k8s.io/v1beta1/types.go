/*
Copyright 2019 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Package v1beta1 contains definitions of Workload related objects.
package v1beta1

import (
	autoscaling "k8s.io/api/autoscaling/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// WorkloadList is a list of Workload objects.
type WorkloadList struct {
	metav1.TypeMeta `json:",inline"`
	// metadata is the standard list metadata.
	// +optional
	metav1.ListMeta `json:"metadata" protobuf:"bytes,1,opt,name=metadata"`

	// items is the list of workload objects.
	Items []Workload `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:storageversion
// +kubebuilder:resource:shortName=wl
// +kubebuilder:printcolumn:name="Mode",type="string",JSONPath=".spec.updatePolicy.updateMode"
// +kubebuilder:printcolumn:name="CPU",type="string",JSONPath=".status.recommendation.containerRecommendations[0].target.cpu"
// +kubebuilder:printcolumn:name="Mem",type="string",JSONPath=".status.recommendation.containerRecommendations[0].target.memory"
// +kubebuilder:printcolumn:name="Provided",type="string",JSONPath=".status.conditions[?(@.type=='RecommendationProvided')].status"
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

// Workload is the configuration for a Deployment/Statusfulset
// ,which automatically manages pod resources based on historical and
// real time resource utilization.
type Workload struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the behavior of the autoscaler.
	// More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#spec-and-status.
	Spec WorkloadSpec `json:"spec" protobuf:"bytes,2,name=spec"`

	// Current information about the autoscaler.
	// +optional
	Status WorkloadStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// WorkloadSpec is the specification of the behavior of the autoscaler.
type WorkloadSpec struct {

	// TargetRef points to the controller managing the set of pods for the
	// autoscaler to control - e.g. Deployment, StatefulSet. Workload
	// can be targeted at controller implementing scale subresource (the pod set is
	// retrieved from the controller's ScaleStatus) or some well known controllers
	// (e.g. for DaemonSet the pod set is read from the controller's spec).
	// If Workload cannot use specified target it will report
	// ConfigUnsupported condition.
	// Note that Workload does not require full implementation
	// of scale subresource - it will not use it to modify the replica count.
	// The only thing retrieved is a label selector matching pods grouped by
	// the target resource.
	TargetRef *autoscaling.CrossVersionObjectReference `json:"targetRef" protobuf:"bytes,1,name=targetRef"`

	// Controls how the autoscaler computes recommended resources.
	// The resource policy may be used to set constraints on the recommendations
	// for individual containers. If not specified, the autoscaler computes recommended
	// resources for all containers in the pod, without additional constraints.
	// +optional
	ResourcePolicy *PodResourcePolicy `json:"resourcePolicy,omitempty" protobuf:"bytes,3,opt,name=resourcePolicy"`

	Config *WorkloadTypeThresholdConfig `json:"config,omitempty" protobuf:"bytes,4,opt,name=config"`
}

// WorkloadType controls when autoscaler applies changes to the pod resoures.
// +kubebuilder:validation:Enum=Off;Initial;Recreate;Auto
type WorkloadType string

const (
	// WorkloadTypeOff means that autoscaler never changes Pod resources.
	// The recommender still sets the recommended resources in the
	// Workload object. This can be used for a "dry run".
	WorkloadTypeCPU WorkloadType = "CPU"
	// WorkloadTypeInitial means that autoscaler only assigns resources on pod
	// creation and does not change them during the lifetime of the pod.
	WorkloadTypeMemory WorkloadType = "Memory"
	// WorkloadTypeRecreate means that autoscaler assigns resources on pod
	// creation and additionally can update them during the lifetime of the
	// pod by deleting and recreating the pod.
	WorkloadTypeDiskIO WorkloadType = "DiskIO"
	// WorkloadTypeAuto means that autoscaler assigns resources on pod creation
	// and additionally can update them during the lifetime of the pod,
	// using any available update method. Currently this is equivalent to
	// Recreate, which is the only available update method.
	WorkloadTypeNetworkIO WorkloadType = "NetworkIO"

	WorkloadTypeNone WorkloadType = "None"
)

type WorkloadTypeThresholdConfig struct {
	CPUAmountThreshold            *int64   `json:"cpuAmountThreshold,omitempty" protobuf:"bytes,1,opt,name=cpuAmountThreshold"`
	CPURatioThreshold             *float64 `json:"cpuRatioThreshold,omitempty" protobuf:"bytes,2,opt,name=cpuRatioThreshold"`
	MemoryAmountThreshold         *int64   `json:"memoryAmountThreshold,omitempty" protobuf:"bytes,3,opt,name=memoryAmountThreshold"`
	MemoryRatioThreshold          *float64 `json:"memoryRatioThreshold,omitempty" protobuf:"bytes,4,opt,name=memoryRatioThreshold"`
	FsReadBytesThreshold          *float64 `json:"fsReadBytesThreshold,omitempty" protobuf:"bytes,5,opt,name=fsReadBytesThreshold"`
	FsWriteBytesThreshold         *float64 `json:"fsWriteBytesThreshold,omitempty" protobuf:"bytes,6,opt,name=fsWriteBytesThreshold"`
	NetworkTransmitBytesThreshold *float64 `json:"networkTransmitBytesThreshold,omitempty" protobuf:"bytes,7,opt,name=networkTransmitBytesThreshold"`
	NetworkReceiveBytesThreshold  *float64 `json:"networkReceiveBytesThreshold,omitempty" protobuf:"bytes,8,opt,name=networkReceiveBytesThreshold"`
	HttpRequestLatencyThreshold   *float64 `json:"httpRequestLatencyThreshold,omitempty" protobuf:"bytes,9,opt,name=httpRequestLatencyThreshold"`
	HttpQPSThreshold              *float64 `json:"httpQPSThreshold,omitempty" protobuf:"bytes,10,opt,name=httpQPSThreshold"`
	StabilizationWindowSeconds    *int32   `json:"stabilizationWindowSeconds,omitempty" protobuf:"varint,11,opt,name=stabilizationWindowSeconds"`
}

// PodResourcePolicy controls how autoscaler computes the recommended resources
// for containers belonging to the pod. There can be at most one entry for every
// named container and optionally a single wildcard entry with `containerName` = '*',
// which handles all containers that don't have individual policies.
type PodResourcePolicy struct {
	// Per-container resource policies.
	// +optional
	// +patchMergeKey=containerName
	// +patchStrategy=merge
	ContainerPolicies []ContainerResourcePolicy `json:"containerPolicies,omitempty" patchStrategy:"merge" patchMergeKey:"containerName" protobuf:"bytes,1,rep,name=containerPolicies"`
}

// ContainerResourcePolicy controls how autoscaler computes the recommended
// resources for a specific container.
type ContainerResourcePolicy struct {
	// Name of the container or DefaultContainerResourcePolicy, in which
	// case the policy is used by the containers that don't have their own
	// policy specified.
	ContainerName string `json:"containerName,omitempty" protobuf:"bytes,1,opt,name=containerName"`
	// Whether autoscaler is enabled for the container. The default is "Auto".
	// +optional
	Mode *ContainerScalingMode `json:"mode,omitempty" protobuf:"bytes,2,opt,name=mode"`
	// Specifies the minimal amount of resources that will be recommended
	// for the container. The default is no minimum.
	// +optional
	MinAllowed v1.ResourceList `json:"minAllowed,omitempty" protobuf:"bytes,3,rep,name=minAllowed,casttype=ResourceList,castkey=ResourceName"`
	// Specifies the maximum amount of resources that will be recommended
	// for the container. The default is no maximum.
	// +optional
	MaxAllowed v1.ResourceList `json:"maxAllowed,omitempty" protobuf:"bytes,4,rep,name=maxAllowed,casttype=ResourceList,castkey=ResourceName"`

	// Specifies the type of recommendations that will be computed
	// (and possibly applied) by Workload.
	// If not specified, the default of [ResourceCPU, ResourceMemory] will be used.
	ControlledResources *[]v1.ResourceName `json:"controlledResources,omitempty" patchStrategy:"merge" protobuf:"bytes,5,rep,name=controlledResources"`

	// Specifies which resource values should be controlled.
	// The default is "RequestsAndLimits".
	// +optional
	ControlledValues *ContainerControlledValues `json:"controlledValues,omitempty" protobuf:"bytes,6,rep,name=controlledValues"`
}

const (
	// DefaultContainerResourcePolicy can be passed as
	// ContainerResourcePolicy.ContainerName to specify the default policy.
	DefaultContainerResourcePolicy = "*"
)

// ContainerScalingMode controls whether autoscaler is enabled for a specific
// container.
// +kubebuilder:validation:Enum=Auto;Off
type ContainerScalingMode string

const (
	// ContainerScalingModeAuto means autoscaling is enabled for a container.
	ContainerScalingModeAuto ContainerScalingMode = "Auto"
	// ContainerScalingModeOff means autoscaling is disabled for a container.
	ContainerScalingModeOff ContainerScalingMode = "Off"
)

// ContainerControlledValues controls which resource value should be autoscaled.
// +kubebuilder:validation:Enum=RequestsAndLimits;RequestsOnly
type ContainerControlledValues string

const (
	// ContainerControlledValuesRequestsAndLimits means resource request and limits
	// are scaled automatically. The limit is scaled proportionally to the request.
	ContainerControlledValuesRequestsAndLimits ContainerControlledValues = "RequestsAndLimits"
	// ContainerControlledValuesRequestsOnly means only requested resource is autoscaled.
	ContainerControlledValuesRequestsOnly ContainerControlledValues = "RequestsOnly"
)

// WorkloadStatus describes the runtime state of the autoscaler.
type WorkloadStatus struct {
	// The most recently computed amount of resources recommended by the
	// autoscaler for the controlled pods.
	// +optional
	Recommendation *RecommendedPodResources `json:"recommendation,omitempty" protobuf:"bytes,1,opt,name=recommendation"`

	// Conditions is the set of conditions required for this autoscaler to scale its target,
	// and indicates whether or not those conditions are met.
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Conditions []WorkloadCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,2,rep,name=conditions"`
}

// RecommendedPodResources is the recommendation of resources computed by
// autoscaler. It contains a recommendation for each container in the pod
// (except for those with `ContainerScalingMode` set to 'Off').
type RecommendedPodResources struct {
	// Resources recommended by the autoscaler for each container.
	// +optional
	ContainerRecommendations []RecommendedContainerResources `json:"containerRecommendations,omitempty" protobuf:"bytes,1,rep,name=containerRecommendations"`

	// minReplicas is the lower limit for the number of replicas
	MinReplicas int32 `json:"minReplicas" protobuf:"varint,2,opt,name=minReplicas"`
	// maxReplicas is the upper limit for the number of replicas .
	MaxReplicas int32 `json:"maxReplicas" protobuf:"varint,3,opt,name=maxReplicas"`
}

type ContainerResource struct {
	Request v1.ResourceList `json:"request" protobuf:"bytes,1,rep,name=request,casttype=ResourceList,castkey=ResourceName"`
	Limit   v1.ResourceList `json:"limit" protobuf:"bytes,2,rep,name=limit,casttype=ResourceList,castkey=ResourceName"`
}

// RecommendedContainerResources is the recommendation of resources computed by
// autoscaler for a specific container. Respects the container resource policy
// if present in the spec. In particular the recommendation is not produced for
// containers with `ContainerScalingMode` set to 'Off'.
type RecommendedContainerResources struct {
	// Name of the container.
	ContainerName string `json:"containerName,omitempty" protobuf:"bytes,1,opt,name=containerName"`
	// Recommended amount of resources. Observes ContainerResourcePolicy.
	Target ContainerResource `json:"target,omitempty" protobuf:"bytes,2,rep,name=target"`
	// Minimum recommended amount of resources. Observes ContainerResourcePolicy.
	// This amount is not guaranteed to be sufficient for the application to operate in a stable way, however
	// running with less resources is likely to have significant impact on performance/availability.
	// +optional
	LowerBound ContainerResource `json:"lowerBound,omitempty" protobuf:"bytes,3,rep,name=lowerBound"`
	// Maximum recommended amount of resources. Observes ContainerResourcePolicy.
	// Any resources allocated beyond this value are likely wasted. This value may be larger than the maximum
	// amount of application is actually capable of consuming.
	// +optional
	UpperBound ContainerResource `json:"upperBound,omitempty" protobuf:"bytes,4,rep,name=upperBound"`

	WorkloadTypes []WorkloadType `json:"workloadTypes,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,5,rep,name=workloadTypes"`

	LastScalingResourceTime metav1.Time `json:"lastScalingResourceTime,omitempty" protobuf:"bytes,6,opt,name=lastScalingResourceTime"`
}

// WorkloadConditionType are the valid conditions of
// a Workload.
type WorkloadConditionType string

var (
	// RecommendationProvided indicates whether the Workload recommender was able to calculate a recommendation.
	RecommendationProvided WorkloadConditionType = "RecommendationProvided"
	// LowConfidence indicates whether the Workload recommender has low confidence in the recommendation for
	// some of containers.
	LowConfidence WorkloadConditionType = "LowConfidence"
	// NoPodsMatched indicates that label selector used with Workload object didn't match any pods.
	NoPodsMatched WorkloadConditionType = "NoPodsMatched"
	// FetchingHistory indicates that Workload recommender is in the process of loading additional history samples.
	FetchingHistory WorkloadConditionType = "FetchingHistory"
	// ConfigDeprecated indicates that this Workload configuration is deprecated
	// and will stop being supported soon.
	ConfigDeprecated WorkloadConditionType = "ConfigDeprecated"
	// ConfigUnsupported indicates that this Workload configuration is unsupported
	// and recommendations will not be provided for it.
	ConfigUnsupported WorkloadConditionType = "ConfigUnsupported"
)

// WorkloadCondition describes the state of
// a Workload at a certain point.
type WorkloadCondition struct {
	// type describes the current condition
	Type WorkloadConditionType `json:"type" protobuf:"bytes,1,name=type"`
	// status is the status of the condition (True, False, Unknown)
	Status v1.ConditionStatus `json:"status" protobuf:"bytes,2,name=status"`
	// lastTransitionTime is the last time the condition transitioned from
	// one status to another
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,3,opt,name=lastTransitionTime"`
	// reason is the reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,4,opt,name=reason"`
	// message is a human-readable explanation containing details about
	// the transition
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,5,opt,name=message"`
}

// +genclient
// +genclient:noStatus
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:storageversion
// +kubebuilder:resource:shortName=wlcheckpoint

// WorkloadCheckpoint is the checkpoint of the internal state of Workload that
// is used for recovery after recommender's restart.
type WorkloadCheckpoint struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the checkpoint.
	// More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#spec-and-status.
	// +optional
	Spec WorkloadCheckpointSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`

	// Data of the checkpoint.
	// +optional
	Status WorkloadCheckpointStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// WorkloadCheckpointList is a list of WorkloadCheckpoint objects.
type WorkloadCheckpointList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`
	Items           []WorkloadCheckpoint `json:"items"`
}

// WorkloadCheckpointSpec is the specification of the checkpoint object.
type WorkloadCheckpointSpec struct {
	// Name of the Workload object that stored WorkloadCheckpoint object.
	WorkloadObjectName string `json:"workloadObjectName,omitempty" protobuf:"bytes,1,opt,name=workloadObjectName"`

	// Name of the checkpointed container.
	ContainerName string `json:"containerName,omitempty" protobuf:"bytes,2,opt,name=containerName"`
}

// WorkloadCheckpointStatus contains data of the checkpoint.
type WorkloadCheckpointStatus struct {
	// The time when the status was last refreshed.
	// +nullable
	LastUpdateTime metav1.Time `json:"lastUpdateTime,omitempty" protobuf:"bytes,1,opt,name=lastUpdateTime"`

	// Version of the format of the stored data.
	Version string `json:"version,omitempty" protobuf:"bytes,2,opt,name=version"`

	// Checkpoint of histogram for consumption of CPU.
	CPUHistogram HistogramCheckpoint `json:"cpuHistogram,omitempty" protobuf:"bytes,3,rep,name=cpuHistograms"`

	// Checkpoint of histogram for consumption of memory.
	MemoryHistogram HistogramCheckpoint `json:"memoryHistogram,omitempty" protobuf:"bytes,4,rep,name=memoryHistogram"`

	// Timestamp of the fist sample from the histograms.
	// +nullable
	FirstSampleStart metav1.Time `json:"firstSampleStart,omitempty" protobuf:"bytes,5,opt,name=firstSampleStart"`

	// Timestamp of the last sample from the histograms.
	// +nullable
	LastSampleStart metav1.Time `json:"lastSampleStart,omitempty" protobuf:"bytes,6,opt,name=lastSampleStart"`

	// Total number of samples in the histograms.
	TotalSamplesCount int `json:"totalSamplesCount,omitempty" protobuf:"bytes,7,opt,name=totalSamplesCount"`
}

// HistogramCheckpoint contains data needed to reconstruct the histogram.
type HistogramCheckpoint struct {
	// Reference timestamp for samples collected within this histogram.
	// +nullable
	ReferenceTimestamp metav1.Time `json:"referenceTimestamp,omitempty" protobuf:"bytes,1,opt,name=referenceTimestamp"`

	// Map from bucket index to bucket weight.
	// +kubebuilder:validation:Type=object
	// +kubebuilder:validation:XPreserveUnknownFields
	BucketWeights map[int]uint32 `json:"bucketWeights,omitempty" protobuf:"bytes,2,opt,name=bucketWeights"`

	// Sum of samples to be used as denominator for weights from BucketWeights.
	TotalWeight float64 `json:"totalWeight,omitempty" protobuf:"bytes,3,opt,name=totalWeight"`
}
