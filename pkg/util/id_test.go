package util

import (
	"fmt"
	"testing"
)

func TestDecodeClusterID(t *testing.T) {
	// 定义输入和预期输出
	input := "cls-7a89cqhd"
	expectedOutput := "tke"

	// 调用 DecodeClusterID 函数
	_, regionID, err := DecodeClusterID(input)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("DecodeClusterID(%s) = %s; want %s", input, err, expectedOutput)
	}

	result := GetClusterType(regionID)

	// 使用断言验证结果
	if result != expectedOutput {
		t.Errorf("DecodeClusterID(%s) = %s; want %s", input, result, expectedOutput)
	}
}

func TestGetClusterType(t *testing.T) {
	id, err := GetRegionAliasFromClusterID("cls-g4y1dw0p")
	if err != nil {
		t.Errorf("GetRegionAliasFromClusterID(%s) = %s; want %s", "cls-jjxvfdvj", err, "tke")
	}
	fmt.Println(id)
}
