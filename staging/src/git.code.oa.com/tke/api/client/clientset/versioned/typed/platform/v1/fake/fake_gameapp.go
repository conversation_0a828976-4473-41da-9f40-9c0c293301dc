/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeGameApps implements GameAppInterface
type FakeGameApps struct {
	Fake *FakePlatformV1
}

var gameappsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "gameapps"}

var gameappsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "GameApp"}

// Get takes name of the gameApp, and returns the corresponding gameApp object, and an error if there is any.
func (c *FakeGameApps) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.GameApp, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(gameappsResource, name), &platformv1.GameApp{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GameApp), err
}

// List takes label and field selectors, and returns the list of GameApps that match those selectors.
func (c *FakeGameApps) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.GameAppList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(gameappsResource, gameappsKind, opts), &platformv1.GameAppList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.GameAppList{ListMeta: obj.(*platformv1.GameAppList).ListMeta}
	for _, item := range obj.(*platformv1.GameAppList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested gameApps.
func (c *FakeGameApps) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(gameappsResource, opts))
}

// Create takes the representation of a gameApp and creates it.  Returns the server's representation of the gameApp, and an error, if there is any.
func (c *FakeGameApps) Create(ctx context.Context, gameApp *platformv1.GameApp, opts v1.CreateOptions) (result *platformv1.GameApp, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(gameappsResource, gameApp), &platformv1.GameApp{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GameApp), err
}

// Update takes the representation of a gameApp and updates it. Returns the server's representation of the gameApp, and an error, if there is any.
func (c *FakeGameApps) Update(ctx context.Context, gameApp *platformv1.GameApp, opts v1.UpdateOptions) (result *platformv1.GameApp, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(gameappsResource, gameApp), &platformv1.GameApp{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GameApp), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeGameApps) UpdateStatus(ctx context.Context, gameApp *platformv1.GameApp, opts v1.UpdateOptions) (*platformv1.GameApp, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(gameappsResource, "status", gameApp), &platformv1.GameApp{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GameApp), err
}

// Delete takes name of the gameApp and deletes it. Returns an error if one occurs.
func (c *FakeGameApps) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(gameappsResource, name), &platformv1.GameApp{})
	return err
}

// Patch applies the patch and returns the patched gameApp.
func (c *FakeGameApps) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.GameApp, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(gameappsResource, name, pt, data, subresources...), &platformv1.GameApp{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.GameApp), err
}
