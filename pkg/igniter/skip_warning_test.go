package igniter

import (
	"strings"
	"testing"

	"git.woa.com/kmetis/starship-engine/pkg/internalapi"
	pb "git.woa.com/tke/tops/pkg/coredns/pb"
)

func TestParseSkipWarningPreCheckItem(t *testing.T) {
	tests := []struct {
		name           string
		extendInfoStr  string
		expectedResult bool
	}{
		{
			name:           "空字符串",
			extendInfoStr:  "",
			expectedResult: false,
		},
		{
			name:           "空JSON对象",
			extendInfoStr:  "{}",
			expectedResult: false,
		},
		{
			name:           "SkipWarningPreCheckItem为true",
			extendInfoStr:  `{"SkipWarningPreCheckItem":true}`,
			expectedResult: true,
		},
		{
			name:           "SkipWarningPreCheckItem为false",
			extendInfoStr:  `{"SkipWarningPreCheckItem":false}`,
			expectedResult: false,
		},
		{
			name:           "包含其他字段的完整ExtendInfo",
			extendInfoStr:  `{"Envs":[],"Args":[],"ContainerName":"test","SkipWarningPreCheckItem":true}`,
			expectedResult: true,
		},
		{
			name:           "无效JSON",
			extendInfoStr:  `{invalid json}`,
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseSkipWarningPreCheckItem(tt.extendInfoStr)
			if result != tt.expectedResult {
				t.Errorf("parseSkipWarningPreCheckItem() = %v, expected %v", result, tt.expectedResult)
			}
		})
	}
}

func TestShouldSkipWarningError(t *testing.T) {
	tests := []struct {
		name                string
		skipWarningPreCheck bool
		subtaskAction       string
		riskLevel           string
		expectedResult      bool
	}{
		{
			name:                "配置为false，不跳过任何错误",
			skipWarningPreCheck: false,
			subtaskAction:       "precheck",
			riskLevel:           "warning",
			expectedResult:      false,
		},
		{
			name:                "配置为true，预检子任务，warning级别，应该跳过",
			skipWarningPreCheck: true,
			subtaskAction:       "precheck",
			riskLevel:           "warning",
			expectedResult:      true,
		},
		{
			name:                "配置为true，发布子任务，warning级别，应该跳过",
			skipWarningPreCheck: true,
			subtaskAction:       "upgrade",
			riskLevel:           "warning",
			expectedResult:      true,
		},
		{
			name:                "配置为true，预检子任务，fatal级别，不应该跳过",
			skipWarningPreCheck: true,
			subtaskAction:       "precheck",
			riskLevel:           "fatal",
			expectedResult:      false,
		},
		{
			name:                "配置为true，后检子任务，warning级别，不应该跳过",
			skipWarningPreCheck: true,
			subtaskAction:       "postcheck",
			riskLevel:           "warning",
			expectedResult:      false,
		},
		{
			name:                "配置为true，其他子任务，warning级别，不应该跳过",
			skipWarningPreCheck: true,
			subtaskAction:       "other",
			riskLevel:           "warning",
			expectedResult:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := shouldSkipWarningError(tt.skipWarningPreCheck, tt.subtaskAction, tt.riskLevel)
			if result != tt.expectedResult {
				t.Errorf("shouldSkipWarningError() = %v, expected %v", result, tt.expectedResult)
			}
		})
	}
}

func TestCheckClusterStatusWithSkipWarning(t *testing.T) {
	// 创建测试响应数据
	response := &internalapi.DescribeAppUpgradingProgressResponse{
		Response: internalapi.DescribeAppUpgradingProgressResponseImp{
			RequestId: "test-request-id",
			Task: pb.DescribeTaskReply{
				TaskId:     123,
				Stage:      "precheck",
				Status:     "done",
				Reason:     "",
				ExtendInfo: `{"SkipWarningPreCheckItem":true}`,
				Subtasks: []*pb.SubTask{
					{
						SubtaskId: 1,
						Action:    "precheck",
						Status:    "done",
						Risks: []*pb.Risk{
							{
								Id:     1,
								Name:   "Fatal Error",
								Code:   "ERROR",
								Level:  "fatal",
								Detail: "This is a fatal error",
							},
							{
								Id:     2,
								Name:   "Warning Error",
								Code:   "ERROR",
								Level:  "warning",
								Detail: "This is a warning error",
							},
						},
					},
				},
			},
		},
	}

	tests := []struct {
		name                      string
		extendInfo                string
		expectedStage             string
		expectedStatus            string
		shouldContainWarningError bool
	}{
		{
			name:                      "不跳过warning错误",
			extendInfo:                `{"SkipWarningPreCheckItem":false}`,
			expectedStage:             "precheck",
			expectedStatus:            "failed",
			shouldContainWarningError: true,
		},
		{
			name:                      "跳过warning错误",
			extendInfo:                `{"SkipWarningPreCheckItem":true}`,
			expectedStage:             "precheck",
			expectedStatus:            "failed", // 仍然失败，因为有fatal错误
			shouldContainWarningError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试响应的ExtendInfo
			response.Response.Task.ExtendInfo = tt.extendInfo
			stage, status, reason := checkClusterStatus(response)

			if stage != tt.expectedStage {
				t.Errorf("Expected stage %s, got %s", tt.expectedStage, stage)
			}

			if status != tt.expectedStatus {
				t.Errorf("Expected status %s, got %s", tt.expectedStatus, status)
			}

			containsWarningError := strings.Contains(reason, "This is a warning error")
			if containsWarningError != tt.shouldContainWarningError {
				t.Errorf("Expected warning error in reason: %v, got: %v (reason: %s)",
					tt.shouldContainWarningError, containsWarningError, reason)
			}
		})
	}
}
