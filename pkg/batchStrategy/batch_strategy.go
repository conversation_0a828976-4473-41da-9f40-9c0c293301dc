package batchStrategy

import (
	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

const (
	RegionInterval3Min  = 180  // 3分钟间隔
	RegionInterval20Min = 1200 // 20分钟间隔
	RegionInterval1Hour = 3600 // 1小时间隔

	// 地域最小分批间隔
	MinLargeRegionBatchInterval = 3600
	PreCheckRegionBatchInterval = 180
)

const (
	OriginBatchStrategyID = iota
	RegionBatchStrategyID
	EksRunningPodsStrategyID
	NodeNumberBatchStrategyID
	BigUserBatchStrategyID
	ClusterLevelBatchStrategyID
)

type BatchConfig struct {
	Region         string
	RegionPriority int
	Clusters       []string
	Applications   []util.Application
	Pods           []util.Pod
	Interval       int64 // 批次间隔时间(秒)
	BatchId        int64 // 主批次
	SubBatchId     int64 // 子批次
}

var RegionBatchMap = map[string]int{
	// 预发布环境（清远）
	"qy": 1,

	// 国内EC地域
	"jnec": 2, "hzec": 2, "fzec": 2, "sjwec": 2, "whec": 2,
	"csec": 2, "xbec": 2, "sheec": 2, "hfeec": 2, "xiyec": 2,
	"cgoec": 2,

	// 海外地域
	"hk": 3, "sg": 3, "jp": 3, "usw": 3, "de": 3,
	"th": 3, "ru": 3, "sao": 3, "in": 3, "kr": 3,
	"ca": 3, "use": 3, "jkt": 3, "tpe": 3,

	// 国内小地域
	"gzopen": 4, "nj": 4, "tsn": 4, "cq": 4, "cd": 4,
	"szx": 4, "szjr": 4, "shjr": 4, "bjjr": 4, "shadc": 4,
	"gzwxzf": 4, "shwxzf": 4, "szjxcft": 4, "njxfcft": 4,
	"shhqcft": 4, "shhqcftfzhj": 4, "shwxzfjpyzc": 4,

	// 北上广
	"bj": 5, "sh": 5, "gz": 5,
}

var RegistryBatchStrategyMap = map[int]BatchStrategy{
	RegionBatchStrategyID:       NewRegionBatchStrategy(),
	EksRunningPodsStrategyID:    NewEksRunningPodsStrategy(),
	NodeNumberBatchStrategyID:   NewNodeNumberBatchStrategy(),
	BigUserBatchStrategyID:      NewBigUserBatchStrategy(),
	ClusterLevelBatchStrategyID: NewClusterLevelBatchStrategy(),
}

type BatchStrategy interface {
	GetName() string
	GetID() int64
	DivideBatches(clusters []BatchConfig, isMainBatch bool, options BatchStrategyOptions) ([]BatchConfig, error)
}

type AppFabricBatchStrategy interface {
	DivideBatches(task model.Task, applications []util.Application) ([]BatchConfig, error)
}

type EksPodBatchStrategy interface {
	DivideBatches(task model.Task, appAction string, pods []util.Pod) ([]BatchConfig, error)
}
