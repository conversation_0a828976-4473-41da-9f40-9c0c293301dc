/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
 
 //revive:disable
 
 
// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = 'proto2';

package git.code.oa.com.tke.api.platform.v1;

import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "v1";

message Actions {
  optional RunTime runtime = 1;

  optional NodePod nodePod = 2;

  optional CVM CVM = 3;
}

// AddonType records the all addons of platform available.
message AddonType {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // +optional
  optional AddonTypeSpec spec = 2;
}

// AddonTypeList is a resource containing a list of AddonType objects.
message AddonTypeList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // +optional
  repeated AddonType items = 2;
}

// AddonTypeSpec describes the attributes of a AddonType.
message AddonTypeSpec {
  // Addon type, one of Helm, PersistentEvent or LogCollector etc.
  optional string type = 1;

  // TenantIDs are whitelist of the addonType
  repeated string tenantids = 2;

  optional string level = 3;

  optional string latestVersion = 4;

  optional string description = 5;

  repeated VersionMap versionSpec = 6;
}

message AppSpec {
  optional string chart = 1;

  optional string chartFrom = 2;

  optional string chartNamespace = 3;

  optional string releaseName = 4;
}

message AuthenticationInfo {
  optional bytes clientCertificate = 1;

  optional bytes clientKey = 2;

  optional string commonName = 3;
}

// CBS is a kubernetes csi for using CBS.
message CBS {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional CBSSpec spec = 2;

  // +optional
  optional CBSStatus status = 3;
}

// CBSList is the whole list of all CBSs.
message CBSList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of CBSs
  repeated CBS items = 2;
}

message CBSProxyOptions {
  // Path is the URL path to use for the current proxy request to cbs-api.
  // +optional
  optional string path = 1;
}

// CBSSpec describes the attributes on a CBS.
message CBSSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 5;

  optional string rootdir = 6;
}

// CBSStatus is information about the current status of a CBS.
message CBSStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of cbs.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// CFS is a kubernetes csi for using CFS.
message CFS {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional CFSSpec spec = 2;

  // +optional
  optional CFSStatus status = 3;
}

// CFSList is the whole list of all CFSs.
message CFSList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of CFSs
  repeated CFS items = 2;
}

// CFSProxyOptions is the query options to a cfs-api proxy call.
message CFSProxyOptions {
  // Path is the URL path to use for the current proxy request to cfs-api.
  // +optional
  optional string path = 1;
}

// CFSSpec describes the attributes on a CFS.
message CFSSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 5;

  optional string rootdir = 6;
}

// CFSStatus is information about the current status of a CFS.
message CFSStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of cfs.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// CLSLogConfigProxyOptions is the query options to a kube-apiserver proxy call for CLS LogConfig crd object.
message CLSLogConfigProxyOptions {
  optional string name = 2;

  // +optional
  optional string action = 3;

  // A selector to restrict the list of returned objects by their fields.
  // Defaults to everything.
  // +optional
  optional string fieldSelector = 4;

  // Limit specifies the maximum number of results to return from the server. The server may
  // not support this field on all resource types, but if it does and more results remain it
  // will set the continue field on the returned list object.
  // +optional
  optional int64 limit = 5;

  // Continue is a token returned by the server that lets a client retrieve chunks of results
  // from the server by specifying limit. The server may reject requests for continuation tokens
  // it does not recognize and will return a 410 error if the token can no longer be used because
  // it has expired.
  // +optional
  optional string continue = 6;
}

// COS is a kubernetes csi for using COS.
message COS {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional COSSpec spec = 2;

  // +optional
  optional COSStatus status = 3;
}

// COSList is the whole list of all COSs.
message COSList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of COSs
  repeated COS items = 2;
}

// COSProxyOptions is the query options to a cos-api proxy call.
message COSProxyOptions {
  // Path is the URL path to use for the current proxy request to cos-api.
  // +optional
  optional string path = 1;
}

// COSSpec describes the attributes on a COS.
message COSSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 5;

  optional string rootdir = 6;
}

// COSStatus is information about the current status of a COS.
message COSStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of cos.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

message CVM {
  optional int32 retryCounts = 1;

  optional bool reBootCVM = 2;
}

// ChartSpec defines the desired identities of Chart.
message ChartSpec {
  optional string chart = 1;

  optional string chartFrom = 2;

  optional string chartVersion = 3;

  optional string chartNamespace = 4;

  optional string chartInstanceID = 5;
}

// Cluster is a Kubernetes cluster in TKE.
message Cluster {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional ClusterSpec spec = 2;

  // +optional
  optional ClusterStatus status = 3;
}

// ClusterAPIResource contains the GKV for the current kubernetes cluster
message ClusterAPIResource {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // groupVersion is the group and version this APIResourceList is for.
  optional string groupVersion = 2;

  // resources contains the name of the resources and if they are namespaced.
  repeated ClusterGroupAPIResource resources = 3;
}

// ClusterAPIResourceList is the whole list of all ClusterAPIResource.
message ClusterAPIResourceList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of ClusterAPIResource
  repeated ClusterAPIResource items = 2;

  // Failed Group Error
  optional string failedGroupError = 3;
}

// ClusterAPIResourceOptions is the query options.
message ClusterAPIResourceOptions {
  // +optional
  optional bool onlySecure = 1;
}

// Deprecated
// ClusterAddOn contains the AddOn component for the current kubernetes cluster
message ClusterAddOn {
}

// ClusterAddon contains the Addon component for the current kubernetes cluster
message ClusterAddon {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of addons in this set.
  // +optional
  optional ClusterAddonSpec spec = 2;

  // +optional
  optional ClusterAddonStatus status = 3;
}

// ClusterAddonKind returns a kind name
message ClusterAddonKind {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta objectMeta = 1;
}

// ClusterAddonKindList saves the addons grouped by kind
message ClusterAddonKindList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta listMeta = 1;

  // +optional
  repeated ClusterAddonKind items = 2;
}

// ClusterAddonList is the whole list of all ClusterAddon.
message ClusterAddonList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of ClusterAddon
  repeated ClusterAddon items = 2;
}

// ClusterAddonSpec indicates the specifications of the ClusterAddon.
message ClusterAddonSpec {
  // Addon type, one of Helm, PersistentEvent or LogCollector etc.
  optional string type = 1;

  // AddonLevel is level of cluster addon.
  optional string level = 2;

  // Version
  optional string version = 3;
}

// ClusterAddonStatus is information about the current status of a ClusterAddon.
message ClusterAddonStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the addon of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;
}

// ClusterAddonType records the all addons of cluster available.
message ClusterAddonType {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Addon type, one of Helm, PersistentEvent or LogCollector etc.
  optional string type = 2;

  // AddonLevel is level of cluster addon.
  optional string level = 3;

  // LatestVersion is latest version of the addon.
  optional string latestVersion = 4;

  optional string description = 5;
}

// ClusterAddonTypeList is a resource containing a list of ClusterAddonType objects.
message ClusterAddonTypeList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // +optional
  repeated ClusterAddonType items = 2;
}

// ClusterAddress contains information for the cluster's address.
message ClusterAddress {
  // Cluster address type, one of Public, ExternalIP or InternalIP.
  optional string type = 1;

  // The cluster address.
  optional string ip = 2;

  optional int32 port = 3;

  optional string proxy = 4;
}

// ClusterApplyOptions is the query options to a kube-apiserver proxy call for cluster object.
message ClusterApplyOptions {
  // +optional
  optional bool notUpdate = 1;

  optional bool isMultiClusterResources = 2;
}

// ClusterAuthentication is the resource that record the users tencentcloud accounts.
message ClusterAuthentication {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // +optional
  optional string tenantID = 2;

  // +optional
  optional string clusterName = 3;

  // +optional
  optional string ownerUIN = 4;

  // +optional
  optional string subAccountUIN = 5;

  // +optional
  optional bool serviceRole = 6;

  // +optional
  optional AuthenticationInfo authenticationInfo = 7;
}

// ClusterAuthenticationList is the whole list of all ClusterAuthentications.
message ClusterAuthenticationList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of ClusterAuthentications
  repeated ClusterAuthentication items = 2;
}

message ClusterComponent {
  optional string type = 1;

  optional ClusterComponentReplicas replicas = 2;
}

message ClusterComponentReplicas {
  optional int32 desired = 1;

  optional int32 current = 2;

  optional int32 available = 3;

  optional int32 updated = 4;
}

// ClusterCondition contains details for the current condition of this cluster.
message ClusterCondition {
  // Type is the type of the condition.
  optional string type = 1;

  // Status is the status of the condition.
  // Can be True, False, Unknown.
  optional string status = 2;

  // Last time we probed the condition.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastProbeTime = 3;

  // Last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // Unique, one-word, CamelCase reason for the condition's last transition.
  // +optional
  optional string reason = 5;

  // Human-readable message indicating details about last transition.
  // +optional
  optional string message = 6;
}

message ClusterCredential {
  // +optional
  optional bytes caCert = 1;

  // +optional
  optional bytes caKey = 2;

  // +optional
  optional bytes apiServerCert = 3;

  // +optional
  optional bytes apiServerKey = 4;

  // +optional
  optional bytes clientCert = 5;

  // +optional
  optional bytes clientKey = 6;

  // +optional
  optional bool privileged = 7;

  // +optional
  optional string token = 8;

  // +optional
  optional string kubeletToken = 9;

  // +optional
  optional string kubeProxyToken = 10;
}

message ClusterFeature {
  // +optional
  optional bool ipvs = 1;

  // +optional
  optional bool public = 2;
}

// APIResource specifies the name of a resource and whether it is namespaced.
message ClusterGroupAPIResource {
  // name is the plural name of the resource.
  optional string name = 1;

  // singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely.
  // The singularName is more correct for reporting status on a single item and both singular and plural are allowed
  // from the kubectl CLI interface.
  optional string singularName = 6;

  // namespaced indicates if a resource is namespaced or not.
  optional bool namespaced = 2;

  // group is the preferred group of the resource.  Empty implies the group of the containing resource list.
  // For subresources, this may have a different value, for example: Scale".
  optional string group = 8;

  // version is the preferred version of the resource.  Empty implies the version of the containing resource list
  // For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)".
  optional string version = 9;

  // kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')
  optional string kind = 3;

  // verbs is a list of supported kube verbs (this includes get, list, watch, create,
  // update, patch, delete, deletecollection, and proxy)
  repeated string verbs = 4;

  // shortNames is a list of suggested short names of the resource.
  repeated string shortNames = 5;

  // categories is a list of the grouped resources this resource belongs to (e.g. 'all')
  repeated string categories = 7;
}

// ClusterList is the whole list of all clusters which owned by a tenant.
message ClusterList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of clusters
  repeated Cluster items = 2;
}

message ClusterProperty {
  // +optional
  optional int32 maxClusterServiceNum = 1;

  // +optional
  optional int32 maxNodePodNum = 2;
}

message ClusterResource {
  // Capacity represents the total resources of a cluster.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> capacity = 1;

  // Allocatable represents the resources of a cluster that are available for scheduling.
  // Defaults to Capacity.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> allocatable = 2;

  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> allocated = 3;
}

// ClusterSpec is a description of a cluster.
message ClusterSpec {
  // Finalizers is an opaque list of values that must be empty to permanently remove object from storage.
  // +optional
  repeated string finalizers = 1;

  optional string tenantID = 2;

  optional string displayName = 3;

  optional string type = 4;

  // +optional
  optional string clusterCIDR = 5;

  // +optional
  repeated string publicAlternativeNames = 6;

  // +optional
  optional ClusterFeature features = 7;

  // +optional
  optional ClusterProperty properties = 8;

  // +optional
  optional string creatorUIN = 9;

  // +optional
  optional bool useRBAC = 10;
}

// ClusterStatus represents information about the status of a cluster.
message ClusterStatus {
  // +optional
  optional bool locked = 1;

  // +optional
  optional string version = 2;

  // +optional
  optional string phase = 3;

  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ClusterCondition conditions = 4;

  // A human readable message indicating details about why the cluster is in this condition.
  // +optional
  optional string message = 5;

  // A brief CamelCase message indicating details about why the cluster is in this state.
  // +optional
  optional string reason = 6;

  // List of addresses reachable to the cluster.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ClusterAddress addresses = 7;

  // +optional
  optional ClusterCredential credential = 8;

  // The addon component of the cluster
  // Deprecated
  // +optional
  optional ClusterAddOn addOns = 9;

  // +optional
  optional ClusterResource resource = 10;

  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ClusterComponent components = 11;
}

// ComponentStatus is a generic status holder for objects
message ComponentStatus {
  // Link to object
  // +optional
  optional string link = 1;

  // Name of object
  optional string name = 2;

  // Kind of object
  optional string kind = 3;

  // Object group
  // +optional
  optional string group = 4;

  // Status. Values: InProgress, Ready, Unknown
  optional string status = 5;
}

// ConfigMap holds configuration data for tke to consume.
message ConfigMap {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Data contains the configuration data.
  // Each key must consist of alphanumeric characters, '-', '_' or '.'.
  // Values with non-UTF-8 byte sequences must use the BinaryData field.
  // The keys stored in Data must not overlap with the keys in
  // the BinaryData field, this is enforced during validation process.
  // +optional
  map<string, string> data = 2;

  // BinaryData contains the binary data.
  // Each key must consist of alphanumeric characters, '-', '_' or '.'.
  // BinaryData can contain byte sequences that are not in the UTF-8 range.
  // The keys stored in BinaryData must not overlap with the ones in
  // the Data field, this is enforced during validation process.
  // +optional
  map<string, bytes> binaryData = 3;
}

// ConfigMapList is a resource containing a list of ConfigMap objects.
message ConfigMapList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of ConfigMaps.
  repeated ConfigMap items = 2;
}

// DNSAutoscaler is a kubernetes Horizontal cluster-proportional-autoscaler
message DNSAutoscaler {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional DNSAutoscalerSpec spec = 2;

  // +optional
  optional DNSAutoscalerStatus status = 3;
}

// DNSAutoscalerList is the whole list of all DNSAutoscaler which owned by a tenant.
message DNSAutoscalerList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of DNSAutoscalers
  repeated DNSAutoscaler items = 2;
}

// DNSAutoscalerSpec defines the desired identities of DNSAutoscaler.
message DNSAutoscalerSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  optional string target = 4;
}

// DNSAutoscaler Status is information about the current status of a DNSAutoscaler.
message DNSAutoscalerStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of cos.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;

  // Components is a generic status holder for the top level resource
  // +optional
  repeated ComponentStatus components = 6;
}

// DeScheduler is a addon to extend native kube-scheduler functions
message DeScheduler {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional DeSchedulerSpec spec = 2;

  // +optional
  optional DeSchedulerStatus status = 3;
}

// DeSchedulerList is the whole list of all DeScheduler.
message DeSchedulerList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of Scheduler
  repeated DeScheduler items = 2;
}

// DeSchedulerSpec describes the attributes on a DeScheduler addon.
message DeSchedulerSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  optional string prometheusBackEnd = 4;

  optional LoadThreshold loadThreshold = 5;
}

// DeSchedulerStatus is information about the current status of a DeScheduler addon.
message DeSchedulerStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of DeScheduler addon.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;
}

// DynamicScheduler is a addon to extend native kube-scheduler functions
message DynamicScheduler {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional DynamicSchedulerSpec spec = 2;

  // +optional
  optional DynamicSchedulerStatus status = 3;
}

// DynamicSchedulerList is the whole list of all DynamicScheduler.
message DynamicSchedulerList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of Scheduler
  repeated DynamicScheduler items = 2;
}

// DynamicSchedulerSpec describes the attributes on a DynamicScheduler addon.
message DynamicSchedulerSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  optional string prometheusBackEnd = 4;

  optional FilterThreshold filterThreshold = 5;

  optional ScoreThreshold scoreThreshold = 6;
}

// DynamicSchedulerStatus is information about the current status of a DynamicScheduler addon.
message DynamicSchedulerStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of DynamicScheduler addon.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;
}

// EniIpamd is a kubernetes eni ipamd.
message EniIpamd {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional EniIpamdSpec spec = 2;

  // +optional
  optional EniIpamdStatus status = 3;
}

// EniIpamdCondition contains details for the current condition of this eniipamd.
message EniIpamdCondition {
  // Type is the type of the condition.
  optional string type = 1;

  // Status is the status of the condition.
  // Can be True, False, Unknown.
  optional string status = 2;

  // Last time we probed the condition.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastProbeTime = 3;

  // Last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // Unique, one-word, CamelCase reason for the condition's last transition.
  // +optional
  optional string reason = 5;

  // Human-readable message indicating details about last transition.
  // +optional
  optional string message = 6;
}

// EniIpamdList is the whole list of all eni ipamd which owned by a tenant.
message EniIpamdList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of eni ipamd
  repeated EniIpamd items = 2;
}

message EniIpamdSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string vpcID = 3;

  optional string subnetID = 4;

  optional string subnetCidr = 5;

  optional string zone = 6;

  optional int32 virtualScale = 7;

  optional string version = 8;

  optional int32 claimExpiredSeconds = 9;
}

message EniIpamdStatus {
  // Phase is the current lifecycle phase of the eni ipamd of cluster.
  // +optional
  optional string phase = 1;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 2;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 3;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 4;

  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated EniIpamdCondition conditions = 5;

  // +optional
  optional string version = 6;
}

// Environment defines a context associating cluster and a bundle of selected apps to provide
// a environment for specific tasks, such like a.i. jobs, big data jobs, etc.
message Environment {
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  optional EnvironmentSpec spec = 2;

  optional EnvironmentStatus status = 3;
}

message EnvironmentList {
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  repeated Environment items = 2;
}

message EnvironmentSpec {
  // EnvType defines what kind of environment is defined
  optional string envType = 1;

  // TenantID and ClusterName are used to locate the cluster that is bound to this Environment
  optional string tenantID = 2;

  optional string clusterName = 3;

  optional string uin = 4;

  optional string subAccountUin = 5;

  // Users customized the environment setting by customized what Apps shall be installed
  repeated AppSpec apps = 6;
}

message EnvironmentStatus {
  // Supports maps the name of Apps to the kind of resources the environment
  // is now supporting due to the installation of the Apps
  map<string, k8s.io.apimachinery.pkg.apis.meta.v1.GroupVersionKind> supports = 1;
}

// Extender holds the parameters used to communicate with the extender. If a verb is unspecified/empty,
// it is assumed that the extender chose not to provide that extension.
message Extender {
  // URLPrefix at which the extender is available
  optional string urlPrefix = 1;

  // Verb for the filter call, empty if not supported. This verb is appended to the URLPrefix when issuing the filter call to extender.
  optional string filterVerb = 2;

  // Verb for the preempt call, empty if not supported. This verb is appended to the URLPrefix when issuing the preempt call to extender.
  optional string preemptVerb = 3;

  // Verb for the prioritize call, empty if not supported. This verb is appended to the URLPrefix when issuing the prioritize call to extender.
  optional string prioritizeVerb = 4;

  // The numeric multiplier for the node scores that the prioritize call generates.
  // The weight should be a positive integer
  optional int64 weight = 5;

  // Verb for the bind call, empty if not supported. This verb is appended to the URLPrefix when issuing the bind call to extender.
  // If this method is implemented by the extender, it is the extender's responsibility to bind the pod to apiserver. Only one extender
  // can implement this function.
  optional string bindVerb = 6;

  // EnableHTTPS specifies whether https should be used to communicate with the extender
  optional bool enableHttps = 7;

  // TLSConfig specifies the transport layer security config
  optional ExtenderTLSConfig tlsConfig = 8;

  // HTTPTimeout specifies the timeout duration for a call to the extender. Filter timeout fails the scheduling of the pod. Prioritize
  // timeout is ignored, k8s/other extenders priorities are used to select the node.
  optional int64 httpTimeout = 9;

  // NodeCacheCapable specifies that the extender is capable of caching node information,
  // so the scheduler should only send minimal information about the eligible nodes
  // assuming that the extender already cached full details of all nodes in the cluster
  optional bool nodeCacheCapable = 10;

  // ManagedResources is a list of extended resources that are managed by
  // this extender.
  // - A pod will be sent to the extender on the Filter, Prioritize and Bind
  //   (if the extender is the binder) phases iff the pod requests at least
  //   one of the extended resources in this list. If empty or unspecified,
  //   all pods will be sent to this extender.
  // - If IgnoredByScheduler is set to true for a resource, kube-scheduler
  //   will skip checking the resource in predicates.
  // +optional
  repeated ExtenderManagedResource managedResources = 11;

  // Ignorable specifies if the extender is ignorable, i.e. scheduling should not
  // fail when the extender returns an error or is not reachable.
  optional bool ignorable = 12;

  // Verb for the prebind call, empty if not supported. This verb is appended to the URLPrefix when issuing the prebind call to extender.
  optional string prebindVerb = 13;

  // Verb for the unreserve call, empty if not supported. This verb is appended to the URLPrefix when issuing the bind call to extender.
  optional string unreserveVerb = 14;
}

// ExtenderManagedResource describes the arguments of extended resources
// managed by an extender.
message ExtenderManagedResource {
  // Name is the extended resource name.
  optional string name = 1;

  // IgnoredByScheduler indicates whether kube-scheduler should ignore this
  // resource when applying predicates.
  optional bool ignoredByScheduler = 2;
}

// ExtenderTLSConfig contains settings to enable TLS with extender
message ExtenderTLSConfig {
  // Server should be accessed without verifying the TLS certificate. For testing only.
  optional bool insecure = 1;

  // ServerName is passed to the server for SNI and is used in the client to check server
  // certificates against. If ServerName is empty, the hostname used to contact the
  // server is used.
  optional string serverName = 2;

  // Server requires TLS client certificate authentication
  optional string certFile = 3;

  // Server requires TLS client certificate authentication
  optional string keyFile = 4;

  // Trusted root certificates for server
  optional string caFile = 5;

  // CertData holds PEM-encoded bytes (typically read from a client certificate file).
  // CertData takes precedence over CertFile
  optional bytes certData = 6;

  // KeyData holds PEM-encoded bytes (typically read from a client certificate key file).
  // KeyData takes precedence over KeyFile
  optional bytes keyData = 7;

  // CAData holds PEM-encoded bytes (typically read from a root certificates bundle).
  // CAData takes precedence over CAFile
  optional bytes caData = 8;
}

message FilterThreshold {
  optional float CpuAvgUsage5M = 1;

  optional float CpuMaxUsage1H = 2;

  optional float MemAvgUsage5M = 3;

  optional float MemMaxUsage1H = 4;
}

// GPUManager is a kind of device plugin for kubelet to help manage GPUs.
message GPUManager {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional GPUManagerSpec spec = 2;

  // +optional
  optional GPUManagerStatus status = 3;
}

// GPUManagerList is the whole list of all GPUManager which owned by a tenant.
message GPUManagerList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of GPUManagers
  repeated GPUManager items = 2;
}

// GPUManagerSpec describes the attributes of a GPUManager.
message GPUManagerSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;
}

// GPUManagerStatus is information about the current status of a GPUManager.
message GPUManagerStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the GPUManager of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;
}

// GameApp is a kubernetes package manager.
message GameApp {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional GameAppSpec spec = 2;

  // +optional
  optional GameAppStatus status = 3;
}

// GameAppList is the whole list of all GameApps which owned by a tenant.
message GameAppList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of GameApps
  repeated GameApp items = 2;
}

// GameAppProxyOptions is the query options to a gameapp proxy call.
message GameAppProxyOptions {
  // +optional
  optional string namespace = 1;

  // +optional
  optional string name = 2;

  // +optional
  optional string action = 3;
}

message GameAppSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional GameAppVersion version = 3;

  optional string version2 = 4;
}

message GameAppStatus {
  // Phase is the current lifecycle phase of the GameApp of cluster.
  // +optional
  optional string phase = 1;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 2;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 3;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 4;

  // +optional
  optional string version = 5;
}

message GameAppVersion {
  optional string gameAppOperator = 1;
}

// HPC is a kubernetes Horizontal pod cron autoscaler
message HPC {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional HPCSpec spec = 2;

  // +optional
  optional HPCStatus status = 3;
}

// HPCList is the whole list of all HPC which owned by a tenant.
message HPCList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of HPCs
  repeated HPC items = 2;
}

// HPCProxyOptions is the query options to a hpc proxy call.
message HPCProxyOptions {
  // +optional
  optional string namespace = 1;

  // +optional
  optional string name = 2;

  // +optional
  optional string action = 3;
}

// HPCSpec defines the desired identities of HPC.
message HPCSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  optional ChartSpec chartInfo = 4;

  optional string uin = 5;

  optional string subuin = 6;
}

// HPCStatus is information about the current status of a HPC.
message HPCStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of cos.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;

  // Components is a generic status holder for the top level resource
  // +optional
  repeated ComponentStatus components = 6;
}

// Helm is a kubernetes package manager.
message Helm {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional HelmSpec spec = 2;

  // +optional
  optional HelmStatus status = 3;
}

// HelmList is the whole list of all helms which owned by a tenant.
message HelmList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of Helms
  repeated Helm items = 2;
}

// HelmProxyOptions is the query options to a Helm-api proxy call.
message HelmProxyOptions {
  // Path is the URL path to use for the current proxy request to helm-api.
  // +optional
  optional string path = 1;
}

message HelmSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional HelmVersion version = 3;

  optional string version2 = 4;
}

message HelmStatus {
  // Phase is the current lifecycle phase of the helm of cluster.
  // +optional
  optional string phase = 1;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 2;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 3;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 4;

  // +optional
  optional string version = 5;
}

message HelmVersion {
  optional string tiller = 1;

  optional string swift = 2;

  optional string helmAPI = 3;
}

// HostItem ...
message HostItem {
  optional string domain = 1;

  optional string ip = 2;

  optional bool disabled = 3;

  optional bool v6 = 4;
}

// ImageP2P is a P2P based distribution image system for Kubernetes.
message ImageP2P {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional ImageP2PSpec spec = 2;

  // +optional
  optional ImageP2PStatus status = 3;
}

// ImageP2PList is the whole list of all ImageP2Ps.
message ImageP2PList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of P2Ps
  repeated ImageP2P items = 2;
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// ImageP2PProxyOptions is the query options to a p2p-api proxy call.
message ImageP2PProxyOptions {
  // Path is the URL path to use for the current proxy request to p2p-api.
  // +optional
  optional string path = 1;
}

// ImageP2PSpec describes the attributes on a ImageP2Ps.
message ImageP2PSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string registryAddress = 3;

  optional int64 agentDownloadRate = 4;

  optional string version = 5;

  optional int64 proxyReplicas = 6;

  optional int64 trackerReplicas = 7;

  optional string p2pAgentWorkDir = 8;

  optional string p2pProxyWorkDir = 9;
}

// ImageP2PStatus is information about the current status of a P2P.
message ImageP2PStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of p2p.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// LBCF is a kubernetes load balancer manager.
message LBCF {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional LBCFSpec spec = 2;

  // +optional
  optional LBCFStatus status = 3;
}

// LBCFList is the whole list of all helms which owned by a tenant.
message LBCFList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of LBCFs
  repeated LBCF items = 2;
}

// LBCFProxyOptions is the query options to a kube-apiserver proxy call.
message LBCFProxyOptions {
  optional string namespace = 1;

  optional string name = 2;

  optional string action = 3;
}

// LBCFSpec describes the attributes on a Helm.
message LBCFSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;
}

// LBCFStatus is information about the current status of a Helm.
message LBCFStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the helm of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// LabelPreference holds the parameters that are used to configure the corresponding priority function
message LabelPreference {
  // Used to identify node "groups"
  optional string label = 1;

  // This is a boolean flag
  // If true, higher priority is given to nodes that have the label
  // If false, higher priority is given to nodes that do not have the label
  optional bool presence = 2;
}

// LabelsPresence holds the parameters that are used to configure the corresponding predicate in scheduler policy configuration.
message LabelsPresence {
  // The list of labels that identify node "groups"
  // All of the labels should be either present (or absent) for the node to be considered a fit for hosting the pod
  repeated string labels = 1;

  // The boolean flag that indicates whether the labels should be present or absent from the node
  optional bool presence = 2;
}

message LoadThreshold {
  optional int32 CpuAvgUsage5M = 1;

  optional int32 CpuTargetUsage = 2;

  optional int32 MemAvgUsage5M = 3;

  optional int32 MemTargetUsage = 4;
}

// LogCollector is a manager to collect logs of workload.
message LogCollector {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of LogCollector.
  // +optional
  optional LogCollectorSpec spec = 2;

  // +optional
  optional LogCollectorStatus status = 3;
}

// LogCollectorList is the whole list of all LogCollector which owned by a tenant.
message LogCollectorList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of volume decorators.
  repeated LogCollector items = 2;
}

// LogCollectorProxyOptions is the query options to a kube-apiserver proxy call for LogCollector crd object.
message LogCollectorProxyOptions {
  optional string namespace = 1;

  optional string name = 2;
}

// LogCollectorSpec describes the attributes of a LogCollector.
message LogCollectorSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;
}

// LogCollectorStatus is information about the current status of a LogCollector.
message LogCollectorStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the LogCollector of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// Manager includes the username.
message Manager {
  optional string name = 1;

  optional string displayName = 2;
}

// NamespaceSet is a namespace in cluster for project.
message NamespaceSet {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of projects in this set.
  // +optional
  optional NamespaceSetSpec spec = 2;

  // +optional
  optional NamespaceSetStatus status = 3;
}

// NamespaceSetList is the whole list of all namespaces in clusters which
// owned by a project.
message NamespaceSetList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of namespaces in clusters.
  repeated NamespaceSet items = 2;
}

// NamespaceSetSpec is a description of a namespace set.
message NamespaceSetSpec {
  optional string tenantID = 1;

  optional string projectName = 2;

  optional string clusterName = 3;

  optional string namespaceName = 4;
}

// NamespaceSetStatus represents information about the status of a namespace set.
message NamespaceSetStatus {
  // Phase is the current lifecycle phase of the namespace of cluster.
  // +optional
  optional string phase = 1;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 2;

  // +optional
  optional bool locked = 3;
}

// NetworkPolicy is a addon to set network policy for Kubernetes.
message NetworkPolicy {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional NetworkPolicySpec spec = 2;

  // +optional
  optional NetworkPolicyStatus status = 3;
}

// NetworkPolicyList is the whole list of all NetworkPolicyList.
message NetworkPolicyList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of NetworkPolicy
  repeated NetworkPolicy items = 2;
}

// NetworkPolicySpec describes the attributes on a NetworkPolicy.
message NetworkPolicySpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 5;
}

// NetworkPolicyStatus is information about the current status of a NetworkPolicy.
message NetworkPolicyStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of p2p.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;
}

message NginxControllerVersion {
  optional string tiller = 1;
}

// NginxIngress is a set of kubernetes nginx ingress controller.
message NginxIngress {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // +optional
  optional NginxIngressSpec spec = 2;

  // +optional
  optional NginxIngressStatus status = 3;
}

message NginxIngressList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta listMeta = 1;

  // List of NginxIngress
  // +optional
  repeated NginxIngress items = 2;
}

// NginxIngressProxyOptions is the query options to a kube-apiserver proxy call for LogCollector crd object.
message NginxIngressProxyOptions {
  optional string name = 2;
}

message NginxIngressSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  optional NginxControllerVersion VersionDeprecated = 4;
}

message NginxIngressStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of the LogCollector of cluster.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// NodeLocalDNSCache is a kubernetes node local dns cache
message NodeLocalDNSCache {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional NodeLocalDNSCacheSpec spec = 2;

  // +optional
  optional NodeLocalDNSCacheStatus status = 3;
}

// NodeLocalDNSCacheList is the whole list of all NodeLocalDNSCache which owned by a tenant.
message NodeLocalDNSCacheList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of NodeLocalDNSCaches
  repeated NodeLocalDNSCache items = 2;
}

// NodeLocalDNSCacheSpec defines the desired identities of NodeLocalDNSCache.
message NodeLocalDNSCacheSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  optional string target = 4;
}

// NodeLocalDNSCache Status is information about the current status of a NodeLocalDNSCache.
message NodeLocalDNSCacheStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of cos.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;

  // Components is a generic status holder for the top level resource
  // +optional
  repeated ComponentStatus components = 6;
}

message NodePod {
  optional int32 retryCounts = 1;

  optional bool evict = 2;
}

// NodeProblemDetector is the whole list of all NPD which owned by a tenant.
message NodeProblemDetector {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional NodeProblemDetectorSpec spec = 2;

  // +optional
  optional NodeProblemDetectorStatus status = 3;
}

// NodeProblemDetectorList is the whole list of all NodeProblemDetector.
message NodeProblemDetectorList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of NPDs.
  repeated NodeProblemDetector items = 2;
}

// NodeProblemDetectorSpec describes the attributes on a NodeProblemDetector.
message NodeProblemDetectorSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  optional bool selfcure = 4;

  optional string uin = 5;

  optional string subuin = 6;

  repeated Policy policys = 7;
}

// NodeProblemDetectorStatus is information about the current status of a NodeProblemDetector.
message NodeProblemDetectorStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of npd.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;
}

// OLM is a tool for operator lifecycle manager on kubernetes
message OLM {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional OLMSpec spec = 2;

  // +optional
  optional OLMStatus status = 3;
}

// OLMList is the whole list of all OLM which owned by a tenant.
message OLMList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of OLMs
  repeated OLM items = 2;
}

// OLMProxyOptions is the query options to a hpc proxy call.
message OLMProxyOptions {
  // +optional
  optional string namespace = 1;

  // +optional
  optional string name = 2;

  // +optional
  optional string action = 3;
}

// OLMSpec defines the desired identities of OLM.
message OLMSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  optional ChartSpec chartInfo = 4;

  optional string uin = 5;

  optional string subuin = 6;
}

// OLMStatus is information about the current status of a OLM.
message OLMStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of cos.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;

  // Components is a generic status holder for the top level resource
  // +optional
  repeated ComponentStatus components = 6;
}

// OOMGuard is a addon to avoid frequently cgroup OOM for Kubernetes.
message OOMGuard {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional OOMGuardSpec spec = 2;

  // +optional
  optional OOMGuardStatus status = 3;
}

// OOMGuardList is the whole list of all OOMGuardList.
message OOMGuardList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of OOMGuard
  repeated OOMGuard items = 2;
}

// OOMGuardSpec describes the attributes on a OOMGuard.
message OOMGuardSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 5;
}

// OOMGuardStatus is information about the current status of a OOMGuard.
message OOMGuardStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of p2p.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;
}

message PersistentBackEnd {
  optional StorageBackEndCLS cls = 1;

  optional StorageBackEndES es = 2;
}

// Persistent event is a recorder of kubernetes event.
message PersistentEvent {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional PersistentEventSpec spec = 2;

  // +optional
  optional PersistentEventStatus status = 3;
}

// PersistentEvent is the whole list of all clusters which owned by a tenant.
message PersistentEventList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of PersistentEvents
  repeated PersistentEvent items = 2;
}

message PersistentEventSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional PersistentBackEnd persistentBackEnd = 3;

  optional string version = 4;
}

message PersistentEventStatus {
  // Phase is the current lifecycle phase of the persistent event of cluster.
  // +optional
  optional string phase = 1;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 2;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 3;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 4;

  // +optional
  optional string version = 5;
}

message Policy {
  optional string conditionType = 1;

  optional Actions actions = 2;
}

// PredicateArgument represents the arguments to configure predicate functions in scheduler policy configuration.
// Only one of its members may be specified
message PredicateArgument {
  // The predicate that provides affinity for pods belonging to a service
  // It uses a label to identify nodes that belong to the same "group"
  optional ServiceAffinity serviceAffinity = 1;

  // The predicate that checks whether a particular node has a certain label
  // defined or not, regardless of value
  optional LabelsPresence labelsPresence = 2;
}

// PredicatePolicy describes a struct of a predicate policy.
message PredicatePolicy {
  // Identifier of the predicate policy
  // For a custom predicate, the name can be user-defined
  // For the Kubernetes provided predicates, the name is the identifier of the pre-defined predicate
  optional string name = 1;

  // Holds the parameters to configure the given predicate
  optional PredicateArgument argument = 2;
}

// PriorityArgument represents the arguments to configure priority functions in scheduler policy configuration.
// Only one of its members may be specified
message PriorityArgument {
  // The priority function that ensures a good spread (anti-affinity) for pods belonging to a service
  // It uses a label to identify nodes that belong to the same "group"
  optional ServiceAntiAffinity serviceAntiAffinity = 1;

  // The priority function that checks whether a particular node has a certain label
  // defined or not, regardless of value
  optional LabelPreference labelPreference = 2;

  // The RequestedToCapacityRatio priority function is parametrized with function shape.
  optional RequestedToCapacityRatioArguments requestedToCapacityRatioArguments = 3;
}

// PriorityPolicy describes a struct of a priority policy.
message PriorityPolicy {
  // Identifier of the priority policy
  // For a custom priority, the name can be user-defined
  // For the Kubernetes provided priority functions, the name is the identifier of the pre-defined priority function
  optional string name = 1;

  // The numeric multiplier for the node scores that the priority function generates
  // The weight should be non-zero and can be a positive or a negative integer
  optional int64 weight = 2;

  // Holds the parameters to configure the given priority function
  optional PriorityArgument argument = 3;
}

// Project is a logical top-level container for a set of kubernetes resources.
message Project {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of projects in this set.
  // +optional
  optional ProjectSpec spec = 2;

  // +optional
  optional ProjectStatus status = 3;
}

// ProjectList is the whole list of all projects which owned by a tenant.
message ProjectList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of projects/namespaces.
  repeated Project items = 2;
}

// ProjectSpec is a description of a project.
message ProjectSpec {
  optional string tenantID = 1;

  optional string displayName = 2;

  optional string description = 3;

  // +optional
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated Manager managers = 4;
}

// ProjectStatus represents information about the status of a project.
message ProjectStatus {
  // +optional
  optional bool locked = 1;
}

// QGPU is a better GPU manager for GPU sharing and isolation.
message QGPU {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional QGPUSpec spec = 2;

  // +optional
  optional QGPUStatus status = 3;
}

// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// QGPUList is the whole list of all QGPU.
message QGPUList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of QGPU
  repeated QGPU items = 2;
}

// QGPUSpec describes the attributes on a QGPUSpec.
message QGPUSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string priority = 3;

  optional string version = 5;
}

// QGPUStatus is information about the current status of a QGPUStatus.
message QGPUStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of p2p.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;
}

// RecommendationProxyOptions is the query options to a kube-apiserver proxy call for Recommendation crd object.
message RecommendationProxyOptions {
  optional string namespace = 1;
}

message Registry {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // +optional
  optional RegistrySpec spec = 2;
}

// RegistryList is a resource containing a list of Registry objects.
message RegistryList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // +optional
  repeated Registry items = 2;
}

message RegistrySpec {
  // +optional
  optional string tenantID = 1;

  // +optional
  optional string displayName = 2;

  // +optional
  optional string clusterName = 3;

  // +optional
  optional string url = 4;

  // +optional
  optional string userName = 5;

  // +optional
  optional string password = 6;
}

// RequestedToCapacityRatioArguments holds arguments specific to RequestedToCapacityRatio priority function.
message RequestedToCapacityRatioArguments {
  // Array of point defining priority function shape.
  repeated UtilizationShapePoint shape = 1;

  repeated ResourceSpec resources = 2;
}

// ResourceSpec represents single resource and weight for bin packing of priority RequestedToCapacityRatioArguments.
message ResourceSpec {
  // Name of the resource to be managed by RequestedToCapacityRatio function.
  optional string name = 1;

  // Weight of the resource.
  optional int64 weight = 2;
}

message RunTime {
  optional int32 retryCounts = 1;

  optional bool reStartDokcer = 2;

  optional bool reStartKubelet = 3;
}

// SchedulerPolicy describes a struct for a policy resource used in api.
message SchedulerPolicy {
  // Holds the information to configure the fit predicate functions
  repeated PredicatePolicy predicates = 1;

  // Holds the information to configure the priority functions
  repeated PriorityPolicy priorities = 2;

  // Holds the information to communicate with the extender(s)
  repeated Extender extenders = 3;

  // RequiredDuringScheduling affinity is not symmetric, but there is an implicit PreferredDuringScheduling affinity rule
  // corresponding to every RequiredDuringScheduling affinity rule.
  // HardPodAffinitySymmetricWeight represents the weight of implicit PreferredDuringScheduling affinity rule, in the range 1-100.
  optional int32 hardPodAffinitySymmetricWeight = 4;

  // When AlwaysCheckAllPredicates is set to true, scheduler checks all
  // the configured predicates even after one or more of them fails.
  // When the flag is set to false, scheduler skips checking the rest
  // of the predicates after it finds one predicate that failed.
  optional bool alwaysCheckAllPredicates = 5;
}

message ScoreThreshold {
  optional float CpuAvgUsage5M = 1;

  optional float CpuMaxUsage1H = 2;

  optional float CpuMaxUsage1D = 3;

  optional float MemAvgUsage5M = 4;

  optional float MemMaxUsage1H = 5;

  optional float MemMaxUsage1D = 6;
}

// ServiceAffinity holds the parameters that are used to configure the corresponding predicate in scheduler policy configuration.
message ServiceAffinity {
  // The list of labels that identify node "groups"
  // All of the labels should match for the node to be considered a fit for hosting the pod
  repeated string labels = 1;
}

// ServiceAntiAffinity holds the parameters that are used to configure the corresponding priority function
message ServiceAntiAffinity {
  // Used to identify node "groups"
  optional string label = 1;
}

message StorageBackEndCLS {
  optional string logSetID = 1;

  optional string topicID = 2;
}

message StorageBackEndES {
  optional string ip = 1;

  optional int32 port = 2;

  optional string scheme = 3;

  optional string indexName = 4;
}

// Tcr is a enterprise private docker registry.
message Tcr {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of clusters in this set.
  // +optional
  optional TcrSpec spec = 2;

  // +optional
  optional TcrStatus status = 3;
}

// TcrItem ...
message TcrItem {
  optional string name = 1;

  optional string registrySource = 2;

  optional string username = 3;

  optional string password = 4;

  optional string server = 5;

  optional string namespaces = 6;

  optional string serviceAccounts = 7;
}

// TcrList is the whole list of all TCRs.
message TcrList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of TCRs
  repeated Tcr items = 2;
}

// TcrSpec describes the attributes of the Tcr.
message TcrSpec {
  optional string tenantID = 1;

  optional string clusterName = 2;

  optional string version = 3;

  repeated HostItem hosts = 4;

  repeated TcrItem tcrss = 5;

  optional bool accelerated = 6;

  optional string acceleratedNamespaces = 7;
}

// TcrStatus is information about the current status of a TCR.
message TcrStatus {
  // +optional
  optional string version = 1;

  // Phase is the current lifecycle phase of TCR.
  // +optional
  optional string phase = 2;

  // Reason is a brief CamelCase string that describes any failure.
  // +optional
  optional string reason = 3;

  // RetryCount is a int between 0 and 5 that describes the time of retrying initializing.
  // +optional
  optional int32 retryCount = 4;

  // LastReInitializingTimestamp is a timestamp that describes the last time of retrying initializing.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastReInitializingTimestamp = 5;
}

// UtilizationShapePoint represents single point of priority function shape.
message UtilizationShapePoint {
  // Utilization (x axis). Valid values are 0 to 100. Fully utilized node maps to 100.
  optional int32 utilization = 1;

  // Score assigned to given utilization (y axis). Valid values are 0 to 10.
  optional int32 score = 2;
}

// VersionMap indicates the specifications of every componet version in the addon.
message VersionMap {
  optional string name = 1;

  optional string version = 2;

  // FullImagePath can be used for overwrite addon componet .Spec.Image
  optional string fullImagePath = 3;
}

