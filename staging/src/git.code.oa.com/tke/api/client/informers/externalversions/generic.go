/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by informer-gen. DO NOT EDIT.

package externalversions

import (
	"fmt"

	v1 "git.code.oa.com/tke/api/platform/v1"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	cache "k8s.io/client-go/tools/cache"
)

// GenericInformer is type of SharedIndexInformer which will locate and delegate to other
// sharedInformers based on type
type GenericInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() cache.GenericLister
}

type genericInformer struct {
	informer cache.SharedIndexInformer
	resource schema.GroupResource
}

// Informer returns the SharedIndexInformer.
func (f *genericInformer) Informer() cache.SharedIndexInformer {
	return f.informer
}

// Lister returns the GenericLister.
func (f *genericInformer) Lister() cache.GenericLister {
	return cache.NewGenericLister(f.Informer().GetIndexer(), f.resource)
}

// ForResource gives generic access to a shared informer of the matching type
// TODO extend this to unknown resources with a client pool
func (f *sharedInformerFactory) ForResource(resource schema.GroupVersionResource) (GenericInformer, error) {
	switch resource {
	// Group=platform.tke, Version=v1
	case v1.SchemeGroupVersion.WithResource("addontypes"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().AddonTypes().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("cbss"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().CBSs().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("cfss"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().CFSs().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("coss"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().COSs().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("clusters"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Clusters().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("clusteraddonkinds"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().ClusterAddonKinds().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("clusterauthentications"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().ClusterAuthentications().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("configmaps"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().ConfigMaps().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("dnsautoscalers"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().DNSAutoscalers().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("deschedulers"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().DeSchedulers().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("dynamicschedulers"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().DynamicSchedulers().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("eniipamds"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().EniIpamds().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("environments"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Environments().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("gpumanagers"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().GPUManagers().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("gameapps"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().GameApps().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("hpcs"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().HPCs().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("helms"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Helms().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("imagep2ps"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().ImageP2Ps().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("lbcfs"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().LBCFs().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("logcollectors"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().LogCollectors().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("namespacesets"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().NamespaceSets().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("networkpolicies"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().NetworkPolicies().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("nginxingresses"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().NginxIngresses().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("nodelocaldnscaches"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().NodeLocalDNSCaches().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("nodeproblemdetectors"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().NodeProblemDetectors().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("olms"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().OLMs().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("oomguards"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().OOMGuards().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("persistentevents"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().PersistentEvents().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("projects"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Projects().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("qgpus"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().QGPUs().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("registries"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Registries().Informer()}, nil
	case v1.SchemeGroupVersion.WithResource("tcrs"):
		return &genericInformer{resource: resource.GroupResource(), informer: f.Platform().V1().Tcrs().Informer()}, nil

	}

	return nil, fmt.Errorf("no informer found for %v", resource)
}
