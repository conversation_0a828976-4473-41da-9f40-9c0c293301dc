/*
Copyright 2019 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Package v1beta1 contains definitions of ClusterScaler related objects.
package v1beta1

import (
	autoscaling "k8s.io/api/autoscaling/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ClusterScalerList is a list of ClusterScaler objects.
type ClusterScalerList struct {
	metav1.TypeMeta `json:",inline"`
	// metadata is the standard list metadata.
	// +optional
	metav1.ListMeta `json:"metadata" protobuf:"bytes,1,opt,name=metadata"`

	// items is the list of ClusterScaler objects.
	Items []ClusterScaler `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:storageversion
// +kubebuilder:resource:shortName=apc
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

// ClusterScaler is the scaling configuration for a k8s cluster
// ,which automatically manages pod resources based on historical and
// real time resource utilization.
type ClusterScaler struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the behavior of the autopilot cluster.
	// More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#spec-and-status.
	Spec ClusterScalerSpec `json:"spec" protobuf:"bytes,2,name=spec"`

	// Current information about the autopilot cluster.
	// +optional
	Status ClusterScalerStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// ClusterScalerSpec is the specification of the behavior of the autopilot.
type ClusterScalerSpec struct {
	Product               string                   `json:"product" protobuf:"bytes,1,name=product"`
	ClusterId             string                   `json:"clusterId" protobuf:"bytes,2,name=clusterId"`
	Region                string                   `json:"region" protobuf:"bytes,3,name=region"`
	AppId                 uint64                   `json:"appId" protobuf:"bytes,4,name=appId"`
	UserLevel             string                   `json:"userLevel,omitempty" protobuf:"bytes,5,opt,name=userLevel"`
	BigCustomer           bool                     `json:"bigCustomer,omitempty" protobuf:"bytes,6,opt,name=bigCustomer"`
	ClusterLevel          string                   `json:"clusterLevel,omitempty" protobuf:"bytes,7,opt,name=clusterLevel"`
	DryRun                bool                     `json:"dryRun" protobuf:"bytes,8,name=dryRun"`
	ComponentScalerPolicy []*ComponentScalerPolicy `json:"componentScalerPolicy,omitempty" protobuf:"bytes,9,rep,name=componentScalerPolicy"`
	Config                *MaintenanceConfig       `json:"config,omitempty" protobuf:"bytes,10,opt,name=config"`
	ForceResync           bool                     `json:"forceResync,omitempty" protobuf:"bytes,11,name=forceResync"`
}

type ResourceUtilizationPercentage struct {
	CPU    *int32 `json:"cpu,omitempty" protobuf:"bytes,1,opt,name=cpu"`
	Memory *int32 `json:"memory,omitempty" protobuf:"bytes,2,opt,name=memory"`
}

type MaintenanceConfig struct {
	// maintenanceWindowStartHour defines the start time of maintenance,Its value should be in the range of [0-23].
	MaintenanceWindowStartHour *int32 `json:"maintenanceWindowStartHour,omitempty" protobuf:"varint,1,opt,name=maintenanceWindowStartHour"`
	// maintenanceWindowEndHour defines the end time of maintenance,Its value should be in the range of [0-23].
	MaintenanceWindowEndHour *int32 `json:"maintenanceWindowEndHour,omitempty" protobuf:"varint,2,opt,name=maintenanceWindowEndHour"`
	// maintenanceWindowStartTs defines the start timestamp of maintenance.
	MaintenanceWindowStartTs *int64 `json:"maintenanceWindowStartTs,omitempty" protobuf:"varint,3,opt,name=MaintenanceWindowStartTs"`
	// maintenanceWindowEndTs defines the end timestamp of maintenance.
	MaintenanceWindowEndTs *int64 `json:"maintenanceWindowEndTs,omitempty" protobuf:"varint,4,opt,name=maintenanceWindowEndTs"`
}

type ComponentScalerPolicy struct {
	// TargetRef points to the controller managing the set of pods for the
	// autopilot to control - e.g. Deployment, StatefulSet. Workload
	// can be targeted at controller implementing scale subresource (the pod set is
	// retrieved from the controller's ScaleStatus) or some well known controllers
	// (e.g. for DaemonSet the pod set is read from the controller's spec).
	// If Workload cannot use specified target it will report
	// ConfigUnsupported condition.
	// Note that Workload does not require full implementation
	// of scale subresource - it will not use it to modify the replica count.
	// The only thing retrieved is a label selector matching pods grouped by
	// the target resource.
	TargetRef autoscaling.CrossVersionObjectReference `json:"targetRef" protobuf:"bytes,1,name=targetRef"`

	VPA *VPAPolicy `json:"vpaPolicy" protobuf:"bytes,2,opt,name=vpaPolicy"`

	HPA *HPAPolicy `json:"hpaPolicy" protobuf:"bytes,3,opt,name=hpaPolicy"`
}

type VPAPolicy struct {
	// Per-container resource policies.
	// +optional
	// +patchMergeKey=containerName
	// +patchStrategy=merge
	ContainerPolicies []ContainerResourcePolicy `json:"containerPolicies,omitempty" patchStrategy:"merge" patchMergeKey:"containerName" protobuf:"bytes,1,rep,name=containerPolicies"`
	// ScaleDownThreshold defines the scaledown threshold,,
	// If the ratio of actual used cpu/memory resources divided by request resources is less than threshold,
	// it will trigger cpu/memory scaledown.
	ScaleDownThreshold *ResourceUtilizationPercentage `json:"scaleDownThreshold" protobuf:"bytes,2,opt,name=scaleDownThreshold"`
	// ScaleUpThreshold defines the  scaleup threshold,,
	// If the ratio of actual used cpu/memory resources divided by limit resources is greater than threshold,
	// it will trigger cpu/memory scaleup.
	ScaleUpThreshold *ResourceUtilizationPercentage `json:"scaleUpThreshold" protobuf:"bytes,3,opt,name=scaleUpThreshold"`
	// ScaleDownStabWindowSeconds defines scale down stabilization window seconds
	ScaleDownStabWindowSeconds *int32 `json:"scaleDownStabWindowSeconds,omitempty" protobuf:"varint,4,opt,name=scaleDownStabWindowSeconds"`
	// scaleUpStabilizationWindowSeconds defines scale up stabilization window seconds
	ScaleUpStabWindowSeconds *int32 `json:"scaleUpStabWindowSeconds,omitempty" protobuf:"varint,5,opt,name=scaleUpStabWindowSeconds"`

	Scalers []string `json:"scalers,omitempty" protobuf:"bytes,6,rep,name=scalers"`
}

type HPAPolicy struct {
	MinReplicas *int32 `json:"minReplicas,omitempty" protobuf:"varint,1,opt,name=minReplicas"`
	// upper limit for the number of pods that can be set by the autopilot; cannot be smaller than MinReplicas.
	MaxReplicas int32 `json:"maxReplicas" protobuf:"varint,2,opt,name=maxReplicas"`
	// ScaleDownThreshold defines the scaledown threshold,,
	// If the ratio of actual used cpu/memory resources divided by request resources is less than threshold,
	// it will trigger replicas scaledown.
	ScaleDownThreshold *ResourceUtilizationPercentage `json:"scaleDownThreshold" protobuf:"bytes,3,opt,name=scaleDownThreshold"`
	// ScaleUpThreshold defines the  scaleup threshold,,
	// If the ratio of actual used cpu/memory resources divided by limit resources is greater than threshold,
	// it will trigger replicas scaleup.
	ScaleUpThreshold *ResourceUtilizationPercentage `json:"scaleUpThreshold" protobuf:"bytes,4,opt,name=scaleUpThreshold"`
	ScaleUpMode      *ReplicasScalingMode           `json:"scaleUpMode,omitempty" protobuf:"bytes,5,opt,name=scaleUpMode"`
	ScaleDownMode    *ReplicasScalingMode           `json:"scaleDownMode,omitempty" protobuf:"bytes,6,opt,name=scaleDownMode"`
}

type ReplicasScalingMode string

const (
	// ReplicasScalingModeAuto means autoscaling is enabled for a deployment.
	ReplicasScalingModeAuto ReplicasScalingMode = "Auto"
	// ReplicasScalingModeOff means autoscaling is disabled for a deployment.
	ReplicasScalingModeOff ReplicasScalingMode = "Off"
)

// ContainerResourcePolicy controls how autopilotor computes the recommended
// resources for a specific container.
type ContainerResourcePolicy struct {
	// Name of the container or DefaultContainerResourcePolicy, in which
	// case the policy is used by the containers that don't have their own
	// policy specified.
	ContainerName string `json:"containerName,omitempty" protobuf:"bytes,1,opt,name=containerName"`
	// Whether scaleup autoscaler is enabled for the container. The default is "Auto".
	// +optional
	ScaleUpMode *ContainerScalingMode `json:"scaleUpMode,omitempty" protobuf:"bytes,2,opt,name=scaleUpMode"`
	// Whether scaledown autoscaler is enabled for the container. The default is "Auto".
	// +optional
	ScaleDownMode *ContainerScalingMode `json:"scaleDownMode,omitempty" protobuf:"bytes,3,opt,name=scaleDownMode"`
	// Specifies the minimal amount of resources that will be recommended
	// for the container. The default is no minimum.
	// +optional
	MinAllowed v1.ResourceList `json:"minAllowed,omitempty" protobuf:"bytes,4,rep,name=minAllowed,casttype=ResourceList,castkey=ResourceName"`
	// Specifies the maximum amount of resources that will be recommended
	// for the container. The default is no maximum.
	// +optional
	MaxAllowed v1.ResourceList `json:"maxAllowed,omitempty" protobuf:"bytes,5,rep,name=maxAllowed,casttype=ResourceList,castkey=ResourceName"`

	// Specifies the type of recommendations that will be computed
	// (and possibly applied) by ClusterScaler.
	// If not specified, the default of [ResourceCPU, ResourceMemory] will be used.
	ControlledResources *[]v1.ResourceName `json:"controlledResources,omitempty" patchStrategy:"merge" protobuf:"bytes,6,rep,name=controlledResources"`

	// Specifies which resource values should be controlled.
	// The default is "RequestsAndLimits".
	// +optional
	ControlledValues *ContainerControlledValues `json:"controlledValues,omitempty" protobuf:"bytes,7,rep,name=controlledValues"`
}

const (
	// DefaultContainerResourcePolicy can be passed as
	// ContainerResourcePolicy.ContainerName to specify the default policy.
	DefaultContainerResourcePolicy = "*"
)

// ContainerScalingMode controls whether autopilot is enabled for a specific
// container.
// +kubebuilder:validation:Enum=Auto;Off
type ContainerScalingMode string

const (
	// ContainerScalingModeAuto means autoscaling is enabled for a container.
	ContainerScalingModeAuto ContainerScalingMode = "Auto"
	// ContainerScalingModeOff means autoscaling is disabled for a container.
	ContainerScalingModeOff ContainerScalingMode = "Off"
)

// ContainerControlledValues controls which resource value should be autoscaled.
// +kubebuilder:validation:Enum=RequestsAndLimits;RequestsOnly
type ContainerControlledValues string

const (
	// ContainerControlledValuesRequestsAndLimits means resource request and limits
	// are scaled automatically. The limit is scaled proportionally to the request.
	ContainerControlledValuesRequestsAndLimits ContainerControlledValues = "RequestsAndLimits"
	// ContainerControlledValuesRequestsOnly means only requested resource is autoscaled.
	ContainerControlledValuesRequestsOnly ContainerControlledValues = "RequestsOnly"
)

// ClusterScalerStatus describes the runtime state of the autopilot.
type ClusterScalerStatus struct {
	// Conditions is the set of conditions required for this autopilotor to scale its target,
	// and indicates whether or not those conditions are met.
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Conditions []AutopilotClusterCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,1,rep,name=conditions"`
	// lastScaleTime is the last time the AutopilotController scaled the cluster component resource(cluster level)
	LastScaleTime metav1.Time `json:"lastScaleTime,omitempty" protobuf:"bytes,2,opt,name=lastScaleTime"`
	// ReadyClusterLevel is the level at which the cluster is currently ready.
	ReadyClusterLevel string `json:"readyClusterLevel,omitempty" protobuf:"bytes,3,opt,name=readyClusterLevel"`
	// DesiredClusterLevel is the cluster level predicted by autopilot based on the number of nodes, resource utilization, and number of resources.
	DesiredClusterLevel string `json:"desiredClusterLevel,omitempty" protobuf:"bytes,4,opt,name=desiredClusterLevel"`
	// EstimatorMessage describes the predicted information for the DesiredClusterLevel
	EstimatorMessage string `json:"estimatorMessage,omitempty" protobuf:"bytes,5,opt,name=estimatorMessage"`
	// ActualClusterLevel is the cluster level calculated by autopilot based on the resources actually used by the cluster.
	ActualClusterLevel string `json:"actualClusterLevel,omitempty" protobuf:"bytes,6,opt,name=actualClusterLevel"`
	// MaxClusterLevel is the max cluster level calculated by autopilot based on the root cause.
	MaxClusterLevel string `json:"maxClusterLevel,omitempty" protobuf:"bytes,7,opt,name=maxClusterLevel"`
}

// AutopilotTaskType are the valid conditions of
// a ClusterScaler.
type AutopilotTaskType string

var (
	HPAScaleUpRelicas             AutopilotTaskType = "HPAScaleUpRelicas"
	HPAScaleDownReplicas          AutopilotTaskType = "HPAScaleDownReplicas"
	VPAScaleDown                  AutopilotTaskType = "VPAScaleDown"
	VPAScaleUp                    AutopilotTaskType = "VPAScaleUp"
	EnableClusterAutoScaling      AutopilotTaskType = "EnableClusterAutoScaling"
	DisableClusterAutoScaling     AutopilotTaskType = "DisableClusterAutoScaling"
	ReconcileClusterLevel         AutopilotTaskType = "ReconcileClusterLevel"
	EnableClusterScaleDownFeature AutopilotTaskType = "EnableClusterScaleDownFeature"
)

// AutopilotClusterCondition describes the state of
// a ClusterScaler at a certain point.
type AutopilotClusterCondition struct {
	// type describes the current condition
	Type AutopilotTaskType `json:"type" protobuf:"bytes,1,name=type"`
	// status is the status of the condition (True, False, Unknown)
	Status v1.ConditionStatus `json:"status" protobuf:"bytes,2,name=status"`
	// lastStartTime is the start time of last task execution.
	// +optional
	LastStartTime metav1.Time `json:"lastStartTime,omitempty" protobuf:"bytes,3,opt,name=lastStartTime"`
	// lastEndTime is the end time of last task execution.
	// +optional
	LastEndTime metav1.Time `json:"lastEndTime,omitempty" protobuf:"bytes,4,opt,name=lastEndTime"`
	// reason is the reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// message is a human-readable explanation containing details about
	// the transition
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
	// The number of times this event has occurred.
	// +optional
	Count int32 `json:"count,omitempty" protobuf:"varint,7,opt,name=count"`

	Component string `json:"component,omitempty" protobuf:"bytes,8,opt,name=component"`

	Triggers []string `json:"triggers,omitempty" protobuf:"bytes,9,rep,name=triggers"`

	Container string `json:"container,omitempty" protobuf:"bytes,10,opt,name=container"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// ScalerTaskList is a list of ScalerTask objects.
type ScalerTaskList struct {
	metav1.TypeMeta `json:",inline"`
	// metadata is the standard list metadata.
	// +optional
	metav1.ListMeta `json:"metadata" protobuf:"bytes,1,opt,name=metadata"`

	// items is the list of ScalerTask objects.
	Items []ScalerTask `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:storageversion
// +kubebuilder:resource:shortName=st
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

// ScalerTask defines how to trigger the configuration of component scale up and down.
type ScalerTask struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the behavior of the scaler rule.
	// More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#spec-and-status.
	Spec ScalerTaskSpec `json:"spec" protobuf:"bytes,2,name=spec"`

	// Current information about the scaler rule status.
	// +optional
	Status ScalerTaskStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// ScalerTaskSpec is the specification of the behavior of the scalertask.
type ScalerTaskSpec struct {
	Product                string   `json:"product" protobuf:"bytes,1,name=product"`
	SourceNodeList         []string `json:"sourceNodeList,omitempty" protobuf:"bytes,2,name=sourceNodeList"`
	SourceNodeProvider     string   `json:"sourceNodeProvider,omitempty" protobuf:"bytes,3,name=sourceNodeProvider"`
	SourceNodePercentage   int      `json:"sourceNodePercentage,omitempty" protobuf:"bytes,4,name=sourceNodePercentage"`
	TolerateUnavailability bool     `json:"tolerateUnavailability,omitempty" protobuf:"bytes,5,name=tolerateUnavailability"`
	DryRun                 bool     `json:"dryRun" protobuf:"bytes,6,name=dryRun"`
	IgnoreAppIdList        []uint64 `json:"ignoreAppIdList,omitempty" protobuf:"bytes,7,name=ignoreAppIdList"`
}

// ScalerTaskConditionType are the valid conditions of
// a ScalerTask.
type ScalerTaskConditionType string

var (
	TaskGetSourceNode           ScalerTaskConditionType = "GetSourceNode"
	TaskPatchNodeUnschedulable  ScalerTaskConditionType = "PatchNodeUnschedulable"
	TaskGetAffectedCluster      ScalerTaskConditionType = "GetAffectedCluster"
	TaskDisableClusterScaleDown ScalerTaskConditionType = "DisableClusterScaleDown"
	TaskScaleComponentReplicas  ScalerTaskConditionType = "ScaleComponentReplicas"
	TaskEnsureComponentReady    ScalerTaskConditionType = "EnsureComponentReady"
	TaskPatchNodeDrained        ScalerTaskConditionType = "PatchNodeDrained"
	TaskEvictNodePod            ScalerTaskConditionType = "EvictNodePod"
	TaskEnableClusterScaleDown  ScalerTaskConditionType = "EnableClusterScaleDown"
)

// ScalerTaskCondition describes the state of
// a ScalerTask at a certain point.
type ScalerTaskCondition struct {
	// type describes the current condition
	Type ScalerTaskConditionType `json:"type" protobuf:"bytes,1,name=type"`
	// status is the status of the condition (True, False, Unknown)
	Status v1.ConditionStatus `json:"status" protobuf:"bytes,2,name=status"`
	// lastStartTime is the start time of last task execution.
	// +optional
	LastStartTime metav1.Time `json:"lastStartTime,omitempty" protobuf:"bytes,3,opt,name=lastStartTime"`
	// lastEndTime is the end time of last task execution.
	// +optional
	LastEndTime metav1.Time `json:"lastEndTime,omitempty" protobuf:"bytes,4,opt,name=lastEndTime"`
	// reason is the reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// message is a human-readable explanation containing details about
	// the transition
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
	// The number of times this event has occurred.
	// +optional
	Count int32 `json:"count,omitempty" protobuf:"varint,7,opt,name=count"`
}

// ScalerTaskStatus describes the runtime state of the scalertask.
type ScalerTaskStatus struct {
	Nodes            []string `json:"nodes,omitempty" protobuf:"bytes,1,name=nodes"`
	FailedNodes      []string `json:"failedNodes,omitempty" protobuf:"bytes,2,name=failedNodes"`
	AffectedClusters []string `json:"affectedClusters,omitempty" protobuf:"bytes,3,name=affectedClusters"`
	// status is the status of the condition (True, False, Unknown)
	Status v1.ConditionStatus `json:"status" protobuf:"bytes,4,name=status"`
	// lastStartTime is the start time of last task execution.
	// +optional
	LastStartTime metav1.Time `json:"lastStartTime,omitempty" protobuf:"bytes,5,opt,name=lastStartTime"`
	// lastEndTime is the end time of last task execution.
	// +optional
	LastEndTime metav1.Time `json:"lastEndTime,omitempty" protobuf:"bytes,6,opt,name=lastEndTime"`
	// reason is the reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,7,opt,name=reason"`
	// message is a human-readable explanation containing details about
	// the transition
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,8,opt,name=message"`
	// Conditions is the set of conditions required for this autopilotor to scale its target,
	// and indicates whether or not those conditions are met.
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Conditions                []ScalerTaskCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,9,rep,name=conditions"`
	AffectedClusterComponents map[string][]string   `json:"affectedClusterComponents,omitempty" protobuf:"bytes,10,name=affectedClusterComponents"`
}
