/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeHPCs implements HPCInterface
type FakeHPCs struct {
	Fake *FakePlatformV1
}

var hpcsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "hpcs"}

var hpcsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "HPC"}

// Get takes name of the hPC, and returns the corresponding hPC object, and an error if there is any.
func (c *FakeHPCs) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.HPC, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(hpcsResource, name), &platformv1.HPC{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.HPC), err
}

// List takes label and field selectors, and returns the list of HPCs that match those selectors.
func (c *FakeHPCs) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.HPCList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(hpcsResource, hpcsKind, opts), &platformv1.HPCList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.HPCList{ListMeta: obj.(*platformv1.HPCList).ListMeta}
	for _, item := range obj.(*platformv1.HPCList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested hPCs.
func (c *FakeHPCs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(hpcsResource, opts))
}

// Create takes the representation of a hPC and creates it.  Returns the server's representation of the hPC, and an error, if there is any.
func (c *FakeHPCs) Create(ctx context.Context, hPC *platformv1.HPC, opts v1.CreateOptions) (result *platformv1.HPC, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(hpcsResource, hPC), &platformv1.HPC{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.HPC), err
}

// Update takes the representation of a hPC and updates it. Returns the server's representation of the hPC, and an error, if there is any.
func (c *FakeHPCs) Update(ctx context.Context, hPC *platformv1.HPC, opts v1.UpdateOptions) (result *platformv1.HPC, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(hpcsResource, hPC), &platformv1.HPC{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.HPC), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeHPCs) UpdateStatus(ctx context.Context, hPC *platformv1.HPC, opts v1.UpdateOptions) (*platformv1.HPC, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(hpcsResource, "status", hPC), &platformv1.HPC{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.HPC), err
}

// Delete takes name of the hPC and deletes it. Returns an error if one occurs.
func (c *FakeHPCs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(hpcsResource, name), &platformv1.HPC{})
	return err
}

// Patch applies the patch and returns the patched hPC.
func (c *FakeHPCs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.HPC, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(hpcsResource, name, pt, data, subresources...), &platformv1.HPC{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.HPC), err
}
