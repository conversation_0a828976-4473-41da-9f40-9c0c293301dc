package cmd

import (
	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/task"
	"git.woa.com/kmetis/starship-engine/pkg/task/postcheck"
	"git.woa.com/kmetis/starship-engine/pkg/task/precheck"
	"git.woa.com/kmetis/starship-engine/pkg/task/publish"

	"github.com/spf13/cobra"
)

func NewDescribeCommand() *cobra.Command {
	describeCmd := &cobra.Command{
		Use:   "describe",
		Short: "Describe a task",
		Long:  "Describe a task, such as publish, precheck, or postcheck.",
	}

	describeCmd.AddCommand(NewDescribeTaskCommand(consts.PUBLISH, &publish.PublishHandler{}))
	describeCmd.AddCommand(NewDescribeTaskCommand(consts.PRECHECK, &precheck.PreCheckHandler{}))
	describeCmd.AddCommand(NewDescribeTaskCommand(consts.POSTCHECK, &postcheck.PostCheckHandler{}))

	return describeCmd
}

func NewDescribeTaskCommand(taskType string, handler task.TaskHandler) *cobra.Command {
	cmd := &cobra.Command{
		Use:   taskType + " [taskName]",
		Short: "Describe a " + taskType + " task",
		Long:  "Describe the details of a " + taskType + " task.",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			taskName := args[0]
			err := handler.DescribeTask(taskName)
			if err != nil {
				// 处理错误
				return
			}
		},
	}
	return cmd
}
