package igniter

import (
	"fmt"
	"git.woa.com/kmetis/starship-engine/pkg/elasticsearch"
	"git.woa.com/kmetis/starship-engine/pkg/server/handler"
	"strings"

	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/ruleengine"
)

// GetClustersFromESCache returns the clusters from the ES cache.
func GetClustersFromESCache(clusterType string) ([]*elasticsearch.ClusterInfo, error) {
	if clusterType != "tke" && clusterType != "eks" {
		return nil, fmt.Errorf("invalid cluster type: %s", clusterType)
	}

	var clusterPool []*elasticsearch.ClusterInfo
	if clusterType == "tke" {
		clusterPool = handler.EsManager.GetESCache().GetTKEClusters()
	} else {
		clusterPool = handler.EsManager.GetESCache().GetEKSClusters()
	}
	if len(clusterPool) == 0 {
		return nil, fmt.Errorf("get cluster from cache failed, cluster pool is empty")
	}
	return clusterPool, nil
}

func GenerateFilterConfig(options []model.ClusterOption) (ruleengine.FilterConfig, error) {
	ruleConditions := make([]ruleengine.RuleCondition, 0)
	for _, option := range options {
		if option.Key == "" || option.Operator == "" || option.Value == "" {
			return ruleengine.FilterConfig{}, fmt.Errorf("invalid filter option: %+v", option)
		}
		key, err := processOptionKey(option.Key)
		if err != nil {
			return ruleengine.FilterConfig{}, err
		}

		var value interface{}
		if option.Operator == "in" || option.Operator == "not_in" {
			var arr []string
			for _, v := range strings.Split(option.Value, ",") {
				v = strings.TrimSpace(v)
				if v != "" {
					arr = append(arr, v)
				}
			}
			if len(arr) == 0 {
				return ruleengine.FilterConfig{}, fmt.Errorf("invalid filter option: %+v", option)
			}
			value = arr
		} else {
			value = option.Value
		}
		ruleConditions = append(ruleConditions, ruleengine.RuleCondition{
			Key:      key,
			Operator: option.Operator,
			Value:    value,
		})
	}
	if len(ruleConditions) == 0 {
		return ruleengine.FilterConfig{}, fmt.Errorf("no filter condition")
	}
	return ruleengine.FilterConfig{
		Operator: "and",
		Rules: []ruleengine.MatchRule{
			{
				Operator:   "and",
				Conditions: ruleConditions,
			},
		},
	}, nil
}

func processOptionKey(key string) (string, error) {
	key = strings.TrimSpace(key)
	if key == "" {
		return "", fmt.Errorf("key is empty")
	}
	arr := strings.Split(key, ".")
	// ComponentVersion开头的key需要替换key中component名称
	if strings.HasPrefix(key, "ComponentVersion") {
		if len(arr) != 3 {
			return "", fmt.Errorf("invalid key: %s", key)
		}
		if arr[1] == "apiserver" {
			arr[1] = "kube-apiserver"
		}
	}
	// key中包含'-'的需要替换为['']
	newKey := ""
	for _, v := range arr {
		if v == "" {
			return "", fmt.Errorf("invalid key: %s", key)
		}
		if strings.Contains(v, "-") {
			newKey += fmt.Sprintf("['%s']", v)
		} else {
			newKey += fmt.Sprintf(".%s", v)
		}
	}
	return newKey[1:], nil
}
