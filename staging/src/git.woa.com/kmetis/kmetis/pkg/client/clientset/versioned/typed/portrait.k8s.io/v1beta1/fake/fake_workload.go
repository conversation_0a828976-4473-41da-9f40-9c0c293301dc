/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/portrait.k8s.io/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeWorkloads implements WorkloadInterface
type FakeWorkloads struct {
	Fake *FakePortraitV1beta1
	ns   string
}

var workloadsResource = schema.GroupVersionResource{Group: "portrait.k8s.io", Version: "v1beta1", Resource: "workloads"}

var workloadsKind = schema.GroupVersionKind{Group: "portrait.k8s.io", Version: "v1beta1", Kind: "Workload"}

// Get takes name of the workload, and returns the corresponding workload object, and an error if there is any.
func (c *FakeWorkloads) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.Workload, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(workloadsResource, c.ns, name), &v1beta1.Workload{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.Workload), err
}

// List takes label and field selectors, and returns the list of Workloads that match those selectors.
func (c *FakeWorkloads) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.WorkloadList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(workloadsResource, workloadsKind, c.ns, opts), &v1beta1.WorkloadList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1beta1.WorkloadList{ListMeta: obj.(*v1beta1.WorkloadList).ListMeta}
	for _, item := range obj.(*v1beta1.WorkloadList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested workloads.
func (c *FakeWorkloads) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(workloadsResource, c.ns, opts))

}

// Create takes the representation of a workload and creates it.  Returns the server's representation of the workload, and an error, if there is any.
func (c *FakeWorkloads) Create(ctx context.Context, workload *v1beta1.Workload, opts v1.CreateOptions) (result *v1beta1.Workload, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(workloadsResource, c.ns, workload), &v1beta1.Workload{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.Workload), err
}

// Update takes the representation of a workload and updates it. Returns the server's representation of the workload, and an error, if there is any.
func (c *FakeWorkloads) Update(ctx context.Context, workload *v1beta1.Workload, opts v1.UpdateOptions) (result *v1beta1.Workload, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(workloadsResource, c.ns, workload), &v1beta1.Workload{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.Workload), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeWorkloads) UpdateStatus(ctx context.Context, workload *v1beta1.Workload, opts v1.UpdateOptions) (*v1beta1.Workload, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(workloadsResource, "status", c.ns, workload), &v1beta1.Workload{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.Workload), err
}

// Delete takes name of the workload and deletes it. Returns an error if one occurs.
func (c *FakeWorkloads) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(workloadsResource, c.ns, name), &v1beta1.Workload{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeWorkloads) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(workloadsResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1beta1.WorkloadList{})
	return err
}

// Patch applies the patch and returns the patched workload.
func (c *FakeWorkloads) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.Workload, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(workloadsResource, c.ns, name, pt, data, subresources...), &v1beta1.Workload{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.Workload), err
}
