package robot

import (
	"encoding/json"
	"net/http"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/config"
	"git.woa.com/kmetis/starship-engine/pkg/http_util"

	"k8s.io/klog/v2"
)

type Message struct {
	MsgType  string   `json:"msgtype"`
	Markdown MarkDown `json:"markdown"`
	Text     Text     `json:"text"`
}

type MarkDown struct {
	Content string `json:"content"`
}

type Text struct {
	Content string   `json:"content"`
	Mention []string `json:"mentioned_list"`
}

func SendToText(component string, msg string, mentions []string) {
	message := Message{
		MsgType: "text",
		Text: Text{
			Content: msg,
			Mention: mentions,
		},
	}

	// 使用配置文件获取机器人webhook URL
	webhookURL := config.GetRobotWebhookForComponent(component)
	sendToNotify(webhookURL, message)
}

func SendToMarkdown(component string, msg string) {
	message := Message{
		MsgType: "markdown",
		Markdown: MarkDown{
			Content: msg,
		},
	}

	// 使用配置文件获取机器人webhook URL
	webhookURL := config.GetRobotWebhookForComponent(component)
	sendToNotify(webhookURL, message)
}

func sendToNotify(url string, message Message) {

	jsonData, err := json.Marshal(message)
	if err != nil {
		klog.Errorf("Error marshalling JSON: %v", err)
		return
	}

	_, statusCode, err := http_util.PostRequest(url, jsonData, nil, 10*time.Second)
	if err != nil {
		klog.Errorf("Error sending request: %v", err)
		return
	}

	if statusCode == http.StatusOK {
		klog.Errorf("Notification sent successfully")
	} else {
		klog.Info("Failed to send notification:", statusCode)
	}
}
