package leader

import (
	"context"
	"os"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/klog/v2"
)

// LeaderElection 管理Leader选举的结构体
type LeaderElection struct {
	client    kubernetes.Interface
	namespace string
	name      string
	identity  string

	isLeader  bool
	callbacks LeaderCallbacks
	cancel    context.CancelFunc
}

// LeaderCallbacks 定义Leader选举的回调函数
type LeaderCallbacks struct {
	OnStartedLeading func(ctx context.Context)
	OnStoppedLeading func()
	OnNewLeader      func(identity string)
}

// NewLeaderElection 创建新的Leader选举实例
func NewLeaderElection(namespace, name, identity string, callbacks LeaderCallbacks) (*LeaderElection, error) {
	// 创建K8s客户端
	config, err := rest.InClusterConfig()
	if err != nil {
		return nil, err
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, err
	}

	return &LeaderElection{
		client:    clientset,
		namespace: namespace,
		name:      name,
		identity:  identity,
		callbacks: callbacks,
	}, nil
}

// NewLeaderElectionFromEnv 从环境变量创建Leader选举实例
func NewLeaderElectionFromEnv(callbacks LeaderCallbacks) (*LeaderElection, error) {
	podName := os.Getenv("POD_NAME")
	namespace := os.Getenv("POD_NAMESPACE")

	if podName == "" {
		podName = "starship-engine-server"
		klog.Warning("POD_NAME environment variable not set, using default")
	}

	if namespace == "" {
		namespace = "starship"
		klog.Warning("POD_NAMESPACE environment variable not set, using default")
	}

	return NewLeaderElection(namespace, "starship-engine-server-leader", podName, callbacks)
}

// Run 启动Leader选举
func (le *LeaderElection) Run(ctx context.Context) error {
	klog.Infof("Starting leader election for %s in namespace %s", le.identity, le.namespace)

	// 创建资源锁
	lock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      le.name,
			Namespace: le.namespace,
		},
		Client: le.client.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: le.identity,
		},
	}

	// 创建可取消的上下文
	ctx, cancel := context.WithCancel(ctx)
	le.cancel = cancel

	// 配置Leader选举
	config := leaderelection.LeaderElectionConfig{
		Lock:            lock,
		ReleaseOnCancel: true,
		LeaseDuration:   60 * time.Second, // 租约持续时间
		RenewDeadline:   15 * time.Second, // 续约截止时间
		RetryPeriod:     5 * time.Second,  // 重试间隔
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				klog.Infof("Started leading: %s", le.identity)
				le.isLeader = true
				if le.callbacks.OnStartedLeading != nil {
					le.callbacks.OnStartedLeading(ctx)
				}
			},
			OnStoppedLeading: func() {
				klog.Infof("Stopped leading: %s", le.identity)
				le.isLeader = false
				if le.callbacks.OnStoppedLeading != nil {
					le.callbacks.OnStoppedLeading()
				}
			},
			OnNewLeader: func(identity string) {
				if identity == le.identity {
					klog.Infof("Still the leader: %s", identity)
				} else {
					klog.Infof("New leader elected: %s", identity)
				}
				if le.callbacks.OnNewLeader != nil {
					le.callbacks.OnNewLeader(identity)
				}
			},
		},
	}

	// 启动Leader选举
	leaderelection.RunOrDie(ctx, config)
	return nil
}

// Stop 停止Leader选举
func (le *LeaderElection) Stop() {
	if le.cancel != nil {
		klog.Info("Stopping leader election")
		le.cancel()
	}
}

// IsLeader 检查当前实例是否为Leader
func (le *LeaderElection) IsLeader() bool {
	return le.isLeader
}

// GetIdentity 获取当前实例的身份标识
func (le *LeaderElection) GetIdentity() string {
	return le.identity
}

// GetNamespace 获取命名空间
func (le *LeaderElection) GetNamespace() string {
	return le.namespace
}

// GetLockName 获取锁名称
func (le *LeaderElection) GetLockName() string {
	return le.name
}
