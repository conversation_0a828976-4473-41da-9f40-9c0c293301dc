package elasticsearch

import (
	"time"
)

// ClusterInfo 集群信息结构体，保持与ES文档结构一致
type ClusterInfo struct {
	// 基本信息
	ClusterID             string `json:"clusterID"`
	ClusterName           string `json:"clusterName"`
	ClusterType           string `json:"clusterType"` // tke 或 eks
	ClusterStatus         string `json:"clusterStatus"`
	Region                string `json:"region"`
	AppID                 string `json:"appId"`
	K8sVersion            string `json:"k8sVersion"`
	ClusterManagementType string `json:"clusterManagementType"`

	// 账户信息
	AccountName   string `json:"accountName"`
	AccountType   string `json:"accountType"`
	AccountLevel  string `json:"accountLevel"`
	IsBigUser     int    `json:"isBigUser"`
	UIN           string `json:"uin"`
	SubAccountUIN string `json:"subAccountUin"`

	// 集群详细信息
	ClusterCreatedAt string `json:"clusterCreatedAt"`
	ClusterLevel     string `json:"clusterLevel"`
	NodeCount        int    `json:"nodeCount"`
	CPUCount         int    `json:"cpuCount"`
	MemCount         int    `json:"memCount"`
	PodCount         int    `json:"podCount"`
	EksPodCount      int    `json:"eksPodCount"`
	VpcID            string `json:"vpcId"`
	ClusterCIDR      string `json:"clusterCIDR"`
	ServiceCIDR      string `json:"serviceCIDR"`
	MetaClusterID    string `json:"metaClusterId"`
	ProductName      string `json:"productName"`

	// 复杂嵌套字段，使用map存储便于go-expr编排
	ComponentVersion map[string]interface{} `json:"componentVersion"`
	AddonInfo        map[string]interface{} `json:"addonInfo"`
	Tags             map[string]interface{} `json:"tags"`
	CmdbMd           map[string]interface{} `json:"cmdbMd"`

	// 时间字段
	Date      string    `json:"date"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// ComponentInfo 组件信息结构体
type ComponentInfo struct {
	Name   string   `json:"name"`
	Image  string   `json:"image"`
	Tag    string   `json:"tag"`
	Args   []string `json:"args"`
	Env    []EnvVar `json:"env"`
	Type   string   `json:"type"`
	Source string   `json:"source"`
}

// EnvVar 环境变量结构体
type EnvVar struct {
	Name      string     `json:"name"`
	Value     string     `json:"value,omitempty"`
	ValueFrom *ValueFrom `json:"valueFrom,omitempty"`
}

// ValueFrom 环境变量值来源
type ValueFrom struct {
	FieldRef *FieldRef `json:"fieldRef,omitempty"`
}

// FieldRef 字段引用
type FieldRef struct {
	APIVersion string `json:"apiVersion"`
	FieldPath  string `json:"fieldPath"`
}

// AddonInfo 插件信息结构体
type AddonInfo struct {
	AddonName     string `json:"addonName"`
	AddonVersion  string `json:"addonVersion"`
	AddonType     string `json:"addonType"`
	AddonStatus   string `json:"addonStatus"`
	AddonLocation string `json:"addonLocation"`
}

// ESSearchResponse ES搜索响应结构体
type ESSearchResponse struct {
	Took     int  `json:"took"`
	TimedOut bool `json:"timed_out"`
	Shards   struct {
		Total      int `json:"total"`
		Successful int `json:"successful"`
		Skipped    int `json:"skipped"`
		Failed     int `json:"failed"`
	} `json:"_shards"`
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
		MaxScore float64 `json:"max_score"`
		Hits     []struct {
			Index  string      `json:"_index"`
			ID     string      `json:"_id"`
			Score  float64     `json:"_score"`
			Source ClusterInfo `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
	ScrollID string `json:"_scroll_id,omitempty"`
}

// GetComponentVersion 获取组件版本信息
func (c *ClusterInfo) GetComponentVersion() map[string]interface{} {
	return c.ComponentVersion
}

// GetAddonInfo 获取插件信息
func (c *ClusterInfo) GetAddonInfo() map[string]interface{} {
	return c.AddonInfo
}

// GetTags 获取标签信息
func (c *ClusterInfo) GetTags() map[string]interface{} {
	return c.Tags
}

// GetCmdbMd 获取CMDB元数据
func (c *ClusterInfo) GetCmdbMd() map[string]interface{} {
	return c.CmdbMd
}

// IsEKS 判断是否为EKS集群
func (c *ClusterInfo) IsEKS() bool {
	return c.ClusterType == "eks"
}

// IsTKE 判断是否为TKE集群
func (c *ClusterInfo) IsTKE() bool {
	return c.ClusterType == "tke"
}

// QueryRequest 查询请求结构体
type QueryRequest struct {
	// 过滤表达式，使用go-expr语法
	Expression string `json:"expression,omitempty"`

	// 分页参数
	Page     int `json:"page" binding:"min=1"`
	PageSize int `json:"pageSize" binding:"min=1,max=1000"`

	// 排序参数
	SortBy    string `json:"sortBy,omitempty"`    // 排序字段
	SortOrder string `json:"sortOrder,omitempty"` // asc 或 desc

	// 字段选择，为空则返回所有字段
	Fields []string `json:"fields,omitempty"`
}

// QueryResponse 查询响应结构体
type QueryResponse struct {
	Data       []map[string]interface{} `json:"data"`
	Total      int                      `json:"total"`
	Page       int                      `json:"page"`
	PageSize   int                      `json:"pageSize"`
	TotalPages int                      `json:"totalPages"`
}

// ExportRequest 导出请求结构体
type ExportRequest struct {
	// 过滤表达式，使用go-expr语法
	Expression string `json:"expression,omitempty"`

	// 字段选择，必须指定要导出的字段
	Fields []string `json:"fields" binding:"required,min=1"`

	// 导出格式：csv 或 json
	Format string `json:"format" binding:"required,oneof=csv json"`

	// 排序参数
	SortBy    string `json:"sortBy,omitempty"`
	SortOrder string `json:"sortOrder,omitempty"`
}

// ExportResponse 导出响应结构体
type ExportResponse struct {
	Data     []map[string]interface{} `json:"data,omitempty"`    // JSON格式时使用
	Content  string                   `json:"content,omitempty"` // CSV格式时使用
	Format   string                   `json:"format"`
	Total    int                      `json:"total"`
	Fields   []string                 `json:"fields"`
	Filename string                   `json:"filename"`
}
