package handler

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/api/v1/common"
	"git.woa.com/kmetis/starship-engine/api/v1/task"
	"git.woa.com/kmetis/starship-engine/pkg/changeOrder"
	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/internalapi"
)

func ListClusters(c *gin.Context) {
	var req task.ListClustersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	resp, err := db.ListClustersByCondition(&req)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	c.J<PERSON>N(http.StatusOK, common.NewSuccess(resp))
}

func ExportClusters(c *gin.Context) {
	var req task.ExportClustersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	if err := req.Validate(); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 设置默认格式为CSV
	if req.Format == "" {
		req.Format = "csv"
	}

	// 获取集群数据
	clusters, total, err := db.ExportClustersByCondition(&req)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	if len(clusters) == 0 {
		handleError(c, http.StatusNotFound, fmt.Errorf("no clusters found matching the criteria"))
		return
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102-150405")
	filename := fmt.Sprintf("clusters_%s_%s.%s", req.TaskName, timestamp, req.Format)

	// 根据格式生成内容
	var content string
	if req.Format == "csv" {
		content, err = generateCSV(clusters)
		if err != nil {
			handleError(c, http.StatusInternalServerError, err)
			return
		}
	} else if req.Format == "json" {
		jsonBytes, err := json.MarshalIndent(clusters, "", "  ")
		if err != nil {
			handleError(c, http.StatusInternalServerError, err)
			return
		}
		content = string(jsonBytes)
	}

	// 检查是否请求文件下载
	downloadParam := c.Query("download")
	isDownload := downloadParam == "true" || downloadParam == "1"

	if isDownload {
		// 设置响应头，提供文件下载
		if req.Format == "csv" {
			c.Header("Content-Type", "text/csv; charset=utf-8")
		} else {
			c.Header("Content-Type", "application/json; charset=utf-8")
		}
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
		c.String(http.StatusOK, content)
		return
	}

	// 返回JSON响应
	response := task.ExportClustersResponse{
		Content:  content,
		Filename: filename,
		Format:   req.Format,
		Total:    total,
	}
	c.JSON(http.StatusOK, common.NewSuccess(response))
}

func GetClusterDetail(c *gin.Context) {
	// 获取查询参数
	taskName := c.Query("task_name")
	clusterID := c.Query("cluster_id")
	rollback := c.Query("rollback") // 新增rollback参数
	namespace := c.Query("namespace")

	if taskName == "" || clusterID == "" {
		handleError(c, http.StatusBadRequest, fmt.Errorf("taskName and clusterId are required"))
		return
	}

	// 获取集群信息
	clusterInfo, err := db.GetClusterInfoByNameAndID(taskName, clusterID, namespace)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}
	//clusterInfo := getMockData()
	if clusterInfo == nil {
		handleError(c, http.StatusNotFound, fmt.Errorf("cluster not found"))
		return
	}

	// 检查集群状态是否为空
	if clusterInfo.Status == "" {
		handleError(c, http.StatusBadRequest, fmt.Errorf("cluster has not started yet"))
		return
	}

	// 如果请求回滚详情，修改AppAction以包含回滚信息
	if rollback == "true" {
		// 临时修改AppAction以获取回滚详情
		originalAppAction := clusterInfo.AppAction
		clusterInfo.AppAction = "precheck,rollback,postcheck"

		// 获取回滚进度
		progress, err := internalapi.DescribeAppUpgradingProgress(clusterInfo)

		// 恢复原始AppAction
		clusterInfo.AppAction = originalAppAction

		if err != nil {
			handleError(c, http.StatusInternalServerError, err)
			return
		}

		// 返回回滚详情
		c.JSON(http.StatusOK, common.NewSuccess(progress.Response.Task))
		return
	}

	// 获取正常发布进度
	progress, err := internalapi.DescribeAppUpgradingProgress(clusterInfo)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	// 返回结果
	c.JSON(http.StatusOK, common.NewSuccess(progress.Response.Task))
}

func RollbackCluster(c *gin.Context) {
	var req task.RollbackClusterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	if err := req.Validate(); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 批量集群回滚
	handleBatchClusterRollback(c, &req)
}

// handleBatchClusterRollback 处理批量集群回滚
func handleBatchClusterRollback(c *gin.Context, req *task.RollbackClusterRequest) {
	// 验证批量回滚的集群列表
	validClusters, validationErr := validateBatchRollbackClusters(req.TaskName, req.ClusterIds)

	var results []task.RollbackClusterResponse
	successCount := 0
	failedCount := 0

	// 处理验证失败的情况
	if validationErr != nil {
		klog.Warningf("Batch rollback validation failed for some clusters: %v", validationErr)

		// 为验证失败的集群创建失败响应
		validClusterIds := make(map[string]bool)
		for _, cluster := range validClusters {
			validClusterIds[cluster.ClusterId] = true
		}

		for _, clusterId := range req.ClusterIds {
			if !validClusterIds[clusterId] {
				results = append(results, task.RollbackClusterResponse{
					ClusterId: clusterId,
					Status:    "failed",
					Message:   "集群验证失败",
				})
				failedCount++
			}
		}
	}

	// 对验证通过的集群执行回滚操作
	for _, clusterInfo := range validClusters {
		// 验证用户权限
		if !changeOrder.CheckChangeOrderApplicant(clusterInfo.ChangeId, req.User) {
			results = append(results, task.RollbackClusterResponse{
				ClusterId: clusterInfo.ClusterId,
				Status:    "failed",
				Message:   "用户无权限对此集群进行回滚操作",
			})
			failedCount++
			continue
		}

		// 执行回滚操作
		taskId, err := internalapi.RollbackApp(clusterInfo)
		if err != nil {
			klog.Errorf("RollbackApp failed for cluster %s: %v", clusterInfo.ClusterId, err)
			results = append(results, task.RollbackClusterResponse{
				ClusterId: clusterInfo.ClusterId,
				Status:    "failed",
				Message:   fmt.Sprintf("回滚失败: %v", err),
			})
			failedCount++
			continue
		}

		// 存储回滚任务ID到数据库
		if err := db.UpdateClusterRollbackTaskID(req.TaskName, clusterInfo.ClusterId, taskId); err != nil {
			klog.Warningf("Failed to update rollback_task_id for cluster %s: %v", clusterInfo.ClusterId, err)
		}

		// 回滚成功
		results = append(results, task.RollbackClusterResponse{
			TaskId:    taskId,
			ClusterId: clusterInfo.ClusterId,
			Status:    "success",
			Message:   "回滚操作已成功启动",
		})
		successCount++
	}

	// 构建批量回滚响应
	overallStatus := "success"
	message := fmt.Sprintf("批量回滚完成：成功 %d 个，失败 %d 个", successCount, failedCount)

	if failedCount > 0 && successCount == 0 {
		overallStatus = "failed"
		message = fmt.Sprintf("批量回滚全部失败：失败 %d 个", failedCount)
	} else if failedCount > 0 {
		overallStatus = "partial_success"
		message = fmt.Sprintf("批量回滚部分成功：成功 %d 个，失败 %d 个", successCount, failedCount)
	}

	batchResponse := task.BatchRollbackResponse{
		TotalClusters:   len(req.ClusterIds),
		SuccessClusters: successCount,
		FailedClusters:  failedCount,
		Results:         results,
		OverallStatus:   overallStatus,
		Message:         message,
	}

	c.JSON(http.StatusOK, common.NewSuccess(batchResponse))
}

// @Router /cluster/process [get]
func GetTaskProcessBar(c *gin.Context) {
	taskName := c.Query("task_name")
	batchID := c.Query("batch_id")

	// 检查参数taskname
	if taskName == "" {
		handleError(c, http.StatusBadRequest, fmt.Errorf("taskName is required"))
		return
	}

	// 获取task_id, 根据task_name
	taskId, err := db.GetTaskIDByTaskName(taskName)
	if err != nil {
		handleError(c, http.StatusInternalServerError, fmt.Errorf("failed to get task_id: %v", err))
		return
	}

	// 如果没有传batch_id，返回完整的批次执行计划
	if batchID == "" {
		plan, err := db.GetBatchExecutionPlan(taskId)
		if err != nil {
			handleError(c, http.StatusInternalServerError, fmt.Errorf("failed to get batch execution plan: %v", err))
			return
		}
		c.JSON(http.StatusOK, common.NewSuccess(plan))
		return
	}

	// 如果传了batch_id，返回特定批次的完整信息（与不传batch_id行为一致）
	batchPlan, err := db.GetBatchExecutionInfo(taskId, batchID)
	if err != nil {
		handleError(c, http.StatusInternalServerError, fmt.Errorf("failed to get batch execution info: %v", err))
		return
	}

	c.JSON(http.StatusOK, common.NewSuccess(batchPlan))
}

// generateCSV 生成CSV格式的集群数据
func generateCSV(clusters []*task.ClusterInfo) (string, error) {
	var buf strings.Builder
	writer := csv.NewWriter(&buf)

	// 写入CSV头部
	headers := []string{
		"集群ID",
		"Meta ID",
		"集群类型",
		"地域",
		"状态",
		"阶段",
		"原因",
	}
	if err := writer.Write(headers); err != nil {
		return "", fmt.Errorf("failed to write CSV headers: %w", err)
	}

	// 写入数据行
	for _, cluster := range clusters {
		record := []string{
			cluster.ClusterId,
			cluster.MetaId,
			cluster.Type,
			cluster.Region,
			cluster.Status,
			cluster.Stage,
			cluster.Reason,
		}
		if err := writer.Write(record); err != nil {
			return "", fmt.Errorf("failed to write CSV record: %w", err)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return "", fmt.Errorf("CSV writer error: %w", err)
	}

	return buf.String(), nil
}
