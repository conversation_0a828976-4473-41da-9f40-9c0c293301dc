/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	rest "k8s.io/client-go/rest"
	scheme "tkestack.io/tke/api/client/clientset/versioned/scheme"
	platformv1 "tkestack.io/tke/api/platform/v1"
)

// ClusterAddonsGetter has a method to return a ClusterAddonInterface.
// A group's client should implement this interface.
type ClusterAddonsGetter interface {
	ClusterAddons() ClusterAddonInterface
}

// ClusterAddonInterface has methods to work with ClusterAddon resources.
type ClusterAddonInterface interface {
	Get(ctx context.Context, name string, opts v1.GetOptions) (*platformv1.ClusterAddon, error)
	List(ctx context.Context, opts v1.ListOptions) (*platformv1.ClusterAddonList, error)
	ClusterAddonExpansion
}

// clusterAddons implements ClusterAddonInterface
type clusterAddons struct {
	client rest.Interface
}

// newClusterAddons returns a ClusterAddons
func newClusterAddons(c *PlatformV1Client) *clusterAddons {
	return &clusterAddons{
		client: c.RESTClient(),
	}
}

// Get takes name of the clusterAddon, and returns the corresponding clusterAddon object, and an error if there is any.
func (c *clusterAddons) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.ClusterAddon, err error) {
	result = &platformv1.ClusterAddon{}
	err = c.client.Get().
		Resource("clusteraddons").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of ClusterAddons that match those selectors.
func (c *clusterAddons) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.ClusterAddonList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &platformv1.ClusterAddonList{}
	err = c.client.Get().
		Resource("clusteraddons").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}
