//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by deepcopy-gen. DO NOT EDIT.

package v1beta1

import (
	v1 "k8s.io/api/core/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AutopilotClusterCondition) DeepCopyInto(out *AutopilotClusterCondition) {
	*out = *in
	in.LastStartTime.DeepCopyInto(&out.LastStartTime)
	in.LastEndTime.DeepCopyInto(&out.LastEndTime)
	if in.Triggers != nil {
		in, out := &in.Triggers, &out.Triggers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AutopilotClusterCondition.
func (in *AutopilotClusterCondition) DeepCopy() *AutopilotClusterCondition {
	if in == nil {
		return nil
	}
	out := new(AutopilotClusterCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterScaler) DeepCopyInto(out *ClusterScaler) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterScaler.
func (in *ClusterScaler) DeepCopy() *ClusterScaler {
	if in == nil {
		return nil
	}
	out := new(ClusterScaler)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterScaler) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterScalerList) DeepCopyInto(out *ClusterScalerList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ClusterScaler, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterScalerList.
func (in *ClusterScalerList) DeepCopy() *ClusterScalerList {
	if in == nil {
		return nil
	}
	out := new(ClusterScalerList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterScalerList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterScalerSpec) DeepCopyInto(out *ClusterScalerSpec) {
	*out = *in
	if in.ComponentScalerPolicy != nil {
		in, out := &in.ComponentScalerPolicy, &out.ComponentScalerPolicy
		*out = make([]*ComponentScalerPolicy, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ComponentScalerPolicy)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Config != nil {
		in, out := &in.Config, &out.Config
		*out = new(MaintenanceConfig)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterScalerSpec.
func (in *ClusterScalerSpec) DeepCopy() *ClusterScalerSpec {
	if in == nil {
		return nil
	}
	out := new(ClusterScalerSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterScalerStatus) DeepCopyInto(out *ClusterScalerStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]AutopilotClusterCondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	in.LastScaleTime.DeepCopyInto(&out.LastScaleTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterScalerStatus.
func (in *ClusterScalerStatus) DeepCopy() *ClusterScalerStatus {
	if in == nil {
		return nil
	}
	out := new(ClusterScalerStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ComponentScalerPolicy) DeepCopyInto(out *ComponentScalerPolicy) {
	*out = *in
	out.TargetRef = in.TargetRef
	if in.VPA != nil {
		in, out := &in.VPA, &out.VPA
		*out = new(VPAPolicy)
		(*in).DeepCopyInto(*out)
	}
	if in.HPA != nil {
		in, out := &in.HPA, &out.HPA
		*out = new(HPAPolicy)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ComponentScalerPolicy.
func (in *ComponentScalerPolicy) DeepCopy() *ComponentScalerPolicy {
	if in == nil {
		return nil
	}
	out := new(ComponentScalerPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ContainerResourcePolicy) DeepCopyInto(out *ContainerResourcePolicy) {
	*out = *in
	if in.ScaleUpMode != nil {
		in, out := &in.ScaleUpMode, &out.ScaleUpMode
		*out = new(ContainerScalingMode)
		**out = **in
	}
	if in.ScaleDownMode != nil {
		in, out := &in.ScaleDownMode, &out.ScaleDownMode
		*out = new(ContainerScalingMode)
		**out = **in
	}
	if in.MinAllowed != nil {
		in, out := &in.MinAllowed, &out.MinAllowed
		*out = make(v1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	if in.MaxAllowed != nil {
		in, out := &in.MaxAllowed, &out.MaxAllowed
		*out = make(v1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	if in.ControlledResources != nil {
		in, out := &in.ControlledResources, &out.ControlledResources
		*out = new([]v1.ResourceName)
		if **in != nil {
			in, out := *in, *out
			*out = make([]v1.ResourceName, len(*in))
			copy(*out, *in)
		}
	}
	if in.ControlledValues != nil {
		in, out := &in.ControlledValues, &out.ControlledValues
		*out = new(ContainerControlledValues)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ContainerResourcePolicy.
func (in *ContainerResourcePolicy) DeepCopy() *ContainerResourcePolicy {
	if in == nil {
		return nil
	}
	out := new(ContainerResourcePolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HPAPolicy) DeepCopyInto(out *HPAPolicy) {
	*out = *in
	if in.MinReplicas != nil {
		in, out := &in.MinReplicas, &out.MinReplicas
		*out = new(int32)
		**out = **in
	}
	if in.ScaleDownThreshold != nil {
		in, out := &in.ScaleDownThreshold, &out.ScaleDownThreshold
		*out = new(ResourceUtilizationPercentage)
		(*in).DeepCopyInto(*out)
	}
	if in.ScaleUpThreshold != nil {
		in, out := &in.ScaleUpThreshold, &out.ScaleUpThreshold
		*out = new(ResourceUtilizationPercentage)
		(*in).DeepCopyInto(*out)
	}
	if in.ScaleUpMode != nil {
		in, out := &in.ScaleUpMode, &out.ScaleUpMode
		*out = new(ReplicasScalingMode)
		**out = **in
	}
	if in.ScaleDownMode != nil {
		in, out := &in.ScaleDownMode, &out.ScaleDownMode
		*out = new(ReplicasScalingMode)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HPAPolicy.
func (in *HPAPolicy) DeepCopy() *HPAPolicy {
	if in == nil {
		return nil
	}
	out := new(HPAPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MaintenanceConfig) DeepCopyInto(out *MaintenanceConfig) {
	*out = *in
	if in.MaintenanceWindowStartHour != nil {
		in, out := &in.MaintenanceWindowStartHour, &out.MaintenanceWindowStartHour
		*out = new(int32)
		**out = **in
	}
	if in.MaintenanceWindowEndHour != nil {
		in, out := &in.MaintenanceWindowEndHour, &out.MaintenanceWindowEndHour
		*out = new(int32)
		**out = **in
	}
	if in.MaintenanceWindowStartTs != nil {
		in, out := &in.MaintenanceWindowStartTs, &out.MaintenanceWindowStartTs
		*out = new(int64)
		**out = **in
	}
	if in.MaintenanceWindowEndTs != nil {
		in, out := &in.MaintenanceWindowEndTs, &out.MaintenanceWindowEndTs
		*out = new(int64)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MaintenanceConfig.
func (in *MaintenanceConfig) DeepCopy() *MaintenanceConfig {
	if in == nil {
		return nil
	}
	out := new(MaintenanceConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ResourceUtilizationPercentage) DeepCopyInto(out *ResourceUtilizationPercentage) {
	*out = *in
	if in.CPU != nil {
		in, out := &in.CPU, &out.CPU
		*out = new(int32)
		**out = **in
	}
	if in.Memory != nil {
		in, out := &in.Memory, &out.Memory
		*out = new(int32)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ResourceUtilizationPercentage.
func (in *ResourceUtilizationPercentage) DeepCopy() *ResourceUtilizationPercentage {
	if in == nil {
		return nil
	}
	out := new(ResourceUtilizationPercentage)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScalerTask) DeepCopyInto(out *ScalerTask) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScalerTask.
func (in *ScalerTask) DeepCopy() *ScalerTask {
	if in == nil {
		return nil
	}
	out := new(ScalerTask)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ScalerTask) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScalerTaskCondition) DeepCopyInto(out *ScalerTaskCondition) {
	*out = *in
	in.LastStartTime.DeepCopyInto(&out.LastStartTime)
	in.LastEndTime.DeepCopyInto(&out.LastEndTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScalerTaskCondition.
func (in *ScalerTaskCondition) DeepCopy() *ScalerTaskCondition {
	if in == nil {
		return nil
	}
	out := new(ScalerTaskCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScalerTaskList) DeepCopyInto(out *ScalerTaskList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ScalerTask, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScalerTaskList.
func (in *ScalerTaskList) DeepCopy() *ScalerTaskList {
	if in == nil {
		return nil
	}
	out := new(ScalerTaskList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ScalerTaskList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScalerTaskSpec) DeepCopyInto(out *ScalerTaskSpec) {
	*out = *in
	if in.SourceNodeList != nil {
		in, out := &in.SourceNodeList, &out.SourceNodeList
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.IgnoreAppIdList != nil {
		in, out := &in.IgnoreAppIdList, &out.IgnoreAppIdList
		*out = make([]uint64, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScalerTaskSpec.
func (in *ScalerTaskSpec) DeepCopy() *ScalerTaskSpec {
	if in == nil {
		return nil
	}
	out := new(ScalerTaskSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScalerTaskStatus) DeepCopyInto(out *ScalerTaskStatus) {
	*out = *in
	if in.Nodes != nil {
		in, out := &in.Nodes, &out.Nodes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.FailedNodes != nil {
		in, out := &in.FailedNodes, &out.FailedNodes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AffectedClusters != nil {
		in, out := &in.AffectedClusters, &out.AffectedClusters
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	in.LastStartTime.DeepCopyInto(&out.LastStartTime)
	in.LastEndTime.DeepCopyInto(&out.LastEndTime)
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]ScalerTaskCondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.AffectedClusterComponents != nil {
		in, out := &in.AffectedClusterComponents, &out.AffectedClusterComponents
		*out = make(map[string][]string, len(*in))
		for key, val := range *in {
			var outVal []string
			if val == nil {
				(*out)[key] = nil
			} else {
				in, out := &val, &outVal
				*out = make([]string, len(*in))
				copy(*out, *in)
			}
			(*out)[key] = outVal
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScalerTaskStatus.
func (in *ScalerTaskStatus) DeepCopy() *ScalerTaskStatus {
	if in == nil {
		return nil
	}
	out := new(ScalerTaskStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VPAPolicy) DeepCopyInto(out *VPAPolicy) {
	*out = *in
	if in.ContainerPolicies != nil {
		in, out := &in.ContainerPolicies, &out.ContainerPolicies
		*out = make([]ContainerResourcePolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ScaleDownThreshold != nil {
		in, out := &in.ScaleDownThreshold, &out.ScaleDownThreshold
		*out = new(ResourceUtilizationPercentage)
		(*in).DeepCopyInto(*out)
	}
	if in.ScaleUpThreshold != nil {
		in, out := &in.ScaleUpThreshold, &out.ScaleUpThreshold
		*out = new(ResourceUtilizationPercentage)
		(*in).DeepCopyInto(*out)
	}
	if in.ScaleDownStabWindowSeconds != nil {
		in, out := &in.ScaleDownStabWindowSeconds, &out.ScaleDownStabWindowSeconds
		*out = new(int32)
		**out = **in
	}
	if in.ScaleUpStabWindowSeconds != nil {
		in, out := &in.ScaleUpStabWindowSeconds, &out.ScaleUpStabWindowSeconds
		*out = new(int32)
		**out = **in
	}
	if in.Scalers != nil {
		in, out := &in.Scalers, &out.Scalers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VPAPolicy.
func (in *VPAPolicy) DeepCopy() *VPAPolicy {
	if in == nil {
		return nil
	}
	out := new(VPAPolicy)
	in.DeepCopyInto(out)
	return out
}
