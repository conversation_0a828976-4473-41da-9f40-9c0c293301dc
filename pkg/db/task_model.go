package db

import "time"

type Task struct {
	// ID 是 Task 的唯一标识符，自增主键
	ID        int64  `gorm:"column:id;primaryKey;autoIncrement;comment:任务的唯一标识符;NOT NULL" json:"id"`
	AppAction string `gorm:"column:app_action;type:varchar(100);comment:应用动作;NOT NULL" json:"app_action"`
	TaskName  string `gorm:"column:task_name;type:varchar(100);uniqueIndex;comment:任务名称;NOT NULL" json:"task_name"`

	Description string `gorm:"column:description;type:varchar(1024);comment:任务描述;default:NULL" json:"description"`

	// 组件信息
	Component     string `gorm:"column:component;type:varchar(100);comment:组件名称;NOT NULL" json:"component"`
	ImageTag      string `gorm:"column:image_tag;type:varchar(100);comment:镜像标签;default:NULL" json:"image_tag"`
	AppVersion    string `gorm:"column:app_version;type:varchar(100);comment:应用版本;default:NULL" json:"app_version"`
	PluginVersion string `gorm:"column:plugin_version;type:varchar(100);comment:插件版本;default:NULL" json:"plugin_version"`
	ExtendInfo    string `gorm:"column:extend_info;type:varchar(1024);comment:参数;default:NULL" json:"extend_info"`

	// 用户信息
	User     string `gorm:"column:user;type:varchar(100);comment:用户名;NOT NULL" json:"user"`
	ChangeId string `gorm:"column:change_id;type:varchar(100);comment:发布的唯一标识符;NOT NULL" json:"change_id"`
	Token    string `gorm:"column:token;type:varchar(100);comment:操作令牌;NOT NULL" json:"token"`

	// 待发布集群信息
	ClusterType   string `gorm:"column:cluster_type;type:varchar(32);comment:集群类型;" json:"cluster_type"`
	ClusterOption string `gorm:"column:cluster_option;type:varchar(1024);comment:集群选项;NOT NULL" json:"cluster_option"`

	// 分批策略
	BatchType int `gorm:"column:batch_type;type:int(11);comment:分批类型;" json:"batch_type"`
	// 基础分批策略
	BatchStrategies string `gorm:"column:batch_strategies;type:varchar(256);comment:分批策略;" json:"batch_strategies"`
	// 业务分批策略
	AppBatchStrategy int `gorm:"column:app_batch_strategy;type:int(11);comment:业务分批策略;" json:"app_batch_strategy"`
	BatchSize        int `gorm:"column:batch_size;type:int(11);comment:分批大小;" json:"batch_size"`

	//自动确认
	AutoConfirm bool `gorm:"column:auto_confirm;type:tinyint(1);default:false;comment:是否自动确认;NOT NULL" json:"auto_confirm"`

	// 机器人推送
	Notify bool `gorm:"column:notify;type:tinyint(1);default:true;comment:是否进行机器人推送;NOT NULL" json:"notify"`

	// Status 存储任务的状态，只允许 Created, Prepare, Running, BatchCompleted, Completed, Pause, Failed, Terminated 八种状态，默认为 Running
	Status string `gorm:"column:status;type:varchar(50);default:Running;comment:任务状态;NOT NULL" json:"status"`

	CreateTime time.Time `gorm:"column:create_time;type:datetime;default:CURRENT_TIMESTAMP;comment:创建时间;NOT NULL" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;type:datetime;default:CURRENT_TIMESTAMP;comment:更新时间;NOT NULL" json:"update_time"`
}

func (task *Task) TableName() string {
	return "task"
}
