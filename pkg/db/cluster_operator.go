package db

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/kmetis/starship-engine/api/v1/task"
	"git.woa.com/kmetis/starship-engine/pkg/cache"
	"git.woa.com/kmetis/starship-engine/pkg/util"

	"gorm.io/gorm/clause"
	"k8s.io/klog/v2"
)

const (
	DB_CONNECTION_TIMEOUT_SECONDS = 10 * time.Second // DB查询超时设置
)

// 批次失败率结构体
type BatchFailureRate struct {
	TaskId     int64   `gorm:"column:task_id"`
	BatchId    int64   `gorm:"column:batch_id"`
	SubBatchId int64   `gorm:"column:sub_batch_id"`
	FailRate   float64 `gorm:"column:fail_rate"`
}

func (rate BatchFailureRate) String() string {
	return fmt.Sprintf("TaskId: %d, BatchId: %d, FailRate: %.2f", rate.TaskId, rate.BatchId, rate.FailRate)
}

func CreateClusters(clusters []*Cluster) error {
	err := createOrUpdateCluster(clusters)
	if err != nil {
		return err
	}
	return nil
}

func createOrUpdateCluster(clusters []*Cluster) error {
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	tx := DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	result := tx.Save(clusters)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func UpdateClusterById(cluster *TaskClusterInfo) error {
	const maxRetries = 2
	var retryCount int

	for retryCount <= maxRetries {
		tx := DB.Begin()
		if tx.Error != nil {
			return tx.Error
		}

		// 更新 Cluster 表中相应记录的数据
		result := tx.Model(&Cluster{}).
			Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("id =?", cluster.ID).
			Updates(map[string]interface{}{
				"app_id":             cluster.AppID,
				"region":             cluster.Region,
				"type":               cluster.Type,
				"meta_id":            cluster.MetaID,
				"uin":                cluster.UIN,
				"sub_uin":            cluster.SubUIN,
				"starship_task_id":   cluster.StarshipTaskId,
				"starship_change_id": cluster.StarshipChangeId,
				"stage":              cluster.Stage,
				"status":             cluster.Status,
				"reason":             cluster.Reason,
				"update_time":        time.Now(),
			})
		if result.Error != nil {
			tx.Rollback()
			klog.Error(result.Error)
			return result.Error
		}

		// 检查是否有行被更新
		if result.RowsAffected == 0 {
			tx.Rollback()
			retryCount++

			if retryCount > maxRetries {
				return fmt.Errorf("failed to update cluster ID=%d after %d retries", cluster.ID, maxRetries)
			}

			klog.Warningf("Update cluster ID=%d failed, no rows modified. Retrying (%d/%d)", cluster.ID, retryCount, maxRetries)
			time.Sleep(100 * time.Millisecond)
			continue
		}

		// 提交事务
		if err := tx.Commit().Error; err != nil {
			return err
		}

		return nil
	}

	return fmt.Errorf("failed to update cluster ID=%d after %d retries", cluster.ID, maxRetries)
}

func GetClusterByFields(fields map[string]interface{}) ([]*Cluster, error) {
	var clusters []*Cluster
	// 使用 gorm 的 Where 方法根据多个字段获取任务信息
	result := DB.Where(fields).Find(&clusters)
	if result.Error != nil {
		klog.Errorf("Failed to fetch tasks by fields: %v: %v", fields, result.Error)
		return nil, result.Error
	}
	return clusters, nil
}

func GetBatchFailureRate() ([]*BatchFailureRate, error) {
	var result []*BatchFailureRate

	err := DB.
		Model(&Cluster{}).
		Select("cluster.task_id, cluster.batch_id, cluster.sub_batch_id, SUM(CASE WHEN cluster.status = 'failed' THEN 1.0 ELSE 0.0 END) /" +
			" (SUM(CASE WHEN cluster.status = 'success' THEN 1.0 ELSE 0.0 END) + " +
			"SUM(CASE WHEN cluster.status = 'failed' THEN 1.0 ELSE 0.0 END)) AS fail_rate").
		Joins("JOIN task t ON cluster.task_id = t.id").
		Joins("JOIN batch b ON cluster.task_id = b.task_id AND cluster.batch_id = b.batch_id AND cluster.sub_batch_id = b.sub_batch_id").
		Where("t.app_action = 'publish' AND t.status = 'Running' AND b.status = 'Running' AND t.batch_type > 0").
		Group("cluster.task_id, cluster.batch_id, cluster.sub_batch_id").
		Having(`
		   (SUM(CASE WHEN cluster.status = 'success' THEN 1.0 ELSE 0.0 END) + SUM(CASE WHEN cluster.status = 'failed' THEN 1.0 ELSE 0.0 END)) > 10
		   AND
		   (SUM(CASE WHEN cluster.status = 'failed' THEN 1.0 ELSE 0.0 END) > 0)
		   AND
		   (SUM(CASE WHEN cluster.status = 'success' THEN 1.0 ELSE 0.0 END) + SUM(CASE WHEN cluster.status = 'failed' THEN 1.0 ELSE 0.0 END) + SUM(CASE WHEN cluster.status = 'running' THEN 1.0 ELSE 0.0 END)) < COUNT(*)
		`).
		Find(&result).
		Error
	if err != nil {
		klog.Error(err)
		return nil, err
	}

	return result, nil
}

func ListClustersByCondition(req *task.ListClustersRequest) (*task.ListClustersResponse, error) {
	// 首先获取taskId
	taskInfo, err := GetTaskByTaskName(req.TaskName)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	// 计算分页偏移量
	offset := (req.PageNum - 1) * req.PageSize

	// 构建查询
	query := DB.WithContext(ctx).Model(&Cluster{}).Where("task_id = ?", taskInfo.ID)

	if req.BatchId > 0 {
		query = query.Where("batch_id = ?", req.BatchId)
	}
	if req.SubBatchId > 0 {
		query = query.Where("sub_batch_id = ?", req.SubBatchId)
	}
	if req.ClusterId != "" {
		query = query.Where("cluster_id LIKE ?", "%"+req.ClusterId+"%")
	}
	if req.Reason != "" {
		query = query.Where("reason LIKE ?", "%"+req.Reason+"%")
	}
	if req.Region != "" {
		query = query.Where("region = ?", req.Region)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		klog.Errorf("Failed to count clusters: taskName=%s, error=%v", req.TaskName, err)
		return nil, err
	}

	var clusters []*task.ClusterInfo
	if err := query.Select("cluster_id, meta_id, type, region, status, stage, reason, create_time, update_time, namespace, rollback_task_id").
		Offset(offset).
		Limit(req.PageSize).
		Find(&clusters).Error; err != nil {
		klog.Errorf("Failed to list clusters: taskName=%s, error=%v", req.TaskName, err)
		return nil, err
	}

	// 从ES缓存获取accountName并填充到结果中
	enrichClustersWithAccountName(clusters)

	util.AddLink(taskInfo.Component, clusters)

	return &task.ListClustersResponse{
		Items:    clusters,
		Total:    total,
		PageNum:  req.PageNum,
		PageSize: req.PageSize,
	}, nil
}

func ExportClustersByCondition(req *task.ExportClustersRequest) ([]*task.ClusterInfo, int64, error) {
	// 首先获取taskId
	taskInfo, err := GetTaskByTaskName(req.TaskName)
	if err != nil {
		return nil, 0, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	// 构建查询
	query := DB.WithContext(ctx).Model(&Cluster{}).Where("task_id = ?", taskInfo.ID)

	if req.BatchId > 0 {
		query = query.Where("batch_id = ?", req.BatchId)
	}
	if req.SubBatchId > 0 {
		query = query.Where("sub_batch_id = ?", req.SubBatchId)
	}
	if req.ClusterId != "" {
		query = query.Where("cluster_id LIKE ?", "%"+req.ClusterId+"%")
	}
	if req.Reason != "" {
		query = query.Where("reason LIKE ?", "%"+req.Reason+"%")
	}
	if req.Region != "" {
		query = query.Where("region = ?", req.Region)
	}

	// 处理状态过滤：success, failed, all
	if req.Status != "" && req.Status != "all" {
		query = query.Where("status = ?", req.Status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		klog.Errorf("Failed to count clusters for export: taskName=%s, error=%v", req.TaskName, err)
		return nil, 0, err
	}

	var clusters []*task.ClusterInfo
	if err := query.Select("cluster_id, meta_id, type, region, status, stage, reason, create_time, update_time, rollback_task_id").
		Find(&clusters).Error; err != nil {
		klog.Errorf("Failed to export clusters: taskName=%s, error=%v", req.TaskName, err)
		return nil, 0, err
	}

	return clusters, total, nil
}

// UpdateClusterRollbackTaskID 更新集群的回滚任务ID
func UpdateClusterRollbackTaskID(taskName, clusterId, rollbackTaskId string) error {
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	// 首先通过task_name获取task信息
	taskInfo, err := GetTaskByTaskName(taskName)
	if err != nil {
		klog.Errorf("Failed to get task by name %s: %v", taskName, err)
		return fmt.Errorf("未找到任务: %s", taskName)
	}

	// 通过task_id和cluster_id找到对应的集群记录并更新rollback_task_id
	result := DB.WithContext(ctx).Model(&Cluster{}).
		Where("task_id = ? AND cluster_id = ?", taskInfo.ID, clusterId).
		Update("rollback_task_id", rollbackTaskId)

	if result.Error != nil {
		klog.Errorf("Failed to update rollback_task_id for cluster %s in task %s: %v", clusterId, taskName, result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		klog.Warningf("No cluster found to update rollback_task_id: task_name=%s, cluster_id=%s", taskName, clusterId)
		return fmt.Errorf("未找到要更新的集群记录")
	}

	klog.Infof("Updated rollback_task_id for cluster %s in task %s to %s", clusterId, taskName, rollbackTaskId)
	return nil
}

// enrichClustersWithAccountName 从ES缓存获取accountName并填充到集群信息中
func enrichClustersWithAccountName(clusters []*task.ClusterInfo) {
	// 获取全局缓存管理器
	cacheManager := cache.GetGlobalManager()

	// 为每个集群获取accountName
	for _, cluster := range clusters {
		if cluster.ClusterId == "" {
			continue
		}

		// 从缓存中获取集群信息
		clusterInfo, exists := cacheManager.GetClusterFromCache(cluster.ClusterId)
		if !exists {
			klog.V(3).Infof("Cluster %s not found in ES cache, accountName will be empty", cluster.ClusterId)
			cluster.AccountName = "" // 设置为空字符串
			continue
		}

		// 填充accountName
		cluster.AccountName = clusterInfo.AccountName
		klog.V(3).Infof("Enriched cluster %s with accountName: %s", cluster.ClusterId, cluster.AccountName)
	}
}
