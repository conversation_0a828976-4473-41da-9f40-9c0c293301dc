/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	scheme "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// MastersGetter has a method to return a MasterInterface.
// A group's client should implement this interface.
type MastersGetter interface {
	Masters(namespace string) MasterInterface
}

// MasterInterface has methods to work with Master resources.
type MasterInterface interface {
	Create(ctx context.Context, master *v1alpha1.Master, opts v1.CreateOptions) (*v1alpha1.Master, error)
	Update(ctx context.Context, master *v1alpha1.Master, opts v1.UpdateOptions) (*v1alpha1.Master, error)
	UpdateStatus(ctx context.Context, master *v1alpha1.Master, opts v1.UpdateOptions) (*v1alpha1.Master, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.Master, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.MasterList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Master, err error)
	MasterExpansion
}

// masters implements MasterInterface
type masters struct {
	client rest.Interface
	ns     string
}

// newMasters returns a Masters
func newMasters(c *MasterV1alpha1Client, namespace string) *masters {
	return &masters{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the master, and returns the corresponding master object, and an error if there is any.
func (c *masters) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.Master, err error) {
	result = &v1alpha1.Master{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("masters").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Masters that match those selectors.
func (c *masters) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.MasterList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.MasterList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("masters").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested masters.
func (c *masters) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("masters").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a master and creates it.  Returns the server's representation of the master, and an error, if there is any.
func (c *masters) Create(ctx context.Context, master *v1alpha1.Master, opts v1.CreateOptions) (result *v1alpha1.Master, err error) {
	result = &v1alpha1.Master{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("masters").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(master).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a master and updates it. Returns the server's representation of the master, and an error, if there is any.
func (c *masters) Update(ctx context.Context, master *v1alpha1.Master, opts v1.UpdateOptions) (result *v1alpha1.Master, err error) {
	result = &v1alpha1.Master{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("masters").
		Name(master.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(master).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *masters) UpdateStatus(ctx context.Context, master *v1alpha1.Master, opts v1.UpdateOptions) (result *v1alpha1.Master, err error) {
	result = &v1alpha1.Master{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("masters").
		Name(master.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(master).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the master and deletes it. Returns an error if one occurs.
func (c *masters) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("masters").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *masters) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("masters").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched master.
func (c *masters) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Master, err error) {
	result = &v1alpha1.Master{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("masters").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
