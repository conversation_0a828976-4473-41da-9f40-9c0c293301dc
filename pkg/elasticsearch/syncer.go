package elasticsearch

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/config"

	"github.com/robfig/cron/v3"
	"k8s.io/klog/v2"
)

// Syncer ES数据同步器
type Syncer struct {
	client *Client
	cache  *Cache
	config *config.ElasticsearchConfig
	cron   *cron.Cron

	// 同步控制
	stopCh chan struct{}
	syncCh chan struct{}

	// 状态
	running bool
}

// NewSyncer 创建新的同步器
func NewSyncer(esConfig *config.ElasticsearchConfig) (*Syncer, error) {
	cache := NewCache()

	// 创建cron调度器，使用中国时区
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		klog.Warningf("Failed to load Asia/Shanghai timezone, using UTC: %v", err)
		location = time.UTC
	}

	cronScheduler := cron.New(cron.WithLocation(location))

	syncer := &Syncer{
		client: nil, // 延迟初始化客户端
		cache:  cache,
		config: esConfig,
		cron:   cronScheduler,
		stopCh: make(chan struct{}),
		syncCh: make(chan struct{}, 1),
	}

	return syncer, nil
}

// initClient 初始化ES客户端（延迟初始化）
func (s *Syncer) initClient() error {
	if s.client != nil {
		return nil
	}

	klog.Infof("Creating ES client for addresses: %v", s.config.Addresses)

	client, err := NewClient(s.config)
	if err != nil {
		klog.Errorf("Failed to create ES client: %v", err)
		return fmt.Errorf("failed to create elasticsearch client: %w", err)
	}

	s.client = client
	klog.Info("ES client created successfully (connection will be tested on first sync)")
	return nil
}

// Start 启动同步器
func (s *Syncer) Start() error {
	if s.running {
		return fmt.Errorf("syncer is already running")
	}

	s.running = true

	// 添加定时任务：每天6点同步
	_, err := s.cron.AddFunc("0 6 * * *", func() {
		klog.Info("Scheduled sync triggered at 6:00 AM")
		s.triggerSync()
	})
	if err != nil {
		return fmt.Errorf("failed to add cron job: %w", err)
	}

	// 启动cron调度器
	s.cron.Start()

	// 启动同步处理协程
	go s.syncWorker()

	// 执行首次同步
	klog.Info("Triggering initial sync")
	s.triggerSync()

	klog.Info("Elasticsearch syncer started successfully")
	return nil
}

// Stop 停止同步器
func (s *Syncer) Stop() {
	if !s.running {
		return
	}

	klog.Info("Stopping elasticsearch syncer")

	s.running = false

	// 停止cron调度器
	s.cron.Stop()

	// 发送停止信号
	close(s.stopCh)

	// 关闭ES客户端
	if err := s.client.Close(); err != nil {
		klog.Errorf("Failed to close elasticsearch client: %v", err)
	}

	klog.Info("Elasticsearch syncer stopped")
}

// triggerSync 触发同步
func (s *Syncer) triggerSync() {
	select {
	case s.syncCh <- struct{}{}:
		// 成功触发同步
	default:
		// 同步通道已满，说明已有同步在进行
		klog.Info("Sync already in progress, skipping trigger")
	}
}

// syncWorker 同步工作协程
func (s *Syncer) syncWorker() {
	for {
		select {
		case <-s.stopCh:
			klog.Info("Sync worker stopped")
			return
		case <-s.syncCh:
			s.performSync()
		}
	}
}

// performSync 执行同步
func (s *Syncer) performSync() {
	if s.cache.IsSyncing() {
		klog.Info("Sync already in progress, skipping")
		return
	}

	klog.Info("Starting elasticsearch data sync")
	startTime := time.Now()

	// 设置同步状态
	s.cache.SetSyncing(true)

	// 初始化ES客户端（如果还未初始化）
	if err := s.initClient(); err != nil {
		klog.Errorf("Failed to initialize ES client: %v", err)
		s.cache.IncrementErrorCount()
		return
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), s.config.SyncTimeout)
	defer cancel()

	// 使用当前日期同步数据
	now := time.Now()
	clusterData, err := s.client.SyncClustersData(ctx, now)
	if err != nil {
		klog.Errorf("Failed to sync clusters data: %v", err)
		s.cache.IncrementErrorCount()
		return
	}

	// 直接使用分类好的数据更新缓存
	s.cache.UpdateClustersByType(clusterData.TKEClusters, clusterData.EKSClusters)

	// 记录变化统计
	changes := s.cache.CompareWithPrevious()
	if summary, ok := changes["summary"].(map[string]interface{}); ok {
		klog.Infof("Sync changes - TKE: +%v/-%v, EKS: +%v/-%v",
			summary["tke_added"], summary["tke_removed"],
			summary["eks_added"], summary["eks_removed"])
	}

	// 延迟清理备用缓存（给一些时间用于调试和监控）
	go func() {
		time.Sleep(1 * time.Minute)
		s.cache.ClearStandbyCache()
	}()

	duration := time.Since(startTime)
	klog.Infof("Elasticsearch data sync completed successfully in %v", duration)
}

// GetCache 获取缓存实例
func (s *Syncer) GetCache() *Cache {
	return s.cache
}

// GetStats 获取同步统计信息
func (s *Syncer) GetStats() CacheStats {
	return s.cache.GetStats()
}

// IsRunning 检查同步器是否运行中
func (s *Syncer) IsRunning() bool {
	return s.running
}

// ForceSync 强制执行同步
func (s *Syncer) ForceSync() error {
	if !s.running {
		return fmt.Errorf("syncer is not running")
	}

	klog.Info("Force sync triggered")
	s.triggerSync()
	return nil
}

// SyncWithDate 使用指定日期同步数据
func (s *Syncer) SyncWithDate(date time.Time) error {
	if s.cache.IsSyncing() {
		return fmt.Errorf("sync already in progress")
	}

	klog.Infof("Starting sync with specific date: %s", date.Format("2006-01-02"))

	s.cache.SetSyncing(true)
	defer s.cache.SetSyncing(false)

	// 初始化ES客户端（如果还未初始化）
	if err := s.initClient(); err != nil {
		s.cache.IncrementErrorCount()
		return fmt.Errorf("failed to initialize ES client: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), s.config.SyncTimeout)
	defer cancel()

	clusterData, err := s.client.SyncClustersData(ctx, date)
	if err != nil {
		s.cache.IncrementErrorCount()
		return fmt.Errorf("failed to sync clusters data for date %s: %w", date.Format("2006-01-02"), err)
	}

	s.cache.UpdateClustersByType(clusterData.TKEClusters, clusterData.EKSClusters)
	klog.Infof("Sync with date %s completed successfully", date.Format("2006-01-02"))

	return nil
}

// GetCluster 获取指定集群信息
func (s *Syncer) GetCluster(clusterID string) (*ClusterInfo, bool) {
	return s.cache.GetCluster(clusterID)
}

// GetAllClusters 获取所有集群信息
func (s *Syncer) GetAllClusters() map[string]*ClusterInfo {
	return s.cache.GetAllClusters()
}

// SearchClusters 搜索集群
func (s *Syncer) SearchClusters(keyword string) []*ClusterInfo {
	return s.cache.SearchClusters(keyword)
}
