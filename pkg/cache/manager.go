package cache

import (
	"sync"

	"git.woa.com/kmetis/starship-engine/pkg/elasticsearch"
)

// Manager 全局缓存管理器
type Manager struct {
	mu      sync.RWMutex
	esCache *elasticsearch.Cache
}

var (
	globalManager *Manager
	once          sync.Once
)

// GetGlobalManager 获取全局缓存管理器实例
func GetGlobalManager() *Manager {
	once.Do(func() {
		globalManager = &Manager{}
	})
	return globalManager
}

// SetESCache 设置ES缓存实例
func (m *Manager) SetESCache(cache *elasticsearch.Cache) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.esCache = cache
}

// GetESCache 获取ES缓存实例
func (m *Manager) GetESCache() *elasticsearch.Cache {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.esCache
}

// GetClusterFromCache 从ES缓存获取集群信息
func (m *Manager) GetClusterFromCache(clusterID string) (*elasticsearch.ClusterInfo, bool) {
	cache := m.GetESCache()
	if cache == nil {
		return nil, false
	}
	return cache.GetCluster(clusterID)
}
