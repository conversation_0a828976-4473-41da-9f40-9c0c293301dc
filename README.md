## starship星舰发布系统配套工具

[使用方式](https://iwiki.woa.com/p/4013371189#starship工具使用方式)

### 目标
方便用户进行大规模快速发布

### 使用
- 创建发布任务
```shell
./starship-cli create publish -f example.yaml
```

- 获取发布任务列表
```shell
./starship-cli get publish
```

- 获取指定发布任务
```shell
./starship-cli get publish publish-xxxxxx
```

- 获取发布任务详情
```shell
./starship-cli describe publish publish-xxxxxx
```

- 暂停发布任务
```shell
./starship-cli pause publish publish-xxxxxx
```

- 解除暂停任务
```shell
./starship-cli unpause publish publish-xxxxxx
```
上述所有命令publish，可以替换为precheck，postcheck，即创建预检、后检任务。