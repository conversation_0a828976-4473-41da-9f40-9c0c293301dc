package db

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/pkg/batchStrategy"
	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

type TaskNumCount struct {
	TaskId     int64     `gorm:"column:task_id"`
	BatchId    int64     `gorm:"column:batch_id"`
	SubBatchId int64     `gorm:"column:sub_batch_id"`
	TaskName   string    `gorm:"column:task_name;type:varchar(100);comment:任务名称;NOT NULL" json:"task_name"`
	Component  string    `gorm:"column:component;type:varchar(100);comment:组件名称;NOT NULL" json:"component"`
	Total      int64     `gorm:"column:total"`
	Running    int64     `gorm:"column:running"`
	Success    int64     `gorm:"column:success"`
	Failed     int64     `gorm:"column:failed"`
	Status     string    `gorm:"column:status;type:varchar(50);comment:任务状态;NOT NULL" json:"status"`
	CreateTime time.Time `gorm:"column:create_time;type:datetime;comment:创建时间;NOT NULL" json:"create_time"`
}

func (taskNumCount TaskNumCount) String() string {
	return fmt.Sprintf("TaskId: %d, TaskName: %s, Total: %d, Running: %d ,Success: %d, Failed: %d, CreateTime: %s",
		taskNumCount.TaskId, taskNumCount.TaskName, taskNumCount.Total,
		taskNumCount.Running, taskNumCount.Success, taskNumCount.Failed, taskNumCount.CreateTime)
}

var mutex sync.Mutex

// 获取正在运行的任务数量（包括processing和running状态）
func getRunningCount(tx *gorm.DB, taskID int64) (int64, error) {
	var count int64
	err := tx.Model(&Cluster{}).Where("status IN (?, ?) AND task_id =?", "running", "processing", taskID).Count(&count).Error
	return count, err
}

// 检查前一个批次是否完成且满足时间间隔
func checkPreviousBatch(tx *gorm.DB, taskID, batchId, subBatchId int64) (bool, error) {
	if batchId == 1 && subBatchId == 1 {
		return true, nil
	}
	// 定义一个临时结构体来存储查询结果
	type BatchInfo struct {
		SubBatchId int64
		Status     string
		EndTime    *time.Time
		Interval   int
	}
	var batchInfo BatchInfo
	var previousBatchId int64
	var previousSubBatchId int64
	if subBatchId > 1 {
		previousSubBatchId = subBatchId - 1
		previousBatchId = batchId

		err := tx.Model(&Batch{}).Where("task_id =? AND batch_id =? AND sub_batch_id = ?", taskID, previousBatchId, previousSubBatchId).Select("sub_batch_id, status, end_time, `interval`").Scan(&batchInfo).Error
		if err != nil {
			return false, fmt.Errorf("query previous subbatch info failed, taskID: %d, previousBatchId: %d, previousSubBatchId: %d, err: %v", taskID, previousBatchId, previousSubBatchId, err)
		}

	} else {
		previousBatchId = batchId - 1
		var previousBatches []BatchInfo
		if err := tx.Model(&Batch{}).Where("task_id = ? AND batch_id = ?", taskID, previousBatchId).Select("sub_batch_id, status, end_time, `interval`").Scan(&previousBatches).Error; err != nil {
			return false, err
		}
		if len(previousBatches) == 0 {
			return false, fmt.Errorf("query previous batch info failed, taskID: %d, batchID: %d", taskID, previousBatchId)
		}

		for _, info := range previousBatches {
			if info.SubBatchId > batchInfo.SubBatchId {
				batchInfo = info
			}
		}

	}

	if batchInfo.Status != consts.COMPLETED || batchInfo.EndTime == nil {
		return false, nil
	}
	now := time.Now()
	if batchInfo.Interval == 0 {
		return now.After(*batchInfo.EndTime), nil
	}
	return now.After((*batchInfo.EndTime).Add(time.Duration(batchInfo.Interval) * time.Second)), nil
}

// 查询未执行任务
func queryUnplayedTask(tx *gorm.DB, concurrencyTask int) (*TaskClusterInfo, error) {
	type TaskBatch struct {
		TaskId     int64
		AppAction  string
		BatchId    int64
		SubBatchId int64
	}

	// 筛选出所有满足基本条件的 task 和 batch 组合
	var taskBatches []TaskBatch
	// 查询运行中任务的批次时，设置 t.batch_type > 0 来忽略来自starship-cli的任务
	err := tx.Table("cluster c").
		Select("t.id as task_id, t.app_action, b.batch_id, b.sub_batch_id").
		Joins("JOIN task t ON t.id = c.task_id").
		Joins("JOIN batch b ON b.task_id = t.id AND b.batch_id = c.batch_id AND b.sub_batch_id = c.sub_batch_id").
		Where("t.status =? AND c.status =? AND t.batch_type > 0", "Running", "").
		Group("t.id, b.batch_id, b.sub_batch_id").
		Find(&taskBatches).Error
	if err != nil {
		return nil, err
	}

	// 遍历每个 task 和 batch 组合
	for _, tb := range taskBatches {
		runningCount, err := getRunningCount(tx, tb.TaskId)
		if err != nil {
			return nil, err
		}
		if runningCount >= int64(concurrencyTask) {
			continue
		}

		var batch Batch
		err = tx.Model(&Batch{}).Where("task_id = ? AND batch_id = ? AND sub_batch_id = ?", tb.TaskId, tb.BatchId, tb.SubBatchId).First(&batch).Error
		if err != nil {
			return nil, err
		}
		if batch.Status != "Pending" && batch.Status != "Running" {
			continue
		}

		if batch.Status == "Pending" {
			ok, err := checkPreviousBatch(tx, tb.TaskId, tb.BatchId, tb.SubBatchId)
			if err != nil {
				return nil, err
			}
			if !ok {
				continue
			}
		}

		// 找到满足条件的 batch 后，查找第一个满足条件的 cluster
		var taskClusterInfo TaskClusterInfo
		err = tx.Table("cluster c").
			Select("c.id, c.cluster_id, c.region, c.namespace, c.name, t.app_action, t.component, t.image_tag, t.app_version, t.plugin_version, "+
				"t.extend_info, t.user, t.change_id, t.token, c.status, b.batch_id, b.sub_batch_id, t.id as task_id").
			Joins("JOIN task t ON t.id = c.task_id").
			Joins("JOIN batch b ON b.task_id = t.id AND b.batch_id = c.batch_id AND b.sub_batch_id = c.sub_batch_id").
			Where("t.id =? AND b.batch_id =? AND b.sub_batch_id = ? AND c.status =?", tb.TaskId, tb.BatchId, tb.SubBatchId, "").
			First(&taskClusterInfo).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			return nil, err
		}
		klog.Infof("Found unplayed task: TaskId=%d, ClusterId=%s, BatchId=%d, SubBatchId=%d", taskClusterInfo.TaskId,
			taskClusterInfo.ClusterId, taskClusterInfo.BatchId, taskClusterInfo.SubBatchId)
		return &taskClusterInfo, nil
	}

	return nil, nil
}

func GetUnplayedTaskAndSetRunning(concurrencyTask int) (*TaskClusterInfo, error) {
	mutex.Lock()
	defer mutex.Unlock()

	// 使用较低的事务隔离级别来减少死锁风险
	tx := DB.Begin(&sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if tx.Error != nil {
		errMsg := fmt.Sprintf("Failed to start transaction: %v", tx.Error)
		klog.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	taskClusterInfo, err := queryUnplayedTask(tx, concurrencyTask)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	if taskClusterInfo != nil {
		// 检查这个批次是否是新开始的（状态从Pending变为Running）
		var batch Batch
		err := tx.Model(&Batch{}).
			Where("task_id = ? AND batch_id = ? AND sub_batch_id = ?", taskClusterInfo.TaskId, taskClusterInfo.BatchId, taskClusterInfo.SubBatchId).
			First(&batch).Error
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		klog.Infof("Updating cluster status to 'processing' for ClusterID=%s", taskClusterInfo.ClusterId)

		// 使用重试机制处理死锁
		var result *gorm.DB
		maxRetries := 3
		for retry := 0; retry < maxRetries; retry++ {
			result = tx.Model(&Cluster{}).Where("id = ?", taskClusterInfo.ID).Update("status", PROCESSING)
			if result.Error != nil {
				// 检查是否是死锁错误
				if strings.Contains(result.Error.Error(), "Deadlock found") && retry < maxRetries-1 {
					klog.Warningf("Deadlock detected, retrying... (attempt %d/%d)", retry+1, maxRetries)
					time.Sleep(time.Duration(retry+1) * 10 * time.Millisecond) // 递增延迟
					continue
				}
				tx.Rollback()
				errMsg := fmt.Sprintf("Failed to update cluster status: %v", result.Error)
				klog.Error(errMsg)
				return nil, errors.New(errMsg)
			}
			break // 成功则跳出重试循环
		}

		// 如果批次状态是Pending，说明这是新批次的开始
		isNewBatch := batch.Status == consts.PENDING

		// 只有新批次才需要更新batch表
		if isNewBatch {
			klog.Infof("Updating batch status to 'Running' for TaskID=%d, BatchID=%d, SubBatchId=%d", taskClusterInfo.TaskId, taskClusterInfo.BatchId, taskClusterInfo.SubBatchId)
			result = tx.Model(&Batch{}).
				Where("task_id = ? AND batch_id = ? AND sub_batch_id = ?", taskClusterInfo.TaskId, taskClusterInfo.BatchId, taskClusterInfo.SubBatchId).
				Updates(map[string]interface{}{
					"status":      consts.RUNNING,
					"start_time":  time.Now(),
					"update_time": time.Now(),
				})
			if result.Error != nil {
				tx.Rollback()
				errMsg := fmt.Sprintf("Failed to update batch status: %v", result.Error)
				klog.Error(errMsg)
				return nil, errors.New(errMsg)
			}

			// 更新task的notify字段为true
			result = tx.Model(&Task{}).
				Where("id = ?", taskClusterInfo.TaskId).
				Updates(map[string]interface{}{
					"notify":      true,
					"update_time": time.Now(),
				})
			if result.Error != nil {
				tx.Rollback()
				errMsg := fmt.Sprintf("Failed to update task notify status: %v", result.Error)
				klog.Error(errMsg)
				return nil, errors.New(errMsg)
			}
		}

		taskClusterInfo.Status = PROCESSING
	}

	if err := tx.Commit().Error; err != nil {
		errMsg := fmt.Sprintf("Failed to commit transaction: %v", err)
		klog.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	return taskClusterInfo, nil
}

func GetClusterInfoByStatus(status string) ([]*TaskClusterInfo, error) {
	var clusterInfoList []*TaskClusterInfo
	// 开始事务
	tx := DB.Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	// 使用 JOIN 操作查找任务，仅选择所需的列
	result := tx.
		Table("cluster").
		Joins("JOIN task ON cluster.task_id = task.id").
		Select("task.id as task_id, task.app_action, task.component, task.user, task.change_id, task.image_tag, task.app_version, cluster.id, cluster.app_id, cluster.uin, cluster.sub_uin, "+
			"cluster.cluster_id, cluster.starship_task_id, cluster.starship_change_id, cluster.region, cluster.type, cluster.meta_id, cluster.create_time").
		Where("cluster.status =? AND task.batch_type > 0", status).
		Find(&clusterInfoList)
	if result.Error != nil {
		tx.Rollback()
		klog.Error(result.Error)
		return nil, result.Error
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}
	return clusterInfoList, nil
}

func GetClusterInfoByNameAndID(taskName, clusterID, namespace string) (*TaskClusterInfo, error) {
	var clusterInfo TaskClusterInfo
	// 开始事务
	tx := DB.Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	// 使用 JOIN 操作查找任务，仅选择所需的列
	query := tx.
		Table("cluster").
		Joins("JOIN task ON cluster.task_id = task.id").
		Select("task.app_action, task.component, task.change_id, cluster.id, cluster.app_id, cluster.uin, cluster.sub_uin, "+
			"cluster.cluster_id, cluster.starship_task_id, cluster.starship_change_id, cluster.region, cluster.type, cluster.meta_id, cluster.status,"+
			"task.id as task_id").
		Where("task.task_name =? AND cluster.cluster_id =?", taskName, clusterID)

	// 只有当namespace不为空时才添加namespace条件
	if namespace != "" {
		query = query.Where("cluster.namespace =?", namespace)
	}

	result := query.First(&clusterInfo)

	if result.Error != nil {
		tx.Rollback()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		klog.Error(result.Error)
		return nil, result.Error
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}
	return &clusterInfo, nil
}

func GetClusterBatchCount() ([]*TaskNumCount, error) {
	var result []*TaskNumCount

	err := DB.
		Model(&Cluster{}).
		Select("cluster.task_id, cluster.batch_id, cluster.sub_batch_id, Count(*) AS total, "+
			"SUM(CASE WHEN cluster.status = 'running' THEN 1 ELSE 0 END) AS running, "+
			"SUM(CASE WHEN cluster.status = 'success' THEN 1 ELSE 0 END) AS success, "+
			"SUM(CASE WHEN cluster.status = 'failed' THEN 1 ELSE 0 END) AS failed").
		Joins("JOIN batch b ON cluster.task_id = b.task_id AND cluster.batch_id = b.batch_id AND cluster.sub_batch_id = b.sub_batch_id").
		Joins("JOIN task t ON cluster.task_id = t.id").
		Where("b.status = ? AND t.batch_type > 0", consts.RUNNING).
		Group("cluster.task_id, cluster.batch_id, cluster.sub_batch_id"). // 按任务 ID 和批次 ID 分组
		Find(&result).
		Error
	if err != nil {
		klog.Error(err)
		return nil, err
	}

	return result, nil
}

func GetTaskStats(appAction, taskName string, taskIds []int64) ([]*TaskNumCount, error) {
	var result []*TaskNumCount
	query := DB.Model(&Task{}).
		Select("task.id as task_id, " +
			"count(cluster.id) AS total, " +
			"sum(case when cluster.status = 'running' THEN 1 else 0 end) AS running, " +
			"sum(case when cluster.status = 'success' THEN 1 else 0 end) AS success, " +
			"sum(case when cluster.status = 'failed' THEN 1 else 0 end) AS failed, " +
			"MAX(task.component) as component, " +
			"MAX(task.task_name) as task_name, " +
			"MAX(task.status) as status, " +
			"MAX(task.create_time) as create_time").
		Joins("LEFT JOIN cluster ON task.id = cluster.task_id")

	// 根据不同条件添加查询条件
	if appAction != "" {
		query = query.Where("task.app_action =?", appAction)
	}
	if taskName != "" {
		query = query.Where("task.task_name =?", taskName)
	}
	if len(taskIds) > 0 {
		query = query.Where("task.id IN ?", taskIds)
	}

	query = query.Group("task.id")

	err := query.Find(&result).Error
	if err != nil {
		klog.V(1).ErrorS(err, "Get task stats failed")
		return nil, err
	}

	// 如果只查询单个任务，处理结果为单个记录
	if taskName != "" && len(result) > 0 {
		return []*TaskNumCount{result[0]}, nil
	}

	return result, nil
}

func GetClusterByTask(appAction, taskName string) (*Task, []*Cluster, error) {
	var task Task
	// 首先在 Task 表中查找 Task 的 ID
	err := DB.Model(&Task{}).
		Select("id, component").
		Where("app_action =? AND task_name =?", appAction, taskName).
		Find(&task).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, []*Cluster{}, nil
		}
		klog.V(1).ErrorS(err, "Failed to find Task by appAction and taskName")
		return nil, nil, err
	}

	var clusters []*Cluster
	// 然后在 Cluster 表中查找关联的 Cluster 记录
	err = DB.Where("task_id =?", task.ID).Find(&clusters).Error
	if err != nil {
		klog.V(1).ErrorS(err, "Failed to find Clusters by task ID")
		return nil, nil, err
	}

	return &task, clusters, nil
}

func CreateTaskWithTx(task *model.Task, appAction string) error {
	// 开启事务
	tx := DB.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建任务记录
	if task.Name == "" {
		task.Name = appAction + "-" + util.GenerateRandomString(6)
	}
	dbTask := &Task{
		TaskName:      task.Name,
		Description:   task.Description,
		AppAction:     appAction,
		User:          task.UserData.User,
		ChangeId:      task.UserData.ChangeId,
		Token:         task.UserData.Token,
		Component:     task.Component.Name,
		ImageTag:      task.Component.ImageTag,
		AppVersion:    task.Component.AppVersion,
		PluginVersion: task.Component.PluginVersion,
		ExtendInfo:    task.Component.ExtendInfo,
		BatchType:     1,
		AutoConfirm:   task.AutoConfirm,
	}

	if err := tx.Create(dbTask).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create task: %v", err)
	}

	// 根据组件类型处理批次和集群
	var dbBatches []*Batch
	var allDbClusters []*Cluster

	switch task.Component.Name {
	case consts.APPFABRIC:
		if err := handleAppFabricComponent(*task, dbTask.ID, &dbBatches, &allDbClusters); err != nil {
			tx.Rollback()
			return err
		}
	case consts.EKS_POD:
		if err := handleEksPod(*task, appAction, dbTask.ID, &dbBatches, &allDbClusters); err != nil {
			tx.Rollback()
			return err
		}
	case consts.TCR:
		if err := handleTcrComponent(*task, appAction, dbTask.ID, &dbBatches, &allDbClusters); err != nil {
			tx.Rollback()
			return err
		}
	case consts.EKLET_AGENT:
		if err := handleEkletAgentComponent(*task, appAction, dbTask.ID, &dbBatches, &allDbClusters); err != nil {
			tx.Rollback()
			return err
		}
	default:
		if err := handleNormalComponent(*task, appAction, dbTask.ID, &dbBatches, &allDbClusters); err != nil {
			tx.Rollback()
			return err
		}
	}
	// 创建批次记录
	if err := tx.Create(&dbBatches).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create batches: %v", err)
	}

	// 创建集群记录
	if err := tx.Create(&allDbClusters).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create clusters: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

func CreateTaskWithoutBatch(task *model.Task, appAction string) error {
	// 开启事务
	tx := DB.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建任务记录
	if task.Name == "" {
		task.Name = appAction + "-" + util.GenerateRandomString(6)
	}

	var clusterOptions []model.ClusterOption
	if err := json.Unmarshal([]byte(task.ClusterOption), &clusterOptions); err != nil {
		return fmt.Errorf("failed to unmarshal cluster option: %v", err)
	}
	if task.ClusterType != "tke" && task.ClusterType != "eks" {
		return fmt.Errorf("invalid cluster type: %s", task.ClusterType)
	}

	var batchStrategyIDs []string
	if len(task.BatchStrategies) > 0 {
		for _, strategy := range task.BatchStrategies {
			batchStrategyIDs = append(batchStrategyIDs, strconv.Itoa(strategy))
		}
	}
	if (len(batchStrategyIDs) > 0 && task.AppBatchStrategy > 0) || (len(batchStrategyIDs) == 0 && task.AppBatchStrategy == 0) {
		return fmt.Errorf("invalid batch strategy: one of batchStrategies and appBatchStrategy must be set")
	}

	dbTask := &Task{
		TaskName:         task.Name,
		Description:      task.Description,
		Status:           consts.CREATED,
		AppAction:        appAction,
		User:             task.UserData.User,
		ChangeId:         task.UserData.ChangeId,
		Token:            task.UserData.Token,
		Component:        task.Component.Name,
		ImageTag:         task.Component.ImageTag,
		AppVersion:       task.Component.AppVersion,
		PluginVersion:    task.Component.PluginVersion,
		ExtendInfo:       task.Component.ExtendInfo,
		ClusterType:      task.ClusterType,
		ClusterOption:    task.ClusterOption,
		BatchType:        2,
		BatchStrategies:  strings.Join(batchStrategyIDs, ","),
		AppBatchStrategy: task.AppBatchStrategy,
		AutoConfirm:      task.AutoConfirm,
	}

	if err := tx.Create(dbTask).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create task: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

func handleAppFabricComponent(task model.Task, taskId int64, dbBatches *[]*Batch, allDbClusters *[]*Cluster) error {
	applications, err := util.ReadProjectFile(task.ProjectFile)
	if err != nil {
		return fmt.Errorf("failed to read project file %s: %v", task.ProjectFile, err)
	}

	strategy := getAppFabricBatchConfig()
	batches, err := strategy.DivideBatches(task, applications)
	if err != nil {
		return err
	}

	// 对于AppFabric组件，取第一个集群ID
	var clusterId string
	if len(task.Cluster) > 0 {
		// 提取纯集群ID（去掉可能的命名空间部分）
		clusterIDs := extractClusterIDs(task.Cluster)
		if len(clusterIDs) > 0 {
			clusterId = clusterIDs[0]
		}
	}

	*dbBatches, *allDbClusters = prepareBatchAndApplicationData(taskId, clusterId, batches)
	return nil
}

func handleEksPod(task model.Task, appAction string, taskId int64, dbBatches *[]*Batch, allDbClusters *[]*Cluster) error {
	return handlePodBasedComponent(task, appAction, taskId, dbBatches, allDbClusters, util.ReadEksPodFile)
}

// handleEkletAgentComponent 处理 eklet-agent 组件的批次和集群数据
func handleEkletAgentComponent(task model.Task, appAction string, taskID int64, dbBatches *[]*Batch, allDbClusters *[]*Cluster) error {
	// 定义eklet-agent特定的Pod读取函数
	readEkletAgentPods := func(filePath string) ([]util.Pod, error) {
		hasClusterFileContent := strings.TrimSpace(task.ClusterFileContent) != ""
		hasCluster := len(task.Cluster) > 0

		// 检查是否至少提供了一个数据源
		if !hasClusterFileContent && !hasCluster {
			return nil, fmt.Errorf("neither cluster file content nor task.Cluster provided for eklet-agent component")
		}

		// 优先使用文件内容，如果没有则使用task.Cluster
		if hasClusterFileContent {
			return util.ReadEkletAgentFileFromContent(task.ClusterFileContent)
		} else if hasCluster {
			return parseTaskClusterArrayToAgents(task.Cluster)
		}
		return nil, fmt.Errorf("no valid data source for eklet-agent")
	}

	return handlePodBasedComponent(task, appAction, taskID, dbBatches, allDbClusters, readEkletAgentPods)
}

// parseTaskClusterArrayToAgents 解析task.Cluster字段（[]string类型）为[]util.Pod格式
// 对于ekletagent组件，所有集群条目都必须是"集群ID 命名空间"格式
func parseTaskClusterArrayToAgents(clusters []string) ([]util.Pod, error) {
	if len(clusters) == 0 {
		return nil, fmt.Errorf("cluster array is empty")
	}

	var agents []util.Pod

	// ekletagent组件只支持集群ID+命名空间格式: ["cls-12345678 kube-system", "cls-87654321 default"]
	for i, cluster := range clusters {
		cluster = strings.TrimSpace(cluster)
		if cluster == "" {
			continue // 跳过空字符串
		}

		parts := strings.Fields(cluster)
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid cluster format at index %d, expected 'clusterId namespace': %s", i, cluster)
		}

		agents = append(agents, util.Pod{
			ClusterId: parts[0],
			Namespace: parts[1],
		})
	}

	if len(agents) == 0 {
		return nil, fmt.Errorf("no valid cluster data found in task.Cluster")
	}

	return agents, nil
}

// extractClusterIDs 从集群数组中提取纯集群ID列表
// 支持两种格式：
// 1. 纯集群ID：["cls-12345678", "cls-87654321"] -> ["cls-12345678", "cls-87654321"]
// 2. 集群ID+命名空间：["cls-12345678 namespace1", "cls-87654321 namespace2"] -> ["cls-12345678", "cls-87654321"]
func extractClusterIDs(clusters []string) []string {
	var clusterIDs []string
	for _, cluster := range clusters {
		cluster = strings.TrimSpace(cluster)
		if cluster == "" {
			continue
		}

		// 如果包含空格，取第一部分作为集群ID
		parts := strings.Fields(cluster)
		if len(parts) > 0 {
			clusterIDs = append(clusterIDs, parts[0])
		}
	}
	return clusterIDs
}

func handleTcrComponent(task model.Task, appAction string, taskId int64, dbBatches *[]*Batch, allDbClusters *[]*Cluster) error {
	return handlePodBasedComponent(task, appAction, taskId, dbBatches, allDbClusters, util.ReadTcrCertFile)
}

// PodReaderFunc 定义Pod读取函数的类型
type PodReaderFunc func(filePath string) ([]util.Pod, error)

// handlePodBasedComponent 处理基于Pod的组件的通用逻辑
func handlePodBasedComponent(task model.Task, appAction string, taskId int64, dbBatches *[]*Batch, allDbClusters *[]*Cluster, readPods PodReaderFunc) error {
	// 读取Pod数据
	pods, err := readPods(task.ClusterFile)
	if err != nil {
		return fmt.Errorf("failed to read pod data: %v", err)
	}

	// 使用重构后的EksPodStrategy，内部集成了RegionBatchStrategy
	strategy := getEksPodBatchConfig()

	batches, err := strategy.DivideBatches(task, appAction, pods)
	if err != nil {
		return fmt.Errorf("failed to divide batches: %v", err)
	}

	*dbBatches, *allDbClusters = prepareBatchAndPodData(taskId, batches)
	return nil
}

// handleNormalComponent 处理普通组件的批次和集群数据
func handleNormalComponent(task model.Task, appAction string, taskId int64, dbBatches *[]*Batch, allDbClusters *[]*Cluster) (err error) {
	var clusters []string

	// 检查数据源可用性
	hasClusterFileContent := strings.TrimSpace(task.ClusterFileContent) != ""
	hasClusterFile := task.ClusterFile != ""
	hasCluster := len(task.Cluster) > 0

	// 优先使用文件内容，如果没有则使用文件路径，最后使用集群数组
	if hasClusterFileContent {
		clusters, err = util.ReadFileFromContent(task.ClusterFileContent)
		if err != nil {
			return fmt.Errorf("failed to read cluster file content: %v", err)
		}
	} else if hasClusterFile {
		clusters, err = util.ReadFile(task.ClusterFile)
		if err != nil {
			return fmt.Errorf("failed to read cluster file %s: %v", task.ClusterFile, err)
		}
	} else if hasCluster {
		// 处理新的[]string格式的Cluster字段
		// 需要将可能包含命名空间的集群信息转换为纯集群ID列表
		clusters = extractClusterIDs(task.Cluster)
	} else {
		return fmt.Errorf("no valid cluster data source provided: neither cluster file content, cluster file, nor cluster array is available")
	}

	// 对集群列表进行去重
	clusters = removeDuplicateClusters(clusters)

	chain := batchStrategy.NewBatchStrategyChain(batchStrategy.NewRegionBatchStrategy())
	batches, err := chain.Execute(clusters, batchStrategy.BatchStrategyOptions{
		AppAction: appAction,
		AppName:   task.Component.Name,
		BatchSize: task.Batch.BatchSize,
	})
	if err != nil {
		return err
	}
	*dbBatches, *allDbClusters = prepareBatchAndClusterData(taskId, batches)
	return nil
}

func getAppFabricBatchConfig() batchStrategy.AppFabricBatchStrategy {
	var strategy batchStrategy.AppFabricBatchStrategy
	strategy = &batchStrategy.AppFabricStrategy{}
	return strategy
}

func getEksPodBatchConfig() batchStrategy.EksPodBatchStrategy {
	var strategy batchStrategy.EksPodBatchStrategy
	strategy = &batchStrategy.EksPodStrategy{}
	return strategy
}

func prepareBatchAndClusterData(taskId int64, batches []batchStrategy.BatchConfig) ([]*Batch, []*Cluster) {
	var dbBatches []*Batch
	var allDbClusters []*Cluster
	for _, batchConfig := range batches {
		batch := &Batch{
			TaskId:     taskId,
			BatchId:    batchConfig.BatchId,
			SubBatchId: batchConfig.SubBatchId,
			Region:     batchConfig.Region,
			StartTime:  nil,
			EndTime:    nil,
			Interval:   batchConfig.Interval,
		}
		dbBatches = append(dbBatches, batch)
		for _, clusterId := range batchConfig.Clusters {
			cluster := &Cluster{
				TaskId:     taskId,
				ClusterId:  clusterId,
				Region:     batchConfig.Region,
				BatchId:    batchConfig.BatchId,
				SubBatchId: batchConfig.SubBatchId,
			}
			allDbClusters = append(allDbClusters, cluster)
		}
	}
	return dbBatches, allDbClusters
}

func prepareBatchAndApplicationData(taskId int64, clusterId string, batches []batchStrategy.BatchConfig) ([]*Batch, []*Cluster) {
	var dbBatches []*Batch
	var allDbClusters []*Cluster
	for batchNum, batchConfig := range batches {
		batch := &Batch{
			TaskId:    taskId,
			BatchId:   int64(batchNum + 1),
			StartTime: nil,
			EndTime:   nil,
			Region:    batchConfig.Region,
			Interval:  batchConfig.Interval,
		}
		dbBatches = append(dbBatches, batch)
		for _, application := range batchConfig.Applications {
			cluster := &Cluster{
				TaskId:    taskId,
				ClusterId: clusterId,
				BatchId:   batch.BatchId,
				NameSpace: application.Namespace,
				Name:      application.Name,
			}
			allDbClusters = append(allDbClusters, cluster)
		}
	}
	return dbBatches, allDbClusters
}

func prepareBatchAndPodData(taskId int64, batches []batchStrategy.BatchConfig) ([]*Batch, []*Cluster) {
	var dbBatches []*Batch
	var allDbClusters []*Cluster

	for batchNum, batchConfig := range batches {
		// 使用BatchConfig中的BatchId和SubBatchId，如果没有则使用序号
		batchId := batchConfig.BatchId
		if batchId == 0 {
			batchId = int64(batchNum + 1)
		}

		batch := &Batch{
			TaskId:     taskId,
			BatchId:    batchId,
			SubBatchId: batchConfig.SubBatchId,
			StartTime:  nil,
			EndTime:    nil,
			Region:     batchConfig.Region,
			Interval:   batchConfig.Interval,
		}
		dbBatches = append(dbBatches, batch)
		for _, pod := range batchConfig.Pods {
			cluster := &Cluster{
				TaskId:     taskId,
				ClusterId:  pod.ClusterId,
				BatchId:    batchId,
				SubBatchId: batchConfig.SubBatchId,
				Region:     batchConfig.Region,
				NameSpace:  pod.Namespace,
				Name:       pod.PodName,
			}
			allDbClusters = append(allDbClusters, cluster)
		}
	}
	return dbBatches, allDbClusters
}

type ClusterStatusResult struct {
	StatusCounts map[string]int
	IntervalTime int // 间隔时间，单位为秒
}

// BatchExecutionPlan 批次执行计划
type BatchExecutionPlan struct {
	TaskName      string               `json:"task_name"`
	TaskID        int64                `json:"task_id"`
	BatchType     int                  `json:"batch_type"`
	TotalClusters int                  `json:"total_clusters"`
	OverallStatus string               `json:"overall_status"`
	StatusCounts  map[string]int       `json:"status_counts"`
	Batches       []BatchExecutionInfo `json:"batches"`
	CurrentBatch  *BatchExecutionInfo  `json:"current_batch,omitempty"`
	NextBatch     *BatchExecutionInfo  `json:"next_batch,omitempty"`
	AllRegions    []RegionInfo         `json:"all_regions"`
}

// BatchExecutionInfo 单个批次的执行信息
type BatchExecutionInfo struct {
	BatchID       int64                `json:"batch_id"`
	SubBatchID    int64                `json:"sub_batch_id"`
	BatchName     string               `json:"batch_name"`
	Region        string               `json:"region"`
	RegionCN      string               `json:"region_cn"`
	Status        string               `json:"status"`
	ClusterCount  int                  `json:"cluster_count"`
	StatusCounts  map[string]int       `json:"status_counts"`
	StartTime     *time.Time           `json:"start_time"`
	EndTime       *time.Time           `json:"end_time"`
	EstimatedTime *time.Time           `json:"estimated_time"`
	Interval      int64                `json:"interval"`
	SubBatches    []BatchExecutionInfo `json:"sub_batches,omitempty"`
	Regions       []RegionInfo         `json:"regions,omitempty"` // 大批次包含的地域列表
}

// RegionInfo 地域信息
type RegionInfo struct {
	Region   string `json:"region"`
	RegionCN string `json:"region_cn"`
}

// 修改GetClusterStatusCounts方法
func GetClusterStatusCounts(taskId int64, batchID string) (map[string]int, error) {
	StatusCounts := make(map[string]int)

	// 2. 查询状态统计
	where := map[string]interface{}{"task_id": taskId}
	if batchID != "" {
		where["batch_id"] = batchID
	}

	var rows []struct {
		Status string
		Count  int
	}
	err := DB.Model(&Cluster{}).
		Select("status, count(*) as count").
		Where(where).
		Group("status").
		Find(&rows).Error
	if err != nil {
		klog.ErrorS(err, "Failed to query cluster status counts")
		return StatusCounts, err
	}

	for _, row := range rows {
		status := row.Status
		if status == "" {
			status = "Pending"
		}
		StatusCounts[status] = row.Count
	}

	// 3. 填充默认状态
	for _, s := range []string{"running", "success", "failed", "Pending"} {
		if _, ok := StatusCounts[s]; !ok {
			StatusCounts[s] = 0
		}
	}
	return StatusCounts, nil
}

//	func GetBatchStatus(taskId int64, batchID string) (string, *time.Time, int, error) {
//		var result struct {
//			Status   string
//			EndTime  *time.Time
//			Interval int
//		}
//		err := DB.Model(&Batch{}).
//			Select("status,end_time, `interval`").
//			Where("task_id = ? AND batch_id = ?", taskId, batchID).
//			Order("id DESC"). // 假设id是自增主键，表示最新
//			Limit(1).
//			Scan(&result).Error
//		if err != nil {
//			return "", nil, 0, fmt.Errorf("failed to query batch status: %v", err)
//		}
//		return result.Status, result.EndTime, result.Interval, nil
//	}
type BatchStatus struct {
	Status   string
	EndTime  *time.Time
	Interval int
}

func GetBatchStatusList(taskId int64, batchID string) ([]BatchStatus, error) {
	var results []BatchStatus
	query := DB.Model(&Batch{}).
		Select("status, end_time, `interval`").
		Where("task_id = ?", taskId)
	if batchID != "" {
		query = query.Where("batch_id = ?", batchID)
	}
	err := query.Order("id ASC").Find(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query batch status: %v", err)
	}
	return results, nil
}

func GetDistinctBatchIDs(taskId int64) ([]int64, error) {
	var batchIDs []int64
	err := DB.Model(&Batch{}).
		Distinct("batch_id").
		Where("task_id = ?", taskId).
		Pluck("batch_id", &batchIDs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query distinct batch IDs: %v", err)
	}
	return batchIDs, nil
}

// GetBatchExecutionPlan 获取完整的批次执行计划
func GetBatchExecutionPlan(taskId int64) (*BatchExecutionPlan, error) {
	// 获取任务信息
	var task Task
	if err := DB.Where("id = ?", taskId).First(&task).Error; err != nil {
		return nil, fmt.Errorf("failed to get task: %v", err)
	}

	// 获取所有批次信息
	var batches []Batch
	if err := DB.Where("task_id = ?", taskId).
		Order("batch_id ASC, sub_batch_id ASC").
		Find(&batches).Error; err != nil {
		return nil, fmt.Errorf("failed to get batches: %v", err)
	}

	// 获取总集群数
	var totalClusters int64
	if err := DB.Model(&Cluster{}).Where("task_id = ?", taskId).Count(&totalClusters).Error; err != nil {
		return nil, fmt.Errorf("failed to count total clusters: %v", err)
	}

	// 获取所有地域信息
	allRegions := getAllRegionsFromBatches(batches)

	// 按大批次分组并构建批次执行信息
	groupedBatches := groupBatchesByBatchId(batches, task.BatchType, taskId)

	// 智能计算预计执行时间（产品优化：只显示必要的时间信息）
	if err := calculateSmartBatchEstimatedTimes(groupedBatches, task.Status); err != nil {
		klog.Warningf("Failed to calculate estimated times: %v", err)
	}

	// 找到当前执行的批次和下一个批次
	currentBatch, nextBatch := findCurrentAndNextBatch(groupedBatches, task.BatchType)

	// 确定整体状态
	overallStatus := determineOverallStatusFromGrouped(groupedBatches)

	// 获取整体状态统计
	overallStatusCounts, err := GetClusterStatusCounts(taskId, "")
	if err != nil {
		klog.Warningf("Failed to get overall status counts: %v", err)
		overallStatusCounts = make(map[string]int)
	}

	plan := &BatchExecutionPlan{
		TaskName:      task.TaskName,
		TaskID:        taskId,
		BatchType:     task.BatchType,
		TotalClusters: int(totalClusters),
		OverallStatus: overallStatus,
		StatusCounts:  overallStatusCounts,
		Batches:       groupedBatches,
		CurrentBatch:  currentBatch,
		NextBatch:     nextBatch,
		AllRegions:    allRegions,
	}

	return plan, nil
}

// GetBatchExecutionInfo 获取特定批次的执行信息（返回完整信息结构）
func GetBatchExecutionInfo(taskId int64, batchId string) (*BatchExecutionPlan, error) {
	// 获取任务信息
	var task Task
	if err := DB.Where("id = ?", taskId).First(&task).Error; err != nil {
		return nil, fmt.Errorf("failed to get task: %v", err)
	}

	// 获取指定大批次的所有子批次信息
	var targetBatches []Batch
	if err := DB.Where("task_id = ? AND batch_id = ?", taskId, batchId).
		Order("sub_batch_id ASC").
		Find(&targetBatches).Error; err != nil {
		return nil, fmt.Errorf("failed to get batches: %v", err)
	}

	if len(targetBatches) == 0 {
		return nil, fmt.Errorf("batch not found: batch_id=%s", batchId)
	}

	// 获取所有批次信息（用于计算 current_batch 和 next_batch）
	var allBatches []Batch
	if err := DB.Where("task_id = ?", taskId).
		Order("batch_id ASC, sub_batch_id ASC").
		Find(&allBatches).Error; err != nil {
		return nil, fmt.Errorf("failed to get all batches: %v", err)
	}

	// 获取总集群数（该大批次的）
	var totalClusters int64
	if err := DB.Model(&Cluster{}).Where("task_id = ? AND batch_id = ?", taskId, batchId).Count(&totalClusters).Error; err != nil {
		return nil, fmt.Errorf("failed to count total clusters: %v", err)
	}

	// 获取该批次的状态统计
	statusCounts, err := GetClusterStatusCounts(taskId, batchId)
	if err != nil {
		klog.Warningf("Failed to get status counts for batch %s: %v", batchId, err)
		statusCounts = make(map[string]int)
	}

	// 获取所有地域信息（该大批次的）
	allRegions := getAllRegionsFromBatches(targetBatches)

	// 按大批次分组并构建批次执行信息（只包含指定的大批次）
	groupedBatch := createGroupedBatchInfo(targetBatches, task.BatchType, taskId)
	groupedBatches := []BatchExecutionInfo{groupedBatch}

	// 按大批次分组并构建所有批次执行信息（用于计算 current_batch 和 next_batch）
	allGroupedBatches := groupBatchesByBatchId(allBatches, task.BatchType, taskId)

	// 智能计算预计执行时间（基于所有批次）
	if err := calculateSmartBatchEstimatedTimes(allGroupedBatches, task.Status); err != nil {
		klog.Warningf("Failed to calculate estimated times: %v", err)
	}

	// 将计算出的预计时间同步到指定批次的子批次中
	syncEstimatedTimesToTargetBatch(allGroupedBatches, groupedBatches, batchId)

	// 找到当前执行的批次和下一个批次（基于所有批次）
	currentBatch, nextBatch := findCurrentAndNextBatch(allGroupedBatches, task.BatchType)

	// 确定整体状态（基于指定批次）
	overallStatus := determineOverallStatusFromGrouped(groupedBatches)

	plan := &BatchExecutionPlan{
		TaskName:      task.TaskName,
		TaskID:        taskId,
		BatchType:     task.BatchType,
		TotalClusters: int(totalClusters),
		OverallStatus: overallStatus,
		StatusCounts:  statusCounts,
		Batches:       groupedBatches,
		CurrentBatch:  currentBatch,
		NextBatch:     nextBatch,
		AllRegions:    allRegions,
	}

	return plan, nil
}

// getAllRegionsFromBatches 获取所有地域信息
func getAllRegionsFromBatches(batches []Batch) []RegionInfo {
	regionMap := make(map[string]RegionInfo)

	for _, batch := range batches {
		// 跳过空的 region，避免添加无意义的地域信息
		if batch.Region == "" {
			continue
		}

		if _, exists := regionMap[batch.Region]; !exists {
			regionMap[batch.Region] = RegionInfo{
				Region:   batch.Region,
				RegionCN: util.GetRegionCNName(batch.Region),
			}
		}
	}

	regions := make([]RegionInfo, 0, len(regionMap))
	for _, region := range regionMap {
		regions = append(regions, region)
	}

	return regions
}

// groupBatchesByBatchId 按大批次ID分组批次
func groupBatchesByBatchId(batches []Batch, batchType int, taskId int64) []BatchExecutionInfo {
	batchGroups := make(map[int64][]Batch)

	// 按 batch_id 分组
	for _, batch := range batches {
		batchGroups[batch.BatchId] = append(batchGroups[batch.BatchId], batch)
	}

	var result []BatchExecutionInfo

	// 按 batch_id 顺序处理
	for batchId := int64(1); batchId <= int64(len(batchGroups)); batchId++ {
		if subBatches, exists := batchGroups[batchId]; exists {
			groupedBatch := createGroupedBatchInfo(subBatches, batchType, taskId)
			result = append(result, groupedBatch)
		}
	}

	return result
}

// createGroupedBatchInfo 创建分组的批次信息
func createGroupedBatchInfo(subBatches []Batch, batchType int, taskId int64) BatchExecutionInfo {
	if len(subBatches) == 0 {
		return BatchExecutionInfo{}
	}

	// 主批次信息：直接使用 batch_id，不使用子批次的地域信息
	batchId := subBatches[0].BatchId

	// 获取该大批次的集群状态统计（所有子批次合并）
	statusCounts, _ := GetClusterStatusCounts(taskId, fmt.Sprintf("%d", batchId))

	// 计算总集群数量
	totalClusterCount := 0
	for _, count := range statusCounts {
		totalClusterCount += count
	}

	// 主批次名称：根据 batch_type 决定如何生成
	var batchName string
	if batchType == 0 {
		// batch_type = 0 时，使用第一个子批次的 region_cn 作为主批次名称
		if len(subBatches) > 0 && subBatches[0].Region != "" {
			batchName = util.GetRegionCNName(subBatches[0].Region)
		} else {
			batchName = util.GetBatchNameByType(batchId, int64(batchType), "")
		}
	} else if batchType == 1 {
		// batch_type = 1 时，主批次名称基于子批次的地域分组
		// 使用第一个子批次的地域来确定分组
		if len(subBatches) > 0 {
			batchName = util.GetBatchNameByType(batchId, int64(batchType), subBatches[0].Region)
		} else {
			batchName = "其他"
		}
	} else {
		// 其他 batch_type，主批次名称直接基于 batch_id
		batchName = util.GetBatchNameByType(batchId, int64(batchType), "")
	}

	// 确定大批次状态
	groupStatus := determineGroupStatus(subBatches)

	// 获取时间信息
	var startTime, endTime *time.Time
	var interval int64

	for _, batch := range subBatches {
		if batch.StartTime != nil && (startTime == nil || batch.StartTime.Before(*startTime)) {
			startTime = batch.StartTime
		}
		if batch.EndTime != nil && (endTime == nil || batch.EndTime.After(*endTime)) {
			endTime = batch.EndTime
		}
		if batch.Interval > interval {
			interval = batch.Interval
		}
	}

	// 构建地域列表（从子批次中获取）
	regions := make([]RegionInfo, 0, len(subBatches))
	for _, batch := range subBatches {
		// 跳过空的地域
		if batch.Region != "" {
			regions = append(regions, RegionInfo{
				Region:   batch.Region,
				RegionCN: util.GetRegionCNName(batch.Region),
			})
		}
	}

	// 构建子批次信息（batch_type = 0 时不需要子批次）
	var subBatchInfos []BatchExecutionInfo
	if batchType != 0 {
		for _, batch := range subBatches {
			subStatusCounts, _ := GetClusterStatusCountsBySubBatch(taskId, batch.BatchId, batch.SubBatchId)
			subClusterCount := 0
			for _, count := range subStatusCounts {
				subClusterCount += count
			}

			// 子批次名称：可以使用地域信息或者批次ID
			subBatchName := batchName
			if batch.Region != "" {
				subBatchName = util.GetBatchNameByType(batch.BatchId, int64(batchType), batch.Region)
			}

			subBatchInfo := BatchExecutionInfo{
				BatchID:      batch.BatchId,
				SubBatchID:   batch.SubBatchId,
				BatchName:    subBatchName,
				Region:       batch.Region,
				RegionCN:     util.GetRegionCNName(batch.Region),
				Status:       batch.Status,
				ClusterCount: subClusterCount,
				StatusCounts: subStatusCounts,
				StartTime:    batch.StartTime,
				EndTime:      batch.EndTime,
				Interval:     batch.Interval,
			}
			subBatchInfos = append(subBatchInfos, subBatchInfo)
		}
	}

	// 主批次信息：根据 batch_type 设置不同的属性
	var mainRegion, mainRegionCN string
	var subBatchID int64

	if batchType == 0 && len(subBatches) > 0 {
		// batch_type = 0 时，主批次直接使用第一个子批次的地域信息
		mainRegion = subBatches[0].Region
		mainRegionCN = util.GetRegionCNName(subBatches[0].Region)
		subBatchID = 0 // batch_type = 0 时，保持数据库中的 sub_batch_id = 0
	} else {
		// 其他 batch_type，使用默认值
		subBatchID = subBatches[0].SubBatchId
	}

	groupedBatch := BatchExecutionInfo{
		BatchID:      batchId,
		SubBatchID:   subBatchID,
		BatchName:    batchName,
		Region:       mainRegion,
		RegionCN:     mainRegionCN,
		Status:       groupStatus,
		ClusterCount: totalClusterCount,
		StatusCounts: statusCounts,
		StartTime:    startTime,
		EndTime:      endTime,
		Interval:     interval,
		Regions:      regions,
		SubBatches:   subBatchInfos,
	}

	return groupedBatch
}

// GetClusterStatusCountsBySubBatch 获取子批次的集群状态统计
func GetClusterStatusCountsBySubBatch(taskId int64, batchId int64, subBatchId int64) (map[string]int, error) {
	statusCounts := make(map[string]int)

	var rows []struct {
		Status string
		Count  int
	}
	err := DB.Model(&Cluster{}).
		Select("status, count(*) as count").
		Where("task_id = ? AND batch_id = ? AND sub_batch_id = ?", taskId, batchId, subBatchId).
		Group("status").
		Find(&rows).Error
	if err != nil {
		klog.ErrorS(err, "Failed to query cluster status counts by sub batch")
		return statusCounts, err
	}

	for _, row := range rows {
		status := row.Status
		if status == "" {
			status = "Pending"
		}
		statusCounts[status] = row.Count
	}

	return statusCounts, nil
}

// determineGroupStatus 确定大批次的状态
func determineGroupStatus(subBatches []Batch) string {
	if len(subBatches) == 0 {
		return "Unknown"
	}

	hasRunning := false
	hasPending := false
	allCompleted := true

	for _, batch := range subBatches {
		switch batch.Status {
		case "Running":
			hasRunning = true
			allCompleted = false
		case "Pending":
			hasPending = true
			allCompleted = false
		case "Failed":
			return "Failed"
		case "Completed":
			// 继续检查其他批次
		default:
			allCompleted = false
		}
	}

	if allCompleted {
		return "Completed"
	} else if hasRunning {
		return "Running"
	} else if hasPending {
		return "Pending"
	}

	return "Unknown"
}

// determineOverallStatusFromGrouped 从分组批次确定整体状态
func determineOverallStatusFromGrouped(batches []BatchExecutionInfo) string {
	if len(batches) == 0 {
		return "Unknown"
	}

	hasRunning := false
	hasPending := false
	allCompleted := true

	for _, batch := range batches {
		switch batch.Status {
		case "Running":
			hasRunning = true
			allCompleted = false
		case "Pending":
			hasPending = true
			allCompleted = false
		case "Failed":
			return "Failed"
		}
	}

	if allCompleted {
		return "Completed"
	} else if hasRunning {
		return "Running"
	} else if hasPending {
		return "Pending"
	}

	return "Unknown"
}

// removeDuplicateClusters 对集群列表进行去重
func removeDuplicateClusters(clusters []string) []string {
	// 使用map来记录已经出现的集群ID
	seen := make(map[string]bool)
	result := make([]string, 0)

	for _, cluster := range clusters {
		// 去除空白字符
		cluster = strings.TrimSpace(cluster)

		// 跳过空字符串
		if cluster == "" {
			continue
		}

		// 如果没有见过这个集群ID，则添加到结果中
		if !seen[cluster] {
			seen[cluster] = true
			result = append(result, cluster)
		}
	}

	return result
}

// calculateSmartBatchEstimatedTimes 正确的批次预计执行时间计算
func calculateSmartBatchEstimatedTimes(batches []BatchExecutionInfo, taskStatus string) error {
	if len(batches) == 0 {
		return nil
	}

	// 清除所有预计时间，重新计算
	for i := range batches {
		batches[i].EstimatedTime = nil
		for j := range batches[i].SubBatches {
			batches[i].SubBatches[j].EstimatedTime = nil
		}
	}

	// 检查是否有正在执行的批次
	for i := range batches {
		batch := &batches[i]
		if batch.Status == "Running" {
			// 如果有大批次正在执行，不计算任何预计时间
			return nil
		}
		for j := range batch.SubBatches {
			subBatch := &batch.SubBatches[j]
			if subBatch.Status == "Running" {
				// 如果有子批次正在执行，不计算任何预计时间
				return nil
			}
		}
	}

	// 只有当没有批次正在执行时，才计算预计时间
	// 找到最后完成的子批次（全局范围内）
	var lastCompletedTime *time.Time
	var lastInterval int64
	var lastCompletedBatchID int64
	var lastCompletedSubBatchID int64

	for i := range batches {
		batch := &batches[i]
		for j := range batch.SubBatches {
			subBatch := &batch.SubBatches[j]
			if subBatch.Status == "Completed" && subBatch.EndTime != nil {
				if lastCompletedTime == nil || subBatch.EndTime.After(*lastCompletedTime) {
					lastCompletedTime = subBatch.EndTime
					lastInterval = subBatch.Interval
					lastCompletedBatchID = subBatch.BatchID
					lastCompletedSubBatchID = subBatch.SubBatchID
				}
			}
		}
	}

	// 如果没有已完成的子批次，不计算预计时间
	if lastCompletedTime == nil {
		return nil
	}

	// 找到下一个要执行的子批次
	var nextSubBatch *BatchExecutionInfo

	// 1. 先在同一个大批次中找下一个子批次
	for i := range batches {
		batch := &batches[i]
		if batch.BatchID == lastCompletedBatchID {
			for j := range batch.SubBatches {
				subBatch := &batch.SubBatches[j]
				if subBatch.Status == "Pending" && subBatch.SubBatchID > lastCompletedSubBatchID {
					nextSubBatch = subBatch
					break
				}
			}
			break
		}
	}

	// 2. 如果同一大批次中没有找到，找下一个大批次的第一个子批次
	if nextSubBatch == nil {
		for i := range batches {
			batch := &batches[i]
			if batch.BatchID > lastCompletedBatchID && batch.Status == "Pending" {
				for j := range batch.SubBatches {
					subBatch := &batch.SubBatches[j]
					if subBatch.Status == "Pending" {
						nextSubBatch = subBatch
						break
					}
				}
				break
			}
		}
	}

	// 3. 计算下一个子批次的预计时间
	if nextSubBatch != nil {
		estimatedTime := lastCompletedTime.Add(time.Duration(lastInterval) * time.Second)
		nextSubBatch.EstimatedTime = &estimatedTime
	}

	return nil
}

// syncEstimatedTimesToTargetBatch 将全局计算的预计时间同步到指定批次中
func syncEstimatedTimesToTargetBatch(allBatches []BatchExecutionInfo, targetBatches []BatchExecutionInfo, targetBatchId string) {
	if len(targetBatches) == 0 {
		return
	}

	targetBatch := &targetBatches[0] // 指定批次只有一个

	// 在全局批次中找到对应的批次
	for i := range allBatches {
		allBatch := &allBatches[i]
		if fmt.Sprintf("%d", allBatch.BatchID) == targetBatchId {
			// 同步子批次的预计时间
			for j := range targetBatch.SubBatches {
				targetSubBatch := &targetBatch.SubBatches[j]
				// 在全局批次的子批次中找到对应的子批次
				for k := range allBatch.SubBatches {
					allSubBatch := &allBatch.SubBatches[k]
					if targetSubBatch.BatchID == allSubBatch.BatchID &&
						targetSubBatch.SubBatchID == allSubBatch.SubBatchID {
						// 同步预计时间
						targetSubBatch.EstimatedTime = allSubBatch.EstimatedTime
						break
					}
				}
			}
			break
		}
	}
}

// findCurrentAndNextBatch 找到当前执行批次和下一个批次（根据batch_type适配）
func findCurrentAndNextBatch(batches []BatchExecutionInfo, batchType int) (*BatchExecutionInfo, *BatchExecutionInfo) {
	var currentBatch *BatchExecutionInfo
	var nextBatch *BatchExecutionInfo

	// 第一步：找到当前正在运行的批次
	for i := range batches {
		batch := &batches[i]
		if batch.Status == "Running" {
			if batchType == 0 {
				// batch_type = 0 时，直接返回主批次
				currentBatch = batch
				if currentBatch != nil {
					currentBatch.EstimatedTime = nil
				}
				return currentBatch, nil
			} else {
				// 其他 batch_type，检查是否有子批次正在运行
				for j := range batch.SubBatches {
					subBatch := &batch.SubBatches[j]
					if subBatch.Status == "Running" {
						// 子批次正在运行
						currentBatch = subBatch
						if currentBatch != nil {
							currentBatch.EstimatedTime = nil
						}
						return currentBatch, nil
					}
				}
				// 大批次正在运行
				currentBatch = batch
				if currentBatch != nil {
					currentBatch.EstimatedTime = nil
				}
				return currentBatch, nil
			}
		}
	}

	// 第二步：如果没有正在运行的批次，找到下一个要执行的批次
	for i := range batches {
		batch := &batches[i]
		if batch.Status == "Pending" {
			if batchType == 0 {
				// batch_type = 0 时，直接检查主批次
				if batch.EstimatedTime != nil {
					nextBatch = batch
					return nil, nextBatch
				}
			} else {
				// 其他 batch_type，检查是否有子批次
				if len(batch.SubBatches) > 0 {
					// 找到第一个待执行的子批次
					for j := range batch.SubBatches {
						subBatch := &batch.SubBatches[j]
						if subBatch.Status == "Pending" && subBatch.EstimatedTime != nil {
							nextBatch = subBatch
							return nil, nextBatch
						}
					}
				} else {
					// 没有子批次，检查大批次是否有预计时间
					if batch.EstimatedTime != nil {
						nextBatch = batch
						return nil, nextBatch
					}
				}
			}
		}
	}

	return nil, nil
}
