package db

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	"k8s.io/klog/v2"
)

// PublishStatsOperator 发布统计操作结构体 - 重新设计，只保留2个核心功能
type PublishStatsOperator struct {
	db *gorm.DB
}

// NewPublishStatsOperator 创建发布统计操作实例
func NewPublishStatsOperator() *PublishStatsOperator {
	if DB == nil {
		klog.Errorf("数据库连接未初始化")
		return nil
	}
	return &PublishStatsOperator{db: DB}
}

// TaskCreationStatsResponse 任务创建统计响应
type TaskCreationStatsResponse struct {
	DateRange  []string                `json:"date_range"`
	DailyStats []TaskCreationDailyStat `json:"daily_stats"`
}

// TaskCreationDailyStat 每日任务创建统计
type TaskCreationDailyStat struct {
	Date        string         `json:"date"`
	TotalTasks  int            `json:"total_tasks"`
	ByComponent map[string]int `json:"by_component"`
}

// ClusterDeploymentStatsResponse 集群发布统计响应
type ClusterDeploymentStatsResponse struct {
	DateRange  []string                     `json:"date_range"`
	DailyStats []ClusterDeploymentDailyStat `json:"daily_stats"`
}

// ClusterDeploymentDailyStat 每日集群发布统计
type ClusterDeploymentDailyStat struct {
	Date          string                          `json:"date"`
	TotalClusters int                             `json:"total_clusters"`
	ByComponent   map[string]ComponentClusterStat `json:"by_component"`
}

// ComponentClusterStat 组件集群统计
type ComponentClusterStat struct {
	Total   int `json:"total"`
	Success int `json:"success"`
	Failed  int `json:"failed"`
}

// GetTaskCreationStats 获取任务创建统计数据
// startDate, endDate 格式: "2006-01-02"
// component 为空表示获取所有组件，否则获取指定组件
func (p *PublishStatsOperator) GetTaskCreationStats(startDate, endDate, component string) (*TaskCreationStatsResponse, error) {
	if p.db == nil {
		return nil, fmt.Errorf("数据库连接未初始化")
	}

	var results []struct {
		Date      string `gorm:"column:date"`
		Component string `gorm:"column:component"`
		Count     int    `gorm:"column:count"`
	}

	// 构建查询 - 基于Task表的create_time字段按日期分组
	query := p.db.Table("task").
		Select("DATE(create_time) as date, component, COUNT(*) as count").
		Where("app_action = 'publish'").
		Where("DATE(create_time) BETWEEN ? AND ?", startDate, endDate)

	// 如果指定了组件，添加组件过滤
	if component != "" {
		query = query.Where("component = ?", component)
	}

	query = query.Group("DATE(create_time), component").Order("date, component")

	if err := query.Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("查询任务创建统计数据失败: %w", err)
	}

	// 按日期分组整理数据
	dateMap := make(map[string]map[string]int)
	for _, result := range results {
		if dateMap[result.Date] == nil {
			dateMap[result.Date] = make(map[string]int)
		}
		dateMap[result.Date][result.Component] = result.Count
	}

	// 转换为返回格式
	var dailyStats []TaskCreationDailyStat
	for date, componentData := range dateMap {
		totalTasks := 0
		for _, count := range componentData {
			totalTasks += count
		}

		dailyStats = append(dailyStats, TaskCreationDailyStat{
			Date:        date,
			TotalTasks:  totalTasks,
			ByComponent: componentData,
		})
	}

	response := &TaskCreationStatsResponse{
		DateRange:  []string{startDate, endDate},
		DailyStats: dailyStats,
	}

	return response, nil
}

// GetClusterDeploymentStats 获取集群发布统计数据
// startDate, endDate 格式: "2006-01-02"
// component 为空表示获取所有组件，否则获取指定组件
func (p *PublishStatsOperator) GetClusterDeploymentStats(startDate, endDate, component string) (*ClusterDeploymentStatsResponse, error) {
	if p.db == nil {
		return nil, fmt.Errorf("数据库连接未初始化")
	}

	var results []struct {
		Date         string `gorm:"column:date"`
		Component    string `gorm:"column:component"`
		TotalCount   int    `gorm:"column:total_count"`
		SuccessCount int    `gorm:"column:success_count"`
		FailedCount  int    `gorm:"column:failed_count"`
	}

	// 构建查询 - 基于Cluster表关联Task表，按update_time分组统计成功和失败
	query := p.db.Table("cluster c").
		Select(`
			DATE(c.update_time) as date,
			t.component,
			COUNT(c.id) as total_count,
			COUNT(CASE WHEN c.status = 'success' THEN 1 END) as success_count,
			COUNT(CASE WHEN c.status = 'failed' THEN 1 END) as failed_count
		`).
		Joins("LEFT JOIN task t ON c.task_id = t.id").
		Where("t.app_action = 'publish'").
		Where("DATE(c.update_time) BETWEEN ? AND ?", startDate, endDate).
		Where("c.status IN ('success', 'failed')") // 只统计已完成的集群

	// 如果指定了组件，添加组件过滤
	if component != "" {
		query = query.Where("t.component = ?", component)
	}

	query = query.Group("DATE(c.update_time), t.component").Order("date, t.component")

	if err := query.Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("查询集群发布统计数据失败: %w", err)
	}

	// 按日期分组整理数据
	dateMap := make(map[string]map[string]ComponentClusterStat)
	for _, result := range results {
		if dateMap[result.Date] == nil {
			dateMap[result.Date] = make(map[string]ComponentClusterStat)
		}
		dateMap[result.Date][result.Component] = ComponentClusterStat{
			Total:   result.TotalCount,
			Success: result.SuccessCount,
			Failed:  result.FailedCount,
		}
	}

	// 转换为返回格式
	var dailyStats []ClusterDeploymentDailyStat
	for date, componentData := range dateMap {
		totalClusters := 0
		for _, stat := range componentData {
			totalClusters += stat.Total
		}

		dailyStats = append(dailyStats, ClusterDeploymentDailyStat{
			Date:          date,
			TotalClusters: totalClusters,
			ByComponent:   componentData,
		})
	}

	response := &ClusterDeploymentStatsResponse{
		DateRange:  []string{startDate, endDate},
		DailyStats: dailyStats,
	}

	return response, nil
}

// GetAllComponents 获取所有组件列表（用于前端组件筛选）
func (p *PublishStatsOperator) GetAllComponents() ([]string, error) {
	if p.db == nil {
		return nil, fmt.Errorf("数据库连接未初始化")
	}

	var components []string
	if err := p.db.Table("task").
		Select("DISTINCT component").
		Where("app_action = 'publish'").
		Pluck("component", &components).Error; err != nil {
		return nil, fmt.Errorf("查询组件列表失败: %w", err)
	}

	return components, nil
}

// GetDateRange 获取数据的时间范围（用于前端时间选择器）
func (p *PublishStatsOperator) GetDateRange() (string, string, error) {
	if p.db == nil {
		return "", "", fmt.Errorf("数据库连接未初始化")
	}

	var result struct {
		MinDate string `gorm:"column:min_date"`
		MaxDate string `gorm:"column:max_date"`
	}

	if err := p.db.Table("task").
		Select("DATE(MIN(create_time)) as min_date, DATE(MAX(create_time)) as max_date").
		Where("app_action = 'publish'").
		Scan(&result).Error; err != nil {
		return "", "", fmt.Errorf("查询时间范围失败: %w", err)
	}

	return result.MinDate, result.MaxDate, nil
}

// ValidateDateRange 验证日期范围是否合理
func (p *PublishStatsOperator) ValidateDateRange(startDate, endDate string) error {
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return fmt.Errorf("开始日期格式错误: %w", err)
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return fmt.Errorf("结束日期格式错误: %w", err)
	}

	if start.After(end) {
		return fmt.Errorf("开始日期不能晚于结束日期")
	}

	// 限制查询范围不超过90天
	if end.Sub(start) > 90*24*time.Hour {
		return fmt.Errorf("查询时间范围不能超过90天")
	}

	return nil
}

// GetQuickDateRange 获取快速时间范围（7天、14天、30天）
func GetQuickDateRange(days int) (string, string) {
	now := time.Now()
	endDate := now.Format("2006-01-02")
	startDate := now.AddDate(0, 0, -days).Format("2006-01-02")
	return startDate, endDate
}
