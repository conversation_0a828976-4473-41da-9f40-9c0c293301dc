/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	clientset "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned"
	autopilotv1beta1 "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/typed/autopilot.k8s.io/v1beta1"
	fakeautopilotv1beta1 "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/typed/autopilot.k8s.io/v1beta1/fake"
	portraitv1beta1 "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/typed/portrait.k8s.io/v1beta1"
	fakeportraitv1beta1 "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/typed/portrait.k8s.io/v1beta1/fake"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/discovery"
	fakediscovery "k8s.io/client-go/discovery/fake"
	"k8s.io/client-go/testing"
)

// NewSimpleClientset returns a clientset that will respond with the provided objects.
// It's backed by a very simple object tracker that processes creates, updates and deletions as-is,
// without applying any validations and/or defaults. It shouldn't be considered a replacement
// for a real clientset and is mostly useful in simple unit tests.
func NewSimpleClientset(objects ...runtime.Object) *Clientset {
	o := testing.NewObjectTracker(scheme, codecs.UniversalDecoder())
	for _, obj := range objects {
		if err := o.Add(obj); err != nil {
			panic(err)
		}
	}

	cs := &Clientset{tracker: o}
	cs.discovery = &fakediscovery.FakeDiscovery{Fake: &cs.Fake}
	cs.AddReactor("*", "*", testing.ObjectReaction(o))
	cs.AddWatchReactor("*", func(action testing.Action) (handled bool, ret watch.Interface, err error) {
		gvr := action.GetResource()
		ns := action.GetNamespace()
		watch, err := o.Watch(gvr, ns)
		if err != nil {
			return false, nil, err
		}
		return true, watch, nil
	})

	return cs
}

// Clientset implements clientset.Interface. Meant to be embedded into a
// struct to get a default implementation. This makes faking out just the method
// you want to test easier.
type Clientset struct {
	testing.Fake
	discovery *fakediscovery.FakeDiscovery
	tracker   testing.ObjectTracker
}

func (c *Clientset) Discovery() discovery.DiscoveryInterface {
	return c.discovery
}

func (c *Clientset) Tracker() testing.ObjectTracker {
	return c.tracker
}

var _ clientset.Interface = &Clientset{}

// AutopilotV1beta1 retrieves the AutopilotV1beta1Client
func (c *Clientset) AutopilotV1beta1() autopilotv1beta1.AutopilotV1beta1Interface {
	return &fakeautopilotv1beta1.FakeAutopilotV1beta1{Fake: &c.Fake}
}

// PortraitV1beta1 retrieves the PortraitV1beta1Client
func (c *Clientset) PortraitV1beta1() portraitv1beta1.PortraitV1beta1Interface {
	return &fakeportraitv1beta1.FakePortraitV1beta1{Fake: &c.Fake}
}
