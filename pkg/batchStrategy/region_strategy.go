package batchStrategy

import (
	"fmt"

	"git.woa.com/kmetis/starship-engine/pkg/util"
)

const (
	RegionBatchStrategyName = "RegionBatchStrategy"
)

type RegionBatchStrategy struct {
	Name string
	ID   int64
}

func NewRegionBatchStrategy() *RegionBatchStrategy {
	return &RegionBatchStrategy{
		Name: RegionBatchStrategyName,
		ID:   RegionBatchStrategyID,
	}
}

func (r *RegionBatchStrategy) DivideBatches(batches []BatchConfig, isMainBatch bool, options BatchStrategyOptions) ([]BatchConfig, error) {
	if len(batches) == 0 {
		return batches, nil
	}
	if isMainBatch && len(batches) > 1 {
		return nil, fmt.Errorf("main batch should have at most one batch")
	}
	if options.AppAction == "" {
		return nil, fmt.Errorf("appAction is required")
	}

	newBatches := make([]BatchConfig, 0)
	// 遍历所有集群，根据地域信息将集群分组
	for _, b := range batches {
		if len(b.Clusters) == 0 {
			continue
		}
		// 用于存储每个优先级下各地域对应的集群列表
		priorityRegionGroups := make(map[int]map[string][]string)
		for _, cluster := range b.Clusters {
			// 优先使用缓存版本获取region
			region, err := util.GetRegionAliasFromClusterIDWithCache(cluster)
			if err != nil {
				// 如果缓存版本失败，降级到原版本
				region, err = util.GetRegionAliasFromClusterID(cluster)
				if err != nil {
					fmt.Printf("Failed to get region for cluster %s: %v", cluster, err)
					continue
				}
			}

			// 获取地域的优先级，如果未定义则默认为最低优先级6
			priority := RegionBatchMap[region]
			if priority == 0 {
				priority = 6
			}

			// 初始化该优先级的地域map
			if priorityRegionGroups[priority] == nil {
				priorityRegionGroups[priority] = make(map[string][]string)
			}

			priorityRegionGroups[priority][region] = append(priorityRegionGroups[priority][region], cluster)
		}

		index := 0
		// 按优先级从高到低处理（1->5）
		for priority := 1; priority <= 6; priority++ {
			if regionGroups, exists := priorityRegionGroups[priority]; exists {
				index++
				batch := b.BatchId
				if isMainBatch {
					batch = int64(index)
				}

				for region, clusterList := range regionGroups {
					newBatches = append(newBatches, BatchConfig{
						Region:         region,
						RegionPriority: index,
						Clusters:       clusterList,
						BatchId:        batch,
					})
				}
			}
		}
	}

	return newBatches, nil
}

func (r *RegionBatchStrategy) GetName() string {
	return RegionBatchStrategyName
}

func (r *RegionBatchStrategy) GetID() int64 {
	return RegionBatchStrategyID
}
