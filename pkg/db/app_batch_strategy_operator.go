package db

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
)

func GetAppBatchStrategyById(id int64) (*AppBatchStrategy, error) {
	var appBatchStrategy AppBatchStrategy
	if err := DB.Model(&appBatchStrategy).Where("id = ?", id).First(&appBatchStrategy).Error; err != nil {
		return nil, err
	}
	return &appBatchStrategy, nil
}

func GetAppBatchStrategy() ([]*AppBatchStrategy, error) {
	var appBatchStrategies []*AppBatchStrategy
	if err := DB.Model(&AppBatchStrategy{}).Find(&appBatchStrategies).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*AppBatchStrategy{}, nil
		}
		return nil, err
	}
	return appBatchStrategies, nil
}

func GetAppBatchStrategyByNameLike(name string) ([]*AppBatchStrategy, error) {
	var appBatchStrategies []*AppBatchStrategy
	if err := DB.Model(&AppBatchStrategy{}).Where(fmt.Sprintf("name LIKE '%%%s%%'", name)).Find(&appBatchStrategies).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*AppBatchStrategy{}, nil
		}
		return nil, err
	}
	return appBatchStrategies, nil
}

func CreateAppBatchStrategy(appBatchStrategy *AppBatchStrategy) error {
	return DB.Create(appBatchStrategy).Error
}

func UpdateAppBatchStrategy(appBatchStrategy *AppBatchStrategy) error {
	return DB.Model(appBatchStrategy).Where("id = ?", appBatchStrategy.ID).Updates(map[string]interface{}{
		"name":     appBatchStrategy.Name,
		"strategy": appBatchStrategy.Strategy,
	}).Error
}
