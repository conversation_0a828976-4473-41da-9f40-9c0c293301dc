package cmd

import (
	"flag"

	"git.woa.com/kmetis/starship-engine/pkg/config"
	"git.woa.com/kmetis/starship-engine/pkg/db"

	"github.com/spf13/cobra"
	"k8s.io/klog/v2"
)

func NewRootCommand() *cobra.Command {
	rootCmd := &cobra.Command{
		Use:   "starship-cli",
		Short: "useful starship tool cli",
		Long:  `Useful starship cli tool for Tencent Cloud Kubernetes Engine`,
		PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
			//configPath := cmd.Flag("config").Value.String()
			//if configPath == "" {
			//	configPath = config.GetDefaultConfigPath()
			//}
			//
			//if err := config.InitConfig(configPath); err != nil {
			//	return fmt.Errorf("failed to init config: %w", err)
			//}

			db.InitDB(config.GetDefaultDbConfig())
			return nil
		},
	}

	klogFlags := flag.NewFlagSet("klog", flag.ExitOnError)
	klog.InitFlags(klogFlags)
	flags := rootCmd.PersistentFlags()
	flags.AddGoFlagSet(klogFlags)

	//flags.String("config", "", "Path to config file (default is $HOME/.starship/config.yaml)")

	rootCmd.AddCommand(NewCreateCommand())
	rootCmd.AddCommand(NewGetCommand())
	rootCmd.AddCommand(NewDescribeCommand())
	rootCmd.AddCommand(NewCancelCommand())
	rootCmd.AddCommand(NewPauseCommand())
	rootCmd.AddCommand(NewUnpauseCommand())

	return rootCmd
}
