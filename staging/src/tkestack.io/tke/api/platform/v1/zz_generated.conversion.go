//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by conversion-gen. DO NOT EDIT.

package v1

import (
	url "net/url"
	unsafe "unsafe"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
	platform "tkestack.io/tke/api/platform"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*AddonSpec)(nil), (*platform.AddonSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AddonSpec_To_platform_AddonSpec(a.(*AddonSpec), b.(*platform.AddonSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.AddonSpec)(nil), (*AddonSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_AddonSpec_To_v1_AddonSpec(a.(*platform.AddonSpec), b.(*AddonSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*App)(nil), (*platform.App)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_App_To_platform_App(a.(*App), b.(*platform.App), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.App)(nil), (*App)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_App_To_v1_App(a.(*platform.App), b.(*App), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*AuthzWebhookAddr)(nil), (*platform.AuthzWebhookAddr)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_AuthzWebhookAddr_To_platform_AuthzWebhookAddr(a.(*AuthzWebhookAddr), b.(*platform.AuthzWebhookAddr), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.AuthzWebhookAddr)(nil), (*AuthzWebhookAddr)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_AuthzWebhookAddr_To_v1_AuthzWebhookAddr(a.(*platform.AuthzWebhookAddr), b.(*AuthzWebhookAddr), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*BuiltinAuthzWebhookAddr)(nil), (*platform.BuiltinAuthzWebhookAddr)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_BuiltinAuthzWebhookAddr_To_platform_BuiltinAuthzWebhookAddr(a.(*BuiltinAuthzWebhookAddr), b.(*platform.BuiltinAuthzWebhookAddr), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.BuiltinAuthzWebhookAddr)(nil), (*BuiltinAuthzWebhookAddr)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_BuiltinAuthzWebhookAddr_To_v1_BuiltinAuthzWebhookAddr(a.(*platform.BuiltinAuthzWebhookAddr), b.(*BuiltinAuthzWebhookAddr), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CSIOperator)(nil), (*platform.CSIOperator)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIOperator_To_platform_CSIOperator(a.(*CSIOperator), b.(*platform.CSIOperator), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CSIOperator)(nil), (*CSIOperator)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CSIOperator_To_v1_CSIOperator(a.(*platform.CSIOperator), b.(*CSIOperator), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CSIOperatorFeature)(nil), (*platform.CSIOperatorFeature)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIOperatorFeature_To_platform_CSIOperatorFeature(a.(*CSIOperatorFeature), b.(*platform.CSIOperatorFeature), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CSIOperatorFeature)(nil), (*CSIOperatorFeature)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CSIOperatorFeature_To_v1_CSIOperatorFeature(a.(*platform.CSIOperatorFeature), b.(*CSIOperatorFeature), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CSIOperatorList)(nil), (*platform.CSIOperatorList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIOperatorList_To_platform_CSIOperatorList(a.(*CSIOperatorList), b.(*platform.CSIOperatorList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CSIOperatorList)(nil), (*CSIOperatorList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CSIOperatorList_To_v1_CSIOperatorList(a.(*platform.CSIOperatorList), b.(*CSIOperatorList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CSIOperatorSpec)(nil), (*platform.CSIOperatorSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIOperatorSpec_To_platform_CSIOperatorSpec(a.(*CSIOperatorSpec), b.(*platform.CSIOperatorSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CSIOperatorSpec)(nil), (*CSIOperatorSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CSIOperatorSpec_To_v1_CSIOperatorSpec(a.(*platform.CSIOperatorSpec), b.(*CSIOperatorSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CSIOperatorStatus)(nil), (*platform.CSIOperatorStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIOperatorStatus_To_platform_CSIOperatorStatus(a.(*CSIOperatorStatus), b.(*platform.CSIOperatorStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CSIOperatorStatus)(nil), (*CSIOperatorStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CSIOperatorStatus_To_v1_CSIOperatorStatus(a.(*platform.CSIOperatorStatus), b.(*CSIOperatorStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CSIProxyOptions)(nil), (*platform.CSIProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CSIProxyOptions_To_platform_CSIProxyOptions(a.(*CSIProxyOptions), b.(*platform.CSIProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CSIProxyOptions)(nil), (*CSIProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CSIProxyOptions_To_v1_CSIProxyOptions(a.(*platform.CSIProxyOptions), b.(*CSIProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Cluster)(nil), (*platform.Cluster)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Cluster_To_platform_Cluster(a.(*Cluster), b.(*platform.Cluster), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Cluster)(nil), (*Cluster)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Cluster_To_v1_Cluster(a.(*platform.Cluster), b.(*Cluster), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddon)(nil), (*platform.ClusterAddon)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddon_To_platform_ClusterAddon(a.(*ClusterAddon), b.(*platform.ClusterAddon), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddon)(nil), (*ClusterAddon)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddon_To_v1_ClusterAddon(a.(*platform.ClusterAddon), b.(*ClusterAddon), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonList)(nil), (*platform.ClusterAddonList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonList_To_platform_ClusterAddonList(a.(*ClusterAddonList), b.(*platform.ClusterAddonList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonList)(nil), (*ClusterAddonList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonList_To_v1_ClusterAddonList(a.(*platform.ClusterAddonList), b.(*ClusterAddonList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonSpec)(nil), (*platform.ClusterAddonSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(a.(*ClusterAddonSpec), b.(*platform.ClusterAddonSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonSpec)(nil), (*ClusterAddonSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(a.(*platform.ClusterAddonSpec), b.(*ClusterAddonSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonStatus)(nil), (*platform.ClusterAddonStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(a.(*ClusterAddonStatus), b.(*platform.ClusterAddonStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonStatus)(nil), (*ClusterAddonStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(a.(*platform.ClusterAddonStatus), b.(*ClusterAddonStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonType)(nil), (*platform.ClusterAddonType)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonType_To_platform_ClusterAddonType(a.(*ClusterAddonType), b.(*platform.ClusterAddonType), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonType)(nil), (*ClusterAddonType)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonType_To_v1_ClusterAddonType(a.(*platform.ClusterAddonType), b.(*ClusterAddonType), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddonTypeList)(nil), (*platform.ClusterAddonTypeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList(a.(*ClusterAddonTypeList), b.(*platform.ClusterAddonTypeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddonTypeList)(nil), (*ClusterAddonTypeList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList(a.(*platform.ClusterAddonTypeList), b.(*ClusterAddonTypeList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterAddress)(nil), (*platform.ClusterAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterAddress_To_platform_ClusterAddress(a.(*ClusterAddress), b.(*platform.ClusterAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterAddress)(nil), (*ClusterAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterAddress_To_v1_ClusterAddress(a.(*platform.ClusterAddress), b.(*ClusterAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterApplyOptions)(nil), (*platform.ClusterApplyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions(a.(*ClusterApplyOptions), b.(*platform.ClusterApplyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterApplyOptions)(nil), (*ClusterApplyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions(a.(*platform.ClusterApplyOptions), b.(*ClusterApplyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterComponent)(nil), (*platform.ClusterComponent)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterComponent_To_platform_ClusterComponent(a.(*ClusterComponent), b.(*platform.ClusterComponent), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterComponent)(nil), (*ClusterComponent)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterComponent_To_v1_ClusterComponent(a.(*platform.ClusterComponent), b.(*ClusterComponent), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterComponentReplicas)(nil), (*platform.ClusterComponentReplicas)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(a.(*ClusterComponentReplicas), b.(*platform.ClusterComponentReplicas), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterComponentReplicas)(nil), (*ClusterComponentReplicas)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(a.(*platform.ClusterComponentReplicas), b.(*ClusterComponentReplicas), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterCondition)(nil), (*platform.ClusterCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterCondition_To_platform_ClusterCondition(a.(*ClusterCondition), b.(*platform.ClusterCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterCondition)(nil), (*ClusterCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterCondition_To_v1_ClusterCondition(a.(*platform.ClusterCondition), b.(*ClusterCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterCredential)(nil), (*platform.ClusterCredential)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterCredential_To_platform_ClusterCredential(a.(*ClusterCredential), b.(*platform.ClusterCredential), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterCredential)(nil), (*ClusterCredential)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterCredential_To_v1_ClusterCredential(a.(*platform.ClusterCredential), b.(*ClusterCredential), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterCredentialList)(nil), (*platform.ClusterCredentialList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterCredentialList_To_platform_ClusterCredentialList(a.(*ClusterCredentialList), b.(*platform.ClusterCredentialList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterCredentialList)(nil), (*ClusterCredentialList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterCredentialList_To_v1_ClusterCredentialList(a.(*platform.ClusterCredentialList), b.(*ClusterCredentialList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterFeature)(nil), (*platform.ClusterFeature)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterFeature_To_platform_ClusterFeature(a.(*ClusterFeature), b.(*platform.ClusterFeature), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterFeature)(nil), (*ClusterFeature)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterFeature_To_v1_ClusterFeature(a.(*platform.ClusterFeature), b.(*ClusterFeature), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterGroupAPIResourceItem)(nil), (*platform.ClusterGroupAPIResourceItem)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterGroupAPIResourceItem_To_platform_ClusterGroupAPIResourceItem(a.(*ClusterGroupAPIResourceItem), b.(*platform.ClusterGroupAPIResourceItem), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterGroupAPIResourceItem)(nil), (*ClusterGroupAPIResourceItem)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterGroupAPIResourceItem_To_v1_ClusterGroupAPIResourceItem(a.(*platform.ClusterGroupAPIResourceItem), b.(*ClusterGroupAPIResourceItem), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterGroupAPIResourceItems)(nil), (*platform.ClusterGroupAPIResourceItems)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterGroupAPIResourceItems_To_platform_ClusterGroupAPIResourceItems(a.(*ClusterGroupAPIResourceItems), b.(*platform.ClusterGroupAPIResourceItems), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterGroupAPIResourceItems)(nil), (*ClusterGroupAPIResourceItems)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterGroupAPIResourceItems_To_v1_ClusterGroupAPIResourceItems(a.(*platform.ClusterGroupAPIResourceItems), b.(*ClusterGroupAPIResourceItems), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterGroupAPIResourceItemsList)(nil), (*platform.ClusterGroupAPIResourceItemsList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterGroupAPIResourceItemsList_To_platform_ClusterGroupAPIResourceItemsList(a.(*ClusterGroupAPIResourceItemsList), b.(*platform.ClusterGroupAPIResourceItemsList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterGroupAPIResourceItemsList)(nil), (*ClusterGroupAPIResourceItemsList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterGroupAPIResourceItemsList_To_v1_ClusterGroupAPIResourceItemsList(a.(*platform.ClusterGroupAPIResourceItemsList), b.(*ClusterGroupAPIResourceItemsList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterGroupAPIResourceOptions)(nil), (*platform.ClusterGroupAPIResourceOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterGroupAPIResourceOptions_To_platform_ClusterGroupAPIResourceOptions(a.(*ClusterGroupAPIResourceOptions), b.(*platform.ClusterGroupAPIResourceOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterGroupAPIResourceOptions)(nil), (*ClusterGroupAPIResourceOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterGroupAPIResourceOptions_To_v1_ClusterGroupAPIResourceOptions(a.(*platform.ClusterGroupAPIResourceOptions), b.(*ClusterGroupAPIResourceOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterList)(nil), (*platform.ClusterList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterList_To_platform_ClusterList(a.(*ClusterList), b.(*platform.ClusterList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterList)(nil), (*ClusterList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterList_To_v1_ClusterList(a.(*platform.ClusterList), b.(*ClusterList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterMachine)(nil), (*platform.ClusterMachine)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterMachine_To_platform_ClusterMachine(a.(*ClusterMachine), b.(*platform.ClusterMachine), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterMachine)(nil), (*ClusterMachine)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterMachine_To_v1_ClusterMachine(a.(*platform.ClusterMachine), b.(*ClusterMachine), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterProperty)(nil), (*platform.ClusterProperty)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterProperty_To_platform_ClusterProperty(a.(*ClusterProperty), b.(*platform.ClusterProperty), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterProperty)(nil), (*ClusterProperty)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterProperty_To_v1_ClusterProperty(a.(*platform.ClusterProperty), b.(*ClusterProperty), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterResource)(nil), (*platform.ClusterResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterResource_To_platform_ClusterResource(a.(*ClusterResource), b.(*platform.ClusterResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterResource)(nil), (*ClusterResource)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterResource_To_v1_ClusterResource(a.(*platform.ClusterResource), b.(*ClusterResource), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterSpec)(nil), (*platform.ClusterSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterSpec_To_platform_ClusterSpec(a.(*ClusterSpec), b.(*platform.ClusterSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterSpec)(nil), (*ClusterSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterSpec_To_v1_ClusterSpec(a.(*platform.ClusterSpec), b.(*ClusterSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ClusterStatus)(nil), (*platform.ClusterStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ClusterStatus_To_platform_ClusterStatus(a.(*ClusterStatus), b.(*platform.ClusterStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ClusterStatus)(nil), (*ClusterStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ClusterStatus_To_v1_ClusterStatus(a.(*platform.ClusterStatus), b.(*ClusterStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ConfigMap)(nil), (*platform.ConfigMap)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMap_To_platform_ConfigMap(a.(*ConfigMap), b.(*platform.ConfigMap), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ConfigMap)(nil), (*ConfigMap)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ConfigMap_To_v1_ConfigMap(a.(*platform.ConfigMap), b.(*ConfigMap), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ConfigMapList)(nil), (*platform.ConfigMapList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ConfigMapList_To_platform_ConfigMapList(a.(*ConfigMapList), b.(*platform.ConfigMapList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ConfigMapList)(nil), (*ConfigMapList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ConfigMapList_To_v1_ConfigMapList(a.(*platform.ConfigMapList), b.(*ConfigMapList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CronHPA)(nil), (*platform.CronHPA)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CronHPA_To_platform_CronHPA(a.(*CronHPA), b.(*platform.CronHPA), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CronHPA)(nil), (*CronHPA)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CronHPA_To_v1_CronHPA(a.(*platform.CronHPA), b.(*CronHPA), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CronHPAList)(nil), (*platform.CronHPAList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CronHPAList_To_platform_CronHPAList(a.(*CronHPAList), b.(*platform.CronHPAList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CronHPAList)(nil), (*CronHPAList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CronHPAList_To_v1_CronHPAList(a.(*platform.CronHPAList), b.(*CronHPAList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CronHPAProxyOptions)(nil), (*platform.CronHPAProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CronHPAProxyOptions_To_platform_CronHPAProxyOptions(a.(*CronHPAProxyOptions), b.(*platform.CronHPAProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CronHPAProxyOptions)(nil), (*CronHPAProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CronHPAProxyOptions_To_v1_CronHPAProxyOptions(a.(*platform.CronHPAProxyOptions), b.(*CronHPAProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CronHPASpec)(nil), (*platform.CronHPASpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CronHPASpec_To_platform_CronHPASpec(a.(*CronHPASpec), b.(*platform.CronHPASpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CronHPASpec)(nil), (*CronHPASpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CronHPASpec_To_v1_CronHPASpec(a.(*platform.CronHPASpec), b.(*CronHPASpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*CronHPAStatus)(nil), (*platform.CronHPAStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_CronHPAStatus_To_platform_CronHPAStatus(a.(*CronHPAStatus), b.(*platform.CronHPAStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.CronHPAStatus)(nil), (*CronHPAStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_CronHPAStatus_To_v1_CronHPAStatus(a.(*platform.CronHPAStatus), b.(*CronHPAStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Etcd)(nil), (*platform.Etcd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Etcd_To_platform_Etcd(a.(*Etcd), b.(*platform.Etcd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Etcd)(nil), (*Etcd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Etcd_To_v1_Etcd(a.(*platform.Etcd), b.(*Etcd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ExternalAuthzWebhookAddr)(nil), (*platform.ExternalAuthzWebhookAddr)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ExternalAuthzWebhookAddr_To_platform_ExternalAuthzWebhookAddr(a.(*ExternalAuthzWebhookAddr), b.(*platform.ExternalAuthzWebhookAddr), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ExternalAuthzWebhookAddr)(nil), (*ExternalAuthzWebhookAddr)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ExternalAuthzWebhookAddr_To_v1_ExternalAuthzWebhookAddr(a.(*platform.ExternalAuthzWebhookAddr), b.(*ExternalAuthzWebhookAddr), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ExternalEtcd)(nil), (*platform.ExternalEtcd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ExternalEtcd_To_platform_ExternalEtcd(a.(*ExternalEtcd), b.(*platform.ExternalEtcd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ExternalEtcd)(nil), (*ExternalEtcd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ExternalEtcd_To_v1_ExternalEtcd(a.(*platform.ExternalEtcd), b.(*ExternalEtcd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*File)(nil), (*platform.File)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_File_To_platform_File(a.(*File), b.(*platform.File), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.File)(nil), (*File)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_File_To_v1_File(a.(*platform.File), b.(*File), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*HA)(nil), (*platform.HA)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_HA_To_platform_HA(a.(*HA), b.(*platform.HA), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.HA)(nil), (*HA)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_HA_To_v1_HA(a.(*platform.HA), b.(*HA), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*LocalEtcd)(nil), (*platform.LocalEtcd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_LocalEtcd_To_platform_LocalEtcd(a.(*LocalEtcd), b.(*platform.LocalEtcd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.LocalEtcd)(nil), (*LocalEtcd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_LocalEtcd_To_v1_LocalEtcd(a.(*platform.LocalEtcd), b.(*LocalEtcd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Machine)(nil), (*platform.Machine)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Machine_To_platform_Machine(a.(*Machine), b.(*platform.Machine), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Machine)(nil), (*Machine)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Machine_To_v1_Machine(a.(*platform.Machine), b.(*Machine), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*MachineAddress)(nil), (*platform.MachineAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_MachineAddress_To_platform_MachineAddress(a.(*MachineAddress), b.(*platform.MachineAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.MachineAddress)(nil), (*MachineAddress)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_MachineAddress_To_v1_MachineAddress(a.(*platform.MachineAddress), b.(*MachineAddress), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*MachineCondition)(nil), (*platform.MachineCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_MachineCondition_To_platform_MachineCondition(a.(*MachineCondition), b.(*platform.MachineCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.MachineCondition)(nil), (*MachineCondition)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_MachineCondition_To_v1_MachineCondition(a.(*platform.MachineCondition), b.(*MachineCondition), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*MachineList)(nil), (*platform.MachineList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_MachineList_To_platform_MachineList(a.(*MachineList), b.(*platform.MachineList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.MachineList)(nil), (*MachineList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_MachineList_To_v1_MachineList(a.(*platform.MachineList), b.(*MachineList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*MachineSpec)(nil), (*platform.MachineSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_MachineSpec_To_platform_MachineSpec(a.(*MachineSpec), b.(*platform.MachineSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.MachineSpec)(nil), (*MachineSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_MachineSpec_To_v1_MachineSpec(a.(*platform.MachineSpec), b.(*MachineSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*MachineStatus)(nil), (*platform.MachineStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_MachineStatus_To_platform_MachineStatus(a.(*MachineStatus), b.(*platform.MachineStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.MachineStatus)(nil), (*MachineStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_MachineStatus_To_v1_MachineStatus(a.(*platform.MachineStatus), b.(*MachineStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*MachineSystemInfo)(nil), (*platform.MachineSystemInfo)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_MachineSystemInfo_To_platform_MachineSystemInfo(a.(*MachineSystemInfo), b.(*platform.MachineSystemInfo), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.MachineSystemInfo)(nil), (*MachineSystemInfo)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_MachineSystemInfo_To_v1_MachineSystemInfo(a.(*platform.MachineSystemInfo), b.(*MachineSystemInfo), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentBackEnd)(nil), (*platform.PersistentBackEnd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(a.(*PersistentBackEnd), b.(*platform.PersistentBackEnd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentBackEnd)(nil), (*PersistentBackEnd)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(a.(*platform.PersistentBackEnd), b.(*PersistentBackEnd), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentEvent)(nil), (*platform.PersistentEvent)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentEvent_To_platform_PersistentEvent(a.(*PersistentEvent), b.(*platform.PersistentEvent), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentEvent)(nil), (*PersistentEvent)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentEvent_To_v1_PersistentEvent(a.(*platform.PersistentEvent), b.(*PersistentEvent), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentEventList)(nil), (*platform.PersistentEventList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentEventList_To_platform_PersistentEventList(a.(*PersistentEventList), b.(*platform.PersistentEventList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentEventList)(nil), (*PersistentEventList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentEventList_To_v1_PersistentEventList(a.(*platform.PersistentEventList), b.(*PersistentEventList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentEventSpec)(nil), (*platform.PersistentEventSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(a.(*PersistentEventSpec), b.(*platform.PersistentEventSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentEventSpec)(nil), (*PersistentEventSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(a.(*platform.PersistentEventSpec), b.(*PersistentEventSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*PersistentEventStatus)(nil), (*platform.PersistentEventStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(a.(*PersistentEventStatus), b.(*platform.PersistentEventStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.PersistentEventStatus)(nil), (*PersistentEventStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(a.(*platform.PersistentEventStatus), b.(*PersistentEventStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ProxyOptions)(nil), (*platform.ProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ProxyOptions_To_platform_ProxyOptions(a.(*ProxyOptions), b.(*platform.ProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ProxyOptions)(nil), (*ProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ProxyOptions_To_v1_ProxyOptions(a.(*platform.ProxyOptions), b.(*ProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Registry)(nil), (*platform.Registry)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Registry_To_platform_Registry(a.(*Registry), b.(*platform.Registry), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Registry)(nil), (*Registry)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Registry_To_v1_Registry(a.(*platform.Registry), b.(*Registry), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*RegistryList)(nil), (*platform.RegistryList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RegistryList_To_platform_RegistryList(a.(*RegistryList), b.(*platform.RegistryList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.RegistryList)(nil), (*RegistryList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_RegistryList_To_v1_RegistryList(a.(*platform.RegistryList), b.(*RegistryList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*RegistrySpec)(nil), (*platform.RegistrySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_RegistrySpec_To_platform_RegistrySpec(a.(*RegistrySpec), b.(*platform.RegistrySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.RegistrySpec)(nil), (*RegistrySpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_RegistrySpec_To_v1_RegistrySpec(a.(*platform.RegistrySpec), b.(*RegistrySpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ResourceRequirements)(nil), (*platform.ResourceRequirements)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ResourceRequirements_To_platform_ResourceRequirements(a.(*ResourceRequirements), b.(*platform.ResourceRequirements), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ResourceRequirements)(nil), (*ResourceRequirements)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ResourceRequirements_To_v1_ResourceRequirements(a.(*platform.ResourceRequirements), b.(*ResourceRequirements), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*StorageBackEndCLS)(nil), (*platform.StorageBackEndCLS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS(a.(*StorageBackEndCLS), b.(*platform.StorageBackEndCLS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.StorageBackEndCLS)(nil), (*StorageBackEndCLS)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS(a.(*platform.StorageBackEndCLS), b.(*StorageBackEndCLS), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*StorageBackEndES)(nil), (*platform.StorageBackEndES)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_StorageBackEndES_To_platform_StorageBackEndES(a.(*StorageBackEndES), b.(*platform.StorageBackEndES), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.StorageBackEndES)(nil), (*StorageBackEndES)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_StorageBackEndES_To_v1_StorageBackEndES(a.(*platform.StorageBackEndES), b.(*StorageBackEndES), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TKEHA)(nil), (*platform.TKEHA)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TKEHA_To_platform_TKEHA(a.(*TKEHA), b.(*platform.TKEHA), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TKEHA)(nil), (*TKEHA)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TKEHA_To_v1_TKEHA(a.(*platform.TKEHA), b.(*TKEHA), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TappController)(nil), (*platform.TappController)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TappController_To_platform_TappController(a.(*TappController), b.(*platform.TappController), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TappController)(nil), (*TappController)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TappController_To_v1_TappController(a.(*platform.TappController), b.(*TappController), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TappControllerList)(nil), (*platform.TappControllerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TappControllerList_To_platform_TappControllerList(a.(*TappControllerList), b.(*platform.TappControllerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TappControllerList)(nil), (*TappControllerList)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TappControllerList_To_v1_TappControllerList(a.(*platform.TappControllerList), b.(*TappControllerList), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TappControllerProxyOptions)(nil), (*platform.TappControllerProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TappControllerProxyOptions_To_platform_TappControllerProxyOptions(a.(*TappControllerProxyOptions), b.(*platform.TappControllerProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TappControllerProxyOptions)(nil), (*TappControllerProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TappControllerProxyOptions_To_v1_TappControllerProxyOptions(a.(*platform.TappControllerProxyOptions), b.(*TappControllerProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TappControllerSpec)(nil), (*platform.TappControllerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TappControllerSpec_To_platform_TappControllerSpec(a.(*TappControllerSpec), b.(*platform.TappControllerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TappControllerSpec)(nil), (*TappControllerSpec)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TappControllerSpec_To_v1_TappControllerSpec(a.(*platform.TappControllerSpec), b.(*TappControllerSpec), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*TappControllerStatus)(nil), (*platform.TappControllerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_TappControllerStatus_To_platform_TappControllerStatus(a.(*TappControllerStatus), b.(*platform.TappControllerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.TappControllerStatus)(nil), (*TappControllerStatus)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_TappControllerStatus_To_v1_TappControllerStatus(a.(*platform.TappControllerStatus), b.(*TappControllerStatus), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*ThirdPartyHA)(nil), (*platform.ThirdPartyHA)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_ThirdPartyHA_To_platform_ThirdPartyHA(a.(*ThirdPartyHA), b.(*platform.ThirdPartyHA), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.ThirdPartyHA)(nil), (*ThirdPartyHA)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_ThirdPartyHA_To_v1_ThirdPartyHA(a.(*platform.ThirdPartyHA), b.(*ThirdPartyHA), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*Upgrade)(nil), (*platform.Upgrade)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_Upgrade_To_platform_Upgrade(a.(*Upgrade), b.(*platform.Upgrade), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.Upgrade)(nil), (*Upgrade)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_Upgrade_To_v1_Upgrade(a.(*platform.Upgrade), b.(*Upgrade), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*UpgradeStrategy)(nil), (*platform.UpgradeStrategy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_v1_UpgradeStrategy_To_platform_UpgradeStrategy(a.(*UpgradeStrategy), b.(*platform.UpgradeStrategy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*platform.UpgradeStrategy)(nil), (*UpgradeStrategy)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_platform_UpgradeStrategy_To_v1_UpgradeStrategy(a.(*platform.UpgradeStrategy), b.(*UpgradeStrategy), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*CSIProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_CSIProxyOptions(a.(*url.Values), b.(*CSIProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*ClusterApplyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_ClusterApplyOptions(a.(*url.Values), b.(*ClusterApplyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*ClusterGroupAPIResourceOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_ClusterGroupAPIResourceOptions(a.(*url.Values), b.(*ClusterGroupAPIResourceOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*CronHPAProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_CronHPAProxyOptions(a.(*url.Values), b.(*CronHPAProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*ProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_ProxyOptions(a.(*url.Values), b.(*ProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*TappControllerProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_TappControllerProxyOptions(a.(*url.Values), b.(*TappControllerProxyOptions), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_v1_AddonSpec_To_platform_AddonSpec(in *AddonSpec, out *platform.AddonSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_AddonSpec_To_platform_AddonSpec is an autogenerated conversion function.
func Convert_v1_AddonSpec_To_platform_AddonSpec(in *AddonSpec, out *platform.AddonSpec, s conversion.Scope) error {
	return autoConvert_v1_AddonSpec_To_platform_AddonSpec(in, out, s)
}

func autoConvert_platform_AddonSpec_To_v1_AddonSpec(in *platform.AddonSpec, out *AddonSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_AddonSpec_To_v1_AddonSpec is an autogenerated conversion function.
func Convert_platform_AddonSpec_To_v1_AddonSpec(in *platform.AddonSpec, out *AddonSpec, s conversion.Scope) error {
	return autoConvert_platform_AddonSpec_To_v1_AddonSpec(in, out, s)
}

func autoConvert_v1_App_To_platform_App(in *App, out *platform.App, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Spec = in.Spec
	return nil
}

// Convert_v1_App_To_platform_App is an autogenerated conversion function.
func Convert_v1_App_To_platform_App(in *App, out *platform.App, s conversion.Scope) error {
	return autoConvert_v1_App_To_platform_App(in, out, s)
}

func autoConvert_platform_App_To_v1_App(in *platform.App, out *App, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Spec = in.Spec
	return nil
}

// Convert_platform_App_To_v1_App is an autogenerated conversion function.
func Convert_platform_App_To_v1_App(in *platform.App, out *App, s conversion.Scope) error {
	return autoConvert_platform_App_To_v1_App(in, out, s)
}

func autoConvert_v1_AuthzWebhookAddr_To_platform_AuthzWebhookAddr(in *AuthzWebhookAddr, out *platform.AuthzWebhookAddr, s conversion.Scope) error {
	out.Builtin = (*platform.BuiltinAuthzWebhookAddr)(unsafe.Pointer(in.Builtin))
	out.External = (*platform.ExternalAuthzWebhookAddr)(unsafe.Pointer(in.External))
	return nil
}

// Convert_v1_AuthzWebhookAddr_To_platform_AuthzWebhookAddr is an autogenerated conversion function.
func Convert_v1_AuthzWebhookAddr_To_platform_AuthzWebhookAddr(in *AuthzWebhookAddr, out *platform.AuthzWebhookAddr, s conversion.Scope) error {
	return autoConvert_v1_AuthzWebhookAddr_To_platform_AuthzWebhookAddr(in, out, s)
}

func autoConvert_platform_AuthzWebhookAddr_To_v1_AuthzWebhookAddr(in *platform.AuthzWebhookAddr, out *AuthzWebhookAddr, s conversion.Scope) error {
	out.Builtin = (*BuiltinAuthzWebhookAddr)(unsafe.Pointer(in.Builtin))
	out.External = (*ExternalAuthzWebhookAddr)(unsafe.Pointer(in.External))
	return nil
}

// Convert_platform_AuthzWebhookAddr_To_v1_AuthzWebhookAddr is an autogenerated conversion function.
func Convert_platform_AuthzWebhookAddr_To_v1_AuthzWebhookAddr(in *platform.AuthzWebhookAddr, out *AuthzWebhookAddr, s conversion.Scope) error {
	return autoConvert_platform_AuthzWebhookAddr_To_v1_AuthzWebhookAddr(in, out, s)
}

func autoConvert_v1_BuiltinAuthzWebhookAddr_To_platform_BuiltinAuthzWebhookAddr(in *BuiltinAuthzWebhookAddr, out *platform.BuiltinAuthzWebhookAddr, s conversion.Scope) error {
	return nil
}

// Convert_v1_BuiltinAuthzWebhookAddr_To_platform_BuiltinAuthzWebhookAddr is an autogenerated conversion function.
func Convert_v1_BuiltinAuthzWebhookAddr_To_platform_BuiltinAuthzWebhookAddr(in *BuiltinAuthzWebhookAddr, out *platform.BuiltinAuthzWebhookAddr, s conversion.Scope) error {
	return autoConvert_v1_BuiltinAuthzWebhookAddr_To_platform_BuiltinAuthzWebhookAddr(in, out, s)
}

func autoConvert_platform_BuiltinAuthzWebhookAddr_To_v1_BuiltinAuthzWebhookAddr(in *platform.BuiltinAuthzWebhookAddr, out *BuiltinAuthzWebhookAddr, s conversion.Scope) error {
	return nil
}

// Convert_platform_BuiltinAuthzWebhookAddr_To_v1_BuiltinAuthzWebhookAddr is an autogenerated conversion function.
func Convert_platform_BuiltinAuthzWebhookAddr_To_v1_BuiltinAuthzWebhookAddr(in *platform.BuiltinAuthzWebhookAddr, out *BuiltinAuthzWebhookAddr, s conversion.Scope) error {
	return autoConvert_platform_BuiltinAuthzWebhookAddr_To_v1_BuiltinAuthzWebhookAddr(in, out, s)
}

func autoConvert_v1_CSIOperator_To_platform_CSIOperator(in *CSIOperator, out *platform.CSIOperator, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_CSIOperatorSpec_To_platform_CSIOperatorSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_CSIOperatorStatus_To_platform_CSIOperatorStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_CSIOperator_To_platform_CSIOperator is an autogenerated conversion function.
func Convert_v1_CSIOperator_To_platform_CSIOperator(in *CSIOperator, out *platform.CSIOperator, s conversion.Scope) error {
	return autoConvert_v1_CSIOperator_To_platform_CSIOperator(in, out, s)
}

func autoConvert_platform_CSIOperator_To_v1_CSIOperator(in *platform.CSIOperator, out *CSIOperator, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_CSIOperatorSpec_To_v1_CSIOperatorSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_CSIOperatorStatus_To_v1_CSIOperatorStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_CSIOperator_To_v1_CSIOperator is an autogenerated conversion function.
func Convert_platform_CSIOperator_To_v1_CSIOperator(in *platform.CSIOperator, out *CSIOperator, s conversion.Scope) error {
	return autoConvert_platform_CSIOperator_To_v1_CSIOperator(in, out, s)
}

func autoConvert_v1_CSIOperatorFeature_To_platform_CSIOperatorFeature(in *CSIOperatorFeature, out *platform.CSIOperatorFeature, s conversion.Scope) error {
	out.Version = in.Version
	return nil
}

// Convert_v1_CSIOperatorFeature_To_platform_CSIOperatorFeature is an autogenerated conversion function.
func Convert_v1_CSIOperatorFeature_To_platform_CSIOperatorFeature(in *CSIOperatorFeature, out *platform.CSIOperatorFeature, s conversion.Scope) error {
	return autoConvert_v1_CSIOperatorFeature_To_platform_CSIOperatorFeature(in, out, s)
}

func autoConvert_platform_CSIOperatorFeature_To_v1_CSIOperatorFeature(in *platform.CSIOperatorFeature, out *CSIOperatorFeature, s conversion.Scope) error {
	out.Version = in.Version
	return nil
}

// Convert_platform_CSIOperatorFeature_To_v1_CSIOperatorFeature is an autogenerated conversion function.
func Convert_platform_CSIOperatorFeature_To_v1_CSIOperatorFeature(in *platform.CSIOperatorFeature, out *CSIOperatorFeature, s conversion.Scope) error {
	return autoConvert_platform_CSIOperatorFeature_To_v1_CSIOperatorFeature(in, out, s)
}

func autoConvert_v1_CSIOperatorList_To_platform_CSIOperatorList(in *CSIOperatorList, out *platform.CSIOperatorList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.CSIOperator)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_CSIOperatorList_To_platform_CSIOperatorList is an autogenerated conversion function.
func Convert_v1_CSIOperatorList_To_platform_CSIOperatorList(in *CSIOperatorList, out *platform.CSIOperatorList, s conversion.Scope) error {
	return autoConvert_v1_CSIOperatorList_To_platform_CSIOperatorList(in, out, s)
}

func autoConvert_platform_CSIOperatorList_To_v1_CSIOperatorList(in *platform.CSIOperatorList, out *CSIOperatorList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]CSIOperator)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_CSIOperatorList_To_v1_CSIOperatorList is an autogenerated conversion function.
func Convert_platform_CSIOperatorList_To_v1_CSIOperatorList(in *platform.CSIOperatorList, out *CSIOperatorList, s conversion.Scope) error {
	return autoConvert_platform_CSIOperatorList_To_v1_CSIOperatorList(in, out, s)
}

func autoConvert_v1_CSIOperatorSpec_To_platform_CSIOperatorSpec(in *CSIOperatorSpec, out *platform.CSIOperatorSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_CSIOperatorSpec_To_platform_CSIOperatorSpec is an autogenerated conversion function.
func Convert_v1_CSIOperatorSpec_To_platform_CSIOperatorSpec(in *CSIOperatorSpec, out *platform.CSIOperatorSpec, s conversion.Scope) error {
	return autoConvert_v1_CSIOperatorSpec_To_platform_CSIOperatorSpec(in, out, s)
}

func autoConvert_platform_CSIOperatorSpec_To_v1_CSIOperatorSpec(in *platform.CSIOperatorSpec, out *CSIOperatorSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_CSIOperatorSpec_To_v1_CSIOperatorSpec is an autogenerated conversion function.
func Convert_platform_CSIOperatorSpec_To_v1_CSIOperatorSpec(in *platform.CSIOperatorSpec, out *CSIOperatorSpec, s conversion.Scope) error {
	return autoConvert_platform_CSIOperatorSpec_To_v1_CSIOperatorSpec(in, out, s)
}

func autoConvert_v1_CSIOperatorStatus_To_platform_CSIOperatorStatus(in *CSIOperatorStatus, out *platform.CSIOperatorStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.StorageVendorVersion = in.StorageVendorVersion
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_CSIOperatorStatus_To_platform_CSIOperatorStatus is an autogenerated conversion function.
func Convert_v1_CSIOperatorStatus_To_platform_CSIOperatorStatus(in *CSIOperatorStatus, out *platform.CSIOperatorStatus, s conversion.Scope) error {
	return autoConvert_v1_CSIOperatorStatus_To_platform_CSIOperatorStatus(in, out, s)
}

func autoConvert_platform_CSIOperatorStatus_To_v1_CSIOperatorStatus(in *platform.CSIOperatorStatus, out *CSIOperatorStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.StorageVendorVersion = in.StorageVendorVersion
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_CSIOperatorStatus_To_v1_CSIOperatorStatus is an autogenerated conversion function.
func Convert_platform_CSIOperatorStatus_To_v1_CSIOperatorStatus(in *platform.CSIOperatorStatus, out *CSIOperatorStatus, s conversion.Scope) error {
	return autoConvert_platform_CSIOperatorStatus_To_v1_CSIOperatorStatus(in, out, s)
}

func autoConvert_v1_CSIProxyOptions_To_platform_CSIProxyOptions(in *CSIProxyOptions, out *platform.CSIProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	return nil
}

// Convert_v1_CSIProxyOptions_To_platform_CSIProxyOptions is an autogenerated conversion function.
func Convert_v1_CSIProxyOptions_To_platform_CSIProxyOptions(in *CSIProxyOptions, out *platform.CSIProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_CSIProxyOptions_To_platform_CSIProxyOptions(in, out, s)
}

func autoConvert_platform_CSIProxyOptions_To_v1_CSIProxyOptions(in *platform.CSIProxyOptions, out *CSIProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	return nil
}

// Convert_platform_CSIProxyOptions_To_v1_CSIProxyOptions is an autogenerated conversion function.
func Convert_platform_CSIProxyOptions_To_v1_CSIProxyOptions(in *platform.CSIProxyOptions, out *CSIProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_CSIProxyOptions_To_v1_CSIProxyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_CSIProxyOptions(in *url.Values, out *CSIProxyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["namespace"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Namespace, s); err != nil {
			return err
		}
	} else {
		out.Namespace = ""
	}
	if values, ok := map[string][]string(*in)["name"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Name, s); err != nil {
			return err
		}
	} else {
		out.Name = ""
	}
	return nil
}

// Convert_url_Values_To_v1_CSIProxyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_CSIProxyOptions(in *url.Values, out *CSIProxyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_CSIProxyOptions(in, out, s)
}

func autoConvert_v1_Cluster_To_platform_Cluster(in *Cluster, out *platform.Cluster, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ClusterSpec_To_platform_ClusterSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ClusterStatus_To_platform_ClusterStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Cluster_To_platform_Cluster is an autogenerated conversion function.
func Convert_v1_Cluster_To_platform_Cluster(in *Cluster, out *platform.Cluster, s conversion.Scope) error {
	return autoConvert_v1_Cluster_To_platform_Cluster(in, out, s)
}

func autoConvert_platform_Cluster_To_v1_Cluster(in *platform.Cluster, out *Cluster, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_ClusterSpec_To_v1_ClusterSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_ClusterStatus_To_v1_ClusterStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Cluster_To_v1_Cluster is an autogenerated conversion function.
func Convert_platform_Cluster_To_v1_Cluster(in *platform.Cluster, out *Cluster, s conversion.Scope) error {
	return autoConvert_platform_Cluster_To_v1_Cluster(in, out, s)
}

func autoConvert_v1_ClusterAddon_To_platform_ClusterAddon(in *ClusterAddon, out *platform.ClusterAddon, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ClusterAddon_To_platform_ClusterAddon is an autogenerated conversion function.
func Convert_v1_ClusterAddon_To_platform_ClusterAddon(in *ClusterAddon, out *platform.ClusterAddon, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddon_To_platform_ClusterAddon(in, out, s)
}

func autoConvert_platform_ClusterAddon_To_v1_ClusterAddon(in *platform.ClusterAddon, out *ClusterAddon, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_ClusterAddon_To_v1_ClusterAddon is an autogenerated conversion function.
func Convert_platform_ClusterAddon_To_v1_ClusterAddon(in *platform.ClusterAddon, out *ClusterAddon, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddon_To_v1_ClusterAddon(in, out, s)
}

func autoConvert_v1_ClusterAddonList_To_platform_ClusterAddonList(in *ClusterAddonList, out *platform.ClusterAddonList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterAddon)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ClusterAddonList_To_platform_ClusterAddonList is an autogenerated conversion function.
func Convert_v1_ClusterAddonList_To_platform_ClusterAddonList(in *ClusterAddonList, out *platform.ClusterAddonList, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonList_To_platform_ClusterAddonList(in, out, s)
}

func autoConvert_platform_ClusterAddonList_To_v1_ClusterAddonList(in *platform.ClusterAddonList, out *ClusterAddonList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterAddon)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ClusterAddonList_To_v1_ClusterAddonList is an autogenerated conversion function.
func Convert_platform_ClusterAddonList_To_v1_ClusterAddonList(in *platform.ClusterAddonList, out *ClusterAddonList, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonList_To_v1_ClusterAddonList(in, out, s)
}

func autoConvert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(in *ClusterAddonSpec, out *platform.ClusterAddonSpec, s conversion.Scope) error {
	out.Type = in.Type
	out.Level = platform.AddonLevel(in.Level)
	out.Version = in.Version
	return nil
}

// Convert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec is an autogenerated conversion function.
func Convert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(in *ClusterAddonSpec, out *platform.ClusterAddonSpec, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonSpec_To_platform_ClusterAddonSpec(in, out, s)
}

func autoConvert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(in *platform.ClusterAddonSpec, out *ClusterAddonSpec, s conversion.Scope) error {
	out.Type = in.Type
	out.Level = AddonLevel(in.Level)
	out.Version = in.Version
	return nil
}

// Convert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec is an autogenerated conversion function.
func Convert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(in *platform.ClusterAddonSpec, out *ClusterAddonSpec, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonSpec_To_v1_ClusterAddonSpec(in, out, s)
}

func autoConvert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(in *ClusterAddonStatus, out *platform.ClusterAddonStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = in.Phase
	out.Reason = in.Reason
	return nil
}

// Convert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus is an autogenerated conversion function.
func Convert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(in *ClusterAddonStatus, out *platform.ClusterAddonStatus, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonStatus_To_platform_ClusterAddonStatus(in, out, s)
}

func autoConvert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(in *platform.ClusterAddonStatus, out *ClusterAddonStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = in.Phase
	out.Reason = in.Reason
	return nil
}

// Convert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus is an autogenerated conversion function.
func Convert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(in *platform.ClusterAddonStatus, out *ClusterAddonStatus, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonStatus_To_v1_ClusterAddonStatus(in, out, s)
}

func autoConvert_v1_ClusterAddonType_To_platform_ClusterAddonType(in *ClusterAddonType, out *platform.ClusterAddonType, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Type = in.Type
	out.Level = platform.AddonLevel(in.Level)
	out.LatestVersion = in.LatestVersion
	out.Description = in.Description
	out.CompatibleClusterType = *(*[]string)(unsafe.Pointer(&in.CompatibleClusterType))
	return nil
}

// Convert_v1_ClusterAddonType_To_platform_ClusterAddonType is an autogenerated conversion function.
func Convert_v1_ClusterAddonType_To_platform_ClusterAddonType(in *ClusterAddonType, out *platform.ClusterAddonType, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonType_To_platform_ClusterAddonType(in, out, s)
}

func autoConvert_platform_ClusterAddonType_To_v1_ClusterAddonType(in *platform.ClusterAddonType, out *ClusterAddonType, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Type = in.Type
	out.Level = AddonLevel(in.Level)
	out.LatestVersion = in.LatestVersion
	out.Description = in.Description
	out.CompatibleClusterType = *(*[]string)(unsafe.Pointer(&in.CompatibleClusterType))
	return nil
}

// Convert_platform_ClusterAddonType_To_v1_ClusterAddonType is an autogenerated conversion function.
func Convert_platform_ClusterAddonType_To_v1_ClusterAddonType(in *platform.ClusterAddonType, out *ClusterAddonType, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonType_To_v1_ClusterAddonType(in, out, s)
}

func autoConvert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList(in *ClusterAddonTypeList, out *platform.ClusterAddonTypeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterAddonType)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList is an autogenerated conversion function.
func Convert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList(in *ClusterAddonTypeList, out *platform.ClusterAddonTypeList, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddonTypeList_To_platform_ClusterAddonTypeList(in, out, s)
}

func autoConvert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList(in *platform.ClusterAddonTypeList, out *ClusterAddonTypeList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterAddonType)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList is an autogenerated conversion function.
func Convert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList(in *platform.ClusterAddonTypeList, out *ClusterAddonTypeList, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddonTypeList_To_v1_ClusterAddonTypeList(in, out, s)
}

func autoConvert_v1_ClusterAddress_To_platform_ClusterAddress(in *ClusterAddress, out *platform.ClusterAddress, s conversion.Scope) error {
	out.Type = platform.AddressType(in.Type)
	out.Host = in.Host
	out.Port = in.Port
	out.Path = in.Path
	return nil
}

// Convert_v1_ClusterAddress_To_platform_ClusterAddress is an autogenerated conversion function.
func Convert_v1_ClusterAddress_To_platform_ClusterAddress(in *ClusterAddress, out *platform.ClusterAddress, s conversion.Scope) error {
	return autoConvert_v1_ClusterAddress_To_platform_ClusterAddress(in, out, s)
}

func autoConvert_platform_ClusterAddress_To_v1_ClusterAddress(in *platform.ClusterAddress, out *ClusterAddress, s conversion.Scope) error {
	out.Type = AddressType(in.Type)
	out.Host = in.Host
	out.Port = in.Port
	out.Path = in.Path
	return nil
}

// Convert_platform_ClusterAddress_To_v1_ClusterAddress is an autogenerated conversion function.
func Convert_platform_ClusterAddress_To_v1_ClusterAddress(in *platform.ClusterAddress, out *ClusterAddress, s conversion.Scope) error {
	return autoConvert_platform_ClusterAddress_To_v1_ClusterAddress(in, out, s)
}

func autoConvert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions(in *ClusterApplyOptions, out *platform.ClusterApplyOptions, s conversion.Scope) error {
	out.NotUpdate = in.NotUpdate
	return nil
}

// Convert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions is an autogenerated conversion function.
func Convert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions(in *ClusterApplyOptions, out *platform.ClusterApplyOptions, s conversion.Scope) error {
	return autoConvert_v1_ClusterApplyOptions_To_platform_ClusterApplyOptions(in, out, s)
}

func autoConvert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions(in *platform.ClusterApplyOptions, out *ClusterApplyOptions, s conversion.Scope) error {
	out.NotUpdate = in.NotUpdate
	return nil
}

// Convert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions is an autogenerated conversion function.
func Convert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions(in *platform.ClusterApplyOptions, out *ClusterApplyOptions, s conversion.Scope) error {
	return autoConvert_platform_ClusterApplyOptions_To_v1_ClusterApplyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_ClusterApplyOptions(in *url.Values, out *ClusterApplyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["notUpdate"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_bool(&values, &out.NotUpdate, s); err != nil {
			return err
		}
	} else {
		out.NotUpdate = false
	}
	return nil
}

// Convert_url_Values_To_v1_ClusterApplyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_ClusterApplyOptions(in *url.Values, out *ClusterApplyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_ClusterApplyOptions(in, out, s)
}

func autoConvert_v1_ClusterComponent_To_platform_ClusterComponent(in *ClusterComponent, out *platform.ClusterComponent, s conversion.Scope) error {
	out.Type = in.Type
	if err := Convert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(&in.Replicas, &out.Replicas, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ClusterComponent_To_platform_ClusterComponent is an autogenerated conversion function.
func Convert_v1_ClusterComponent_To_platform_ClusterComponent(in *ClusterComponent, out *platform.ClusterComponent, s conversion.Scope) error {
	return autoConvert_v1_ClusterComponent_To_platform_ClusterComponent(in, out, s)
}

func autoConvert_platform_ClusterComponent_To_v1_ClusterComponent(in *platform.ClusterComponent, out *ClusterComponent, s conversion.Scope) error {
	out.Type = in.Type
	if err := Convert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(&in.Replicas, &out.Replicas, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_ClusterComponent_To_v1_ClusterComponent is an autogenerated conversion function.
func Convert_platform_ClusterComponent_To_v1_ClusterComponent(in *platform.ClusterComponent, out *ClusterComponent, s conversion.Scope) error {
	return autoConvert_platform_ClusterComponent_To_v1_ClusterComponent(in, out, s)
}

func autoConvert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(in *ClusterComponentReplicas, out *platform.ClusterComponentReplicas, s conversion.Scope) error {
	out.Desired = in.Desired
	out.Current = in.Current
	out.Available = in.Available
	out.Updated = in.Updated
	return nil
}

// Convert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas is an autogenerated conversion function.
func Convert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(in *ClusterComponentReplicas, out *platform.ClusterComponentReplicas, s conversion.Scope) error {
	return autoConvert_v1_ClusterComponentReplicas_To_platform_ClusterComponentReplicas(in, out, s)
}

func autoConvert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(in *platform.ClusterComponentReplicas, out *ClusterComponentReplicas, s conversion.Scope) error {
	out.Desired = in.Desired
	out.Current = in.Current
	out.Available = in.Available
	out.Updated = in.Updated
	return nil
}

// Convert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas is an autogenerated conversion function.
func Convert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(in *platform.ClusterComponentReplicas, out *ClusterComponentReplicas, s conversion.Scope) error {
	return autoConvert_platform_ClusterComponentReplicas_To_v1_ClusterComponentReplicas(in, out, s)
}

func autoConvert_v1_ClusterCondition_To_platform_ClusterCondition(in *ClusterCondition, out *platform.ClusterCondition, s conversion.Scope) error {
	out.Type = in.Type
	out.Status = platform.ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_ClusterCondition_To_platform_ClusterCondition is an autogenerated conversion function.
func Convert_v1_ClusterCondition_To_platform_ClusterCondition(in *ClusterCondition, out *platform.ClusterCondition, s conversion.Scope) error {
	return autoConvert_v1_ClusterCondition_To_platform_ClusterCondition(in, out, s)
}

func autoConvert_platform_ClusterCondition_To_v1_ClusterCondition(in *platform.ClusterCondition, out *ClusterCondition, s conversion.Scope) error {
	out.Type = in.Type
	out.Status = ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_platform_ClusterCondition_To_v1_ClusterCondition is an autogenerated conversion function.
func Convert_platform_ClusterCondition_To_v1_ClusterCondition(in *platform.ClusterCondition, out *ClusterCondition, s conversion.Scope) error {
	return autoConvert_platform_ClusterCondition_To_v1_ClusterCondition(in, out, s)
}

func autoConvert_v1_ClusterCredential_To_platform_ClusterCredential(in *ClusterCredential, out *platform.ClusterCredential, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.ETCDCACert = *(*[]byte)(unsafe.Pointer(&in.ETCDCACert))
	out.ETCDCAKey = *(*[]byte)(unsafe.Pointer(&in.ETCDCAKey))
	out.ETCDAPIClientCert = *(*[]byte)(unsafe.Pointer(&in.ETCDAPIClientCert))
	out.ETCDAPIClientKey = *(*[]byte)(unsafe.Pointer(&in.ETCDAPIClientKey))
	out.CACert = *(*[]byte)(unsafe.Pointer(&in.CACert))
	out.CAKey = *(*[]byte)(unsafe.Pointer(&in.CAKey))
	out.ClientCert = *(*[]byte)(unsafe.Pointer(&in.ClientCert))
	out.ClientKey = *(*[]byte)(unsafe.Pointer(&in.ClientKey))
	out.Token = (*string)(unsafe.Pointer(in.Token))
	out.BootstrapToken = (*string)(unsafe.Pointer(in.BootstrapToken))
	out.CertificateKey = (*string)(unsafe.Pointer(in.CertificateKey))
	out.Username = in.Username
	out.Impersonate = in.Impersonate
	out.ImpersonateGroups = *(*[]string)(unsafe.Pointer(&in.ImpersonateGroups))
	out.ImpersonateUserExtra = *(*platform.ImpersonateUserExtra)(unsafe.Pointer(&in.ImpersonateUserExtra))
	return nil
}

// Convert_v1_ClusterCredential_To_platform_ClusterCredential is an autogenerated conversion function.
func Convert_v1_ClusterCredential_To_platform_ClusterCredential(in *ClusterCredential, out *platform.ClusterCredential, s conversion.Scope) error {
	return autoConvert_v1_ClusterCredential_To_platform_ClusterCredential(in, out, s)
}

func autoConvert_platform_ClusterCredential_To_v1_ClusterCredential(in *platform.ClusterCredential, out *ClusterCredential, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.ETCDCACert = *(*[]byte)(unsafe.Pointer(&in.ETCDCACert))
	out.ETCDCAKey = *(*[]byte)(unsafe.Pointer(&in.ETCDCAKey))
	out.ETCDAPIClientCert = *(*[]byte)(unsafe.Pointer(&in.ETCDAPIClientCert))
	out.ETCDAPIClientKey = *(*[]byte)(unsafe.Pointer(&in.ETCDAPIClientKey))
	out.CACert = *(*[]byte)(unsafe.Pointer(&in.CACert))
	out.CAKey = *(*[]byte)(unsafe.Pointer(&in.CAKey))
	out.ClientCert = *(*[]byte)(unsafe.Pointer(&in.ClientCert))
	out.ClientKey = *(*[]byte)(unsafe.Pointer(&in.ClientKey))
	out.Token = (*string)(unsafe.Pointer(in.Token))
	out.BootstrapToken = (*string)(unsafe.Pointer(in.BootstrapToken))
	out.CertificateKey = (*string)(unsafe.Pointer(in.CertificateKey))
	out.Username = in.Username
	out.Impersonate = in.Impersonate
	out.ImpersonateGroups = *(*[]string)(unsafe.Pointer(&in.ImpersonateGroups))
	out.ImpersonateUserExtra = *(*ImpersonateUserExtra)(unsafe.Pointer(&in.ImpersonateUserExtra))
	return nil
}

// Convert_platform_ClusterCredential_To_v1_ClusterCredential is an autogenerated conversion function.
func Convert_platform_ClusterCredential_To_v1_ClusterCredential(in *platform.ClusterCredential, out *ClusterCredential, s conversion.Scope) error {
	return autoConvert_platform_ClusterCredential_To_v1_ClusterCredential(in, out, s)
}

func autoConvert_v1_ClusterCredentialList_To_platform_ClusterCredentialList(in *ClusterCredentialList, out *platform.ClusterCredentialList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterCredential)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ClusterCredentialList_To_platform_ClusterCredentialList is an autogenerated conversion function.
func Convert_v1_ClusterCredentialList_To_platform_ClusterCredentialList(in *ClusterCredentialList, out *platform.ClusterCredentialList, s conversion.Scope) error {
	return autoConvert_v1_ClusterCredentialList_To_platform_ClusterCredentialList(in, out, s)
}

func autoConvert_platform_ClusterCredentialList_To_v1_ClusterCredentialList(in *platform.ClusterCredentialList, out *ClusterCredentialList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterCredential)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ClusterCredentialList_To_v1_ClusterCredentialList is an autogenerated conversion function.
func Convert_platform_ClusterCredentialList_To_v1_ClusterCredentialList(in *platform.ClusterCredentialList, out *ClusterCredentialList, s conversion.Scope) error {
	return autoConvert_platform_ClusterCredentialList_To_v1_ClusterCredentialList(in, out, s)
}

func autoConvert_v1_ClusterFeature_To_platform_ClusterFeature(in *ClusterFeature, out *platform.ClusterFeature, s conversion.Scope) error {
	out.IPVS = (*bool)(unsafe.Pointer(in.IPVS))
	out.PublicLB = (*bool)(unsafe.Pointer(in.PublicLB))
	out.InternalLB = (*bool)(unsafe.Pointer(in.InternalLB))
	out.GPUType = (*platform.GPUType)(unsafe.Pointer(in.GPUType))
	out.EnableMasterSchedule = in.EnableMasterSchedule
	out.HA = (*platform.HA)(unsafe.Pointer(in.HA))
	out.SkipConditions = *(*[]string)(unsafe.Pointer(&in.SkipConditions))
	out.Files = *(*[]platform.File)(unsafe.Pointer(&in.Files))
	out.Hooks = *(*map[platform.HookType]string)(unsafe.Pointer(&in.Hooks))
	out.CSIOperator = (*platform.CSIOperatorFeature)(unsafe.Pointer(in.CSIOperator))
	out.AuthzWebhookAddr = (*platform.AuthzWebhookAddr)(unsafe.Pointer(in.AuthzWebhookAddr))
	out.EnableMetricsServer = in.EnableMetricsServer
	out.IPv6DualStack = in.IPv6DualStack
	out.EnableCilium = in.EnableCilium
	out.ContainerRuntime = in.ContainerRuntime
	if err := Convert_v1_Upgrade_To_platform_Upgrade(&in.Upgrade, &out.Upgrade, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_ClusterFeature_To_platform_ClusterFeature is an autogenerated conversion function.
func Convert_v1_ClusterFeature_To_platform_ClusterFeature(in *ClusterFeature, out *platform.ClusterFeature, s conversion.Scope) error {
	return autoConvert_v1_ClusterFeature_To_platform_ClusterFeature(in, out, s)
}

func autoConvert_platform_ClusterFeature_To_v1_ClusterFeature(in *platform.ClusterFeature, out *ClusterFeature, s conversion.Scope) error {
	out.IPVS = (*bool)(unsafe.Pointer(in.IPVS))
	out.PublicLB = (*bool)(unsafe.Pointer(in.PublicLB))
	out.InternalLB = (*bool)(unsafe.Pointer(in.InternalLB))
	out.GPUType = (*GPUType)(unsafe.Pointer(in.GPUType))
	out.EnableMasterSchedule = in.EnableMasterSchedule
	out.HA = (*HA)(unsafe.Pointer(in.HA))
	out.SkipConditions = *(*[]string)(unsafe.Pointer(&in.SkipConditions))
	out.Files = *(*[]File)(unsafe.Pointer(&in.Files))
	out.Hooks = *(*map[HookType]string)(unsafe.Pointer(&in.Hooks))
	out.CSIOperator = (*CSIOperatorFeature)(unsafe.Pointer(in.CSIOperator))
	out.AuthzWebhookAddr = (*AuthzWebhookAddr)(unsafe.Pointer(in.AuthzWebhookAddr))
	out.EnableMetricsServer = in.EnableMetricsServer
	out.EnableCilium = in.EnableCilium
	out.ContainerRuntime = in.ContainerRuntime
	out.IPv6DualStack = in.IPv6DualStack
	if err := Convert_platform_Upgrade_To_v1_Upgrade(&in.Upgrade, &out.Upgrade, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_ClusterFeature_To_v1_ClusterFeature is an autogenerated conversion function.
func Convert_platform_ClusterFeature_To_v1_ClusterFeature(in *platform.ClusterFeature, out *ClusterFeature, s conversion.Scope) error {
	return autoConvert_platform_ClusterFeature_To_v1_ClusterFeature(in, out, s)
}

func autoConvert_v1_ClusterGroupAPIResourceItem_To_platform_ClusterGroupAPIResourceItem(in *ClusterGroupAPIResourceItem, out *platform.ClusterGroupAPIResourceItem, s conversion.Scope) error {
	out.Name = in.Name
	out.SingularName = in.SingularName
	out.Namespaced = in.Namespaced
	out.Group = in.Group
	out.Version = in.Version
	out.Kind = in.Kind
	out.Verbs = *(*[]string)(unsafe.Pointer(&in.Verbs))
	out.ShortNames = *(*[]string)(unsafe.Pointer(&in.ShortNames))
	out.Categories = *(*[]string)(unsafe.Pointer(&in.Categories))
	return nil
}

// Convert_v1_ClusterGroupAPIResourceItem_To_platform_ClusterGroupAPIResourceItem is an autogenerated conversion function.
func Convert_v1_ClusterGroupAPIResourceItem_To_platform_ClusterGroupAPIResourceItem(in *ClusterGroupAPIResourceItem, out *platform.ClusterGroupAPIResourceItem, s conversion.Scope) error {
	return autoConvert_v1_ClusterGroupAPIResourceItem_To_platform_ClusterGroupAPIResourceItem(in, out, s)
}

func autoConvert_platform_ClusterGroupAPIResourceItem_To_v1_ClusterGroupAPIResourceItem(in *platform.ClusterGroupAPIResourceItem, out *ClusterGroupAPIResourceItem, s conversion.Scope) error {
	out.Name = in.Name
	out.SingularName = in.SingularName
	out.Namespaced = in.Namespaced
	out.Group = in.Group
	out.Version = in.Version
	out.Kind = in.Kind
	out.Verbs = *(*[]string)(unsafe.Pointer(&in.Verbs))
	out.ShortNames = *(*[]string)(unsafe.Pointer(&in.ShortNames))
	out.Categories = *(*[]string)(unsafe.Pointer(&in.Categories))
	return nil
}

// Convert_platform_ClusterGroupAPIResourceItem_To_v1_ClusterGroupAPIResourceItem is an autogenerated conversion function.
func Convert_platform_ClusterGroupAPIResourceItem_To_v1_ClusterGroupAPIResourceItem(in *platform.ClusterGroupAPIResourceItem, out *ClusterGroupAPIResourceItem, s conversion.Scope) error {
	return autoConvert_platform_ClusterGroupAPIResourceItem_To_v1_ClusterGroupAPIResourceItem(in, out, s)
}

func autoConvert_v1_ClusterGroupAPIResourceItems_To_platform_ClusterGroupAPIResourceItems(in *ClusterGroupAPIResourceItems, out *platform.ClusterGroupAPIResourceItems, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.GroupVersion = in.GroupVersion
	out.APIResources = *(*[]platform.ClusterGroupAPIResourceItem)(unsafe.Pointer(&in.APIResources))
	return nil
}

// Convert_v1_ClusterGroupAPIResourceItems_To_platform_ClusterGroupAPIResourceItems is an autogenerated conversion function.
func Convert_v1_ClusterGroupAPIResourceItems_To_platform_ClusterGroupAPIResourceItems(in *ClusterGroupAPIResourceItems, out *platform.ClusterGroupAPIResourceItems, s conversion.Scope) error {
	return autoConvert_v1_ClusterGroupAPIResourceItems_To_platform_ClusterGroupAPIResourceItems(in, out, s)
}

func autoConvert_platform_ClusterGroupAPIResourceItems_To_v1_ClusterGroupAPIResourceItems(in *platform.ClusterGroupAPIResourceItems, out *ClusterGroupAPIResourceItems, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.GroupVersion = in.GroupVersion
	out.APIResources = *(*[]ClusterGroupAPIResourceItem)(unsafe.Pointer(&in.APIResources))
	return nil
}

// Convert_platform_ClusterGroupAPIResourceItems_To_v1_ClusterGroupAPIResourceItems is an autogenerated conversion function.
func Convert_platform_ClusterGroupAPIResourceItems_To_v1_ClusterGroupAPIResourceItems(in *platform.ClusterGroupAPIResourceItems, out *ClusterGroupAPIResourceItems, s conversion.Scope) error {
	return autoConvert_platform_ClusterGroupAPIResourceItems_To_v1_ClusterGroupAPIResourceItems(in, out, s)
}

func autoConvert_v1_ClusterGroupAPIResourceItemsList_To_platform_ClusterGroupAPIResourceItemsList(in *ClusterGroupAPIResourceItemsList, out *platform.ClusterGroupAPIResourceItemsList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ClusterGroupAPIResourceItems)(unsafe.Pointer(&in.Items))
	out.FailedGroupError = in.FailedGroupError
	return nil
}

// Convert_v1_ClusterGroupAPIResourceItemsList_To_platform_ClusterGroupAPIResourceItemsList is an autogenerated conversion function.
func Convert_v1_ClusterGroupAPIResourceItemsList_To_platform_ClusterGroupAPIResourceItemsList(in *ClusterGroupAPIResourceItemsList, out *platform.ClusterGroupAPIResourceItemsList, s conversion.Scope) error {
	return autoConvert_v1_ClusterGroupAPIResourceItemsList_To_platform_ClusterGroupAPIResourceItemsList(in, out, s)
}

func autoConvert_platform_ClusterGroupAPIResourceItemsList_To_v1_ClusterGroupAPIResourceItemsList(in *platform.ClusterGroupAPIResourceItemsList, out *ClusterGroupAPIResourceItemsList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ClusterGroupAPIResourceItems)(unsafe.Pointer(&in.Items))
	out.FailedGroupError = in.FailedGroupError
	return nil
}

// Convert_platform_ClusterGroupAPIResourceItemsList_To_v1_ClusterGroupAPIResourceItemsList is an autogenerated conversion function.
func Convert_platform_ClusterGroupAPIResourceItemsList_To_v1_ClusterGroupAPIResourceItemsList(in *platform.ClusterGroupAPIResourceItemsList, out *ClusterGroupAPIResourceItemsList, s conversion.Scope) error {
	return autoConvert_platform_ClusterGroupAPIResourceItemsList_To_v1_ClusterGroupAPIResourceItemsList(in, out, s)
}

func autoConvert_v1_ClusterGroupAPIResourceOptions_To_platform_ClusterGroupAPIResourceOptions(in *ClusterGroupAPIResourceOptions, out *platform.ClusterGroupAPIResourceOptions, s conversion.Scope) error {
	return nil
}

// Convert_v1_ClusterGroupAPIResourceOptions_To_platform_ClusterGroupAPIResourceOptions is an autogenerated conversion function.
func Convert_v1_ClusterGroupAPIResourceOptions_To_platform_ClusterGroupAPIResourceOptions(in *ClusterGroupAPIResourceOptions, out *platform.ClusterGroupAPIResourceOptions, s conversion.Scope) error {
	return autoConvert_v1_ClusterGroupAPIResourceOptions_To_platform_ClusterGroupAPIResourceOptions(in, out, s)
}

func autoConvert_platform_ClusterGroupAPIResourceOptions_To_v1_ClusterGroupAPIResourceOptions(in *platform.ClusterGroupAPIResourceOptions, out *ClusterGroupAPIResourceOptions, s conversion.Scope) error {
	return nil
}

// Convert_platform_ClusterGroupAPIResourceOptions_To_v1_ClusterGroupAPIResourceOptions is an autogenerated conversion function.
func Convert_platform_ClusterGroupAPIResourceOptions_To_v1_ClusterGroupAPIResourceOptions(in *platform.ClusterGroupAPIResourceOptions, out *ClusterGroupAPIResourceOptions, s conversion.Scope) error {
	return autoConvert_platform_ClusterGroupAPIResourceOptions_To_v1_ClusterGroupAPIResourceOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_ClusterGroupAPIResourceOptions(in *url.Values, out *ClusterGroupAPIResourceOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	return nil
}

// Convert_url_Values_To_v1_ClusterGroupAPIResourceOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_ClusterGroupAPIResourceOptions(in *url.Values, out *ClusterGroupAPIResourceOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_ClusterGroupAPIResourceOptions(in, out, s)
}

func autoConvert_v1_ClusterList_To_platform_ClusterList(in *ClusterList, out *platform.ClusterList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]platform.Cluster, len(*in))
		for i := range *in {
			if err := Convert_v1_Cluster_To_platform_Cluster(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_v1_ClusterList_To_platform_ClusterList is an autogenerated conversion function.
func Convert_v1_ClusterList_To_platform_ClusterList(in *ClusterList, out *platform.ClusterList, s conversion.Scope) error {
	return autoConvert_v1_ClusterList_To_platform_ClusterList(in, out, s)
}

func autoConvert_platform_ClusterList_To_v1_ClusterList(in *platform.ClusterList, out *ClusterList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Cluster, len(*in))
		for i := range *in {
			if err := Convert_platform_Cluster_To_v1_Cluster(&(*in)[i], &(*out)[i], s); err != nil {
				return err
			}
		}
	} else {
		out.Items = nil
	}
	return nil
}

// Convert_platform_ClusterList_To_v1_ClusterList is an autogenerated conversion function.
func Convert_platform_ClusterList_To_v1_ClusterList(in *platform.ClusterList, out *ClusterList, s conversion.Scope) error {
	return autoConvert_platform_ClusterList_To_v1_ClusterList(in, out, s)
}

func autoConvert_v1_ClusterMachine_To_platform_ClusterMachine(in *ClusterMachine, out *platform.ClusterMachine, s conversion.Scope) error {
	out.IP = in.IP
	out.Port = in.Port
	out.Username = in.Username
	out.Password = *(*[]byte)(unsafe.Pointer(&in.Password))
	out.PrivateKey = *(*[]byte)(unsafe.Pointer(&in.PrivateKey))
	out.PassPhrase = *(*[]byte)(unsafe.Pointer(&in.PassPhrase))
	out.Labels = *(*map[string]string)(unsafe.Pointer(&in.Labels))
	out.Taints = *(*[]corev1.Taint)(unsafe.Pointer(&in.Taints))
	return nil
}

// Convert_v1_ClusterMachine_To_platform_ClusterMachine is an autogenerated conversion function.
func Convert_v1_ClusterMachine_To_platform_ClusterMachine(in *ClusterMachine, out *platform.ClusterMachine, s conversion.Scope) error {
	return autoConvert_v1_ClusterMachine_To_platform_ClusterMachine(in, out, s)
}

func autoConvert_platform_ClusterMachine_To_v1_ClusterMachine(in *platform.ClusterMachine, out *ClusterMachine, s conversion.Scope) error {
	out.IP = in.IP
	out.Port = in.Port
	out.Username = in.Username
	out.Password = *(*[]byte)(unsafe.Pointer(&in.Password))
	out.PrivateKey = *(*[]byte)(unsafe.Pointer(&in.PrivateKey))
	out.PassPhrase = *(*[]byte)(unsafe.Pointer(&in.PassPhrase))
	out.Labels = *(*map[string]string)(unsafe.Pointer(&in.Labels))
	out.Taints = *(*[]corev1.Taint)(unsafe.Pointer(&in.Taints))
	return nil
}

// Convert_platform_ClusterMachine_To_v1_ClusterMachine is an autogenerated conversion function.
func Convert_platform_ClusterMachine_To_v1_ClusterMachine(in *platform.ClusterMachine, out *ClusterMachine, s conversion.Scope) error {
	return autoConvert_platform_ClusterMachine_To_v1_ClusterMachine(in, out, s)
}

func autoConvert_v1_ClusterProperty_To_platform_ClusterProperty(in *ClusterProperty, out *platform.ClusterProperty, s conversion.Scope) error {
	out.MaxClusterServiceNum = (*int32)(unsafe.Pointer(in.MaxClusterServiceNum))
	out.MaxNodePodNum = (*int32)(unsafe.Pointer(in.MaxNodePodNum))
	out.OversoldRatio = *(*map[string]string)(unsafe.Pointer(&in.OversoldRatio))
	return nil
}

// Convert_v1_ClusterProperty_To_platform_ClusterProperty is an autogenerated conversion function.
func Convert_v1_ClusterProperty_To_platform_ClusterProperty(in *ClusterProperty, out *platform.ClusterProperty, s conversion.Scope) error {
	return autoConvert_v1_ClusterProperty_To_platform_ClusterProperty(in, out, s)
}

func autoConvert_platform_ClusterProperty_To_v1_ClusterProperty(in *platform.ClusterProperty, out *ClusterProperty, s conversion.Scope) error {
	out.MaxClusterServiceNum = (*int32)(unsafe.Pointer(in.MaxClusterServiceNum))
	out.MaxNodePodNum = (*int32)(unsafe.Pointer(in.MaxNodePodNum))
	out.OversoldRatio = *(*map[string]string)(unsafe.Pointer(&in.OversoldRatio))
	return nil
}

// Convert_platform_ClusterProperty_To_v1_ClusterProperty is an autogenerated conversion function.
func Convert_platform_ClusterProperty_To_v1_ClusterProperty(in *platform.ClusterProperty, out *ClusterProperty, s conversion.Scope) error {
	return autoConvert_platform_ClusterProperty_To_v1_ClusterProperty(in, out, s)
}

func autoConvert_v1_ClusterResource_To_platform_ClusterResource(in *ClusterResource, out *platform.ClusterResource, s conversion.Scope) error {
	out.Capacity = *(*platform.ResourceList)(unsafe.Pointer(&in.Capacity))
	out.Allocatable = *(*platform.ResourceList)(unsafe.Pointer(&in.Allocatable))
	out.Allocated = *(*platform.ResourceList)(unsafe.Pointer(&in.Allocated))
	return nil
}

// Convert_v1_ClusterResource_To_platform_ClusterResource is an autogenerated conversion function.
func Convert_v1_ClusterResource_To_platform_ClusterResource(in *ClusterResource, out *platform.ClusterResource, s conversion.Scope) error {
	return autoConvert_v1_ClusterResource_To_platform_ClusterResource(in, out, s)
}

func autoConvert_platform_ClusterResource_To_v1_ClusterResource(in *platform.ClusterResource, out *ClusterResource, s conversion.Scope) error {
	out.Capacity = *(*ResourceList)(unsafe.Pointer(&in.Capacity))
	out.Allocatable = *(*ResourceList)(unsafe.Pointer(&in.Allocatable))
	out.Allocated = *(*ResourceList)(unsafe.Pointer(&in.Allocated))
	return nil
}

// Convert_platform_ClusterResource_To_v1_ClusterResource is an autogenerated conversion function.
func Convert_platform_ClusterResource_To_v1_ClusterResource(in *platform.ClusterResource, out *ClusterResource, s conversion.Scope) error {
	return autoConvert_platform_ClusterResource_To_v1_ClusterResource(in, out, s)
}

func autoConvert_v1_ClusterSpec_To_platform_ClusterSpec(in *ClusterSpec, out *platform.ClusterSpec, s conversion.Scope) error {
	out.Finalizers = *(*[]platform.FinalizerName)(unsafe.Pointer(&in.Finalizers))
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.Type = in.Type
	out.Version = in.Version
	out.NetworkType = platform.NetworkType(in.NetworkType)
	out.NetworkDevice = in.NetworkDevice
	out.ClusterCIDR = in.ClusterCIDR
	out.ServiceCIDR = (*string)(unsafe.Pointer(in.ServiceCIDR))
	out.DNSDomain = in.DNSDomain
	out.PublicAlternativeNames = *(*[]string)(unsafe.Pointer(&in.PublicAlternativeNames))
	if err := Convert_v1_ClusterFeature_To_platform_ClusterFeature(&in.Features, &out.Features, s); err != nil {
		return err
	}
	if err := Convert_v1_ClusterProperty_To_platform_ClusterProperty(&in.Properties, &out.Properties, s); err != nil {
		return err
	}
	out.Machines = *(*[]platform.ClusterMachine)(unsafe.Pointer(&in.Machines))
	out.DockerExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.DockerExtraArgs))
	out.KubeletExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.KubeletExtraArgs))
	out.APIServerExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.APIServerExtraArgs))
	out.ControllerManagerExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.ControllerManagerExtraArgs))
	out.SchedulerExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.SchedulerExtraArgs))
	out.ClusterCredentialRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.ClusterCredentialRef))
	out.Etcd = (*platform.Etcd)(unsafe.Pointer(in.Etcd))
	out.HostnameAsNodename = in.HostnameAsNodename
	out.NetworkArgs = *(*map[string]string)(unsafe.Pointer(&in.NetworkArgs))
	out.ScalingMachines = *(*[]platform.ClusterMachine)(unsafe.Pointer(&in.ScalingMachines))
	out.BootstrapApps = *(*platform.BootstrapApps)(unsafe.Pointer(&in.BootstrapApps))
	return nil
}

// Convert_v1_ClusterSpec_To_platform_ClusterSpec is an autogenerated conversion function.
func Convert_v1_ClusterSpec_To_platform_ClusterSpec(in *ClusterSpec, out *platform.ClusterSpec, s conversion.Scope) error {
	return autoConvert_v1_ClusterSpec_To_platform_ClusterSpec(in, out, s)
}

func autoConvert_platform_ClusterSpec_To_v1_ClusterSpec(in *platform.ClusterSpec, out *ClusterSpec, s conversion.Scope) error {
	out.Finalizers = *(*[]FinalizerName)(unsafe.Pointer(&in.Finalizers))
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.Type = in.Type
	out.Version = in.Version
	out.NetworkType = NetworkType(in.NetworkType)
	out.NetworkDevice = in.NetworkDevice
	out.ClusterCIDR = in.ClusterCIDR
	out.ServiceCIDR = (*string)(unsafe.Pointer(in.ServiceCIDR))
	out.DNSDomain = in.DNSDomain
	out.PublicAlternativeNames = *(*[]string)(unsafe.Pointer(&in.PublicAlternativeNames))
	if err := Convert_platform_ClusterFeature_To_v1_ClusterFeature(&in.Features, &out.Features, s); err != nil {
		return err
	}
	if err := Convert_platform_ClusterProperty_To_v1_ClusterProperty(&in.Properties, &out.Properties, s); err != nil {
		return err
	}
	out.Machines = *(*[]ClusterMachine)(unsafe.Pointer(&in.Machines))
	out.ScalingMachines = *(*[]ClusterMachine)(unsafe.Pointer(&in.ScalingMachines))
	out.DockerExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.DockerExtraArgs))
	out.KubeletExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.KubeletExtraArgs))
	out.APIServerExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.APIServerExtraArgs))
	out.ControllerManagerExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.ControllerManagerExtraArgs))
	out.SchedulerExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.SchedulerExtraArgs))
	out.ClusterCredentialRef = (*corev1.LocalObjectReference)(unsafe.Pointer(in.ClusterCredentialRef))
	out.Etcd = (*Etcd)(unsafe.Pointer(in.Etcd))
	out.HostnameAsNodename = in.HostnameAsNodename
	out.NetworkArgs = *(*map[string]string)(unsafe.Pointer(&in.NetworkArgs))
	out.BootstrapApps = *(*BootstrapApps)(unsafe.Pointer(&in.BootstrapApps))
	return nil
}

// Convert_platform_ClusterSpec_To_v1_ClusterSpec is an autogenerated conversion function.
func Convert_platform_ClusterSpec_To_v1_ClusterSpec(in *platform.ClusterSpec, out *ClusterSpec, s conversion.Scope) error {
	return autoConvert_platform_ClusterSpec_To_v1_ClusterSpec(in, out, s)
}

func autoConvert_v1_ClusterStatus_To_platform_ClusterStatus(in *ClusterStatus, out *platform.ClusterStatus, s conversion.Scope) error {
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	out.Version = in.Version
	out.Phase = platform.ClusterPhase(in.Phase)
	out.Conditions = *(*[]platform.ClusterCondition)(unsafe.Pointer(&in.Conditions))
	out.Message = in.Message
	out.Reason = in.Reason
	out.Addresses = *(*[]platform.ClusterAddress)(unsafe.Pointer(&in.Addresses))
	if err := Convert_v1_ClusterResource_To_platform_ClusterResource(&in.Resource, &out.Resource, s); err != nil {
		return err
	}
	out.Components = *(*[]platform.ClusterComponent)(unsafe.Pointer(&in.Components))
	out.ServiceCIDR = in.ServiceCIDR
	out.NodeCIDRMaskSize = in.NodeCIDRMaskSize
	out.DNSIP = in.DNSIP
	out.RegistryIPs = *(*[]string)(unsafe.Pointer(&in.RegistryIPs))
	out.SecondaryServiceCIDR = in.SecondaryServiceCIDR
	out.ClusterCIDR = in.ClusterCIDR
	out.SecondaryClusterCIDR = in.SecondaryClusterCIDR
	out.NodeCIDRMaskSizeIPv4 = in.NodeCIDRMaskSizeIPv4
	out.NodeCIDRMaskSizeIPv6 = in.NodeCIDRMaskSizeIPv6
	out.KubeVendor = platform.KubeVendorType(in.KubeVendor)
	return nil
}

// Convert_v1_ClusterStatus_To_platform_ClusterStatus is an autogenerated conversion function.
func Convert_v1_ClusterStatus_To_platform_ClusterStatus(in *ClusterStatus, out *platform.ClusterStatus, s conversion.Scope) error {
	return autoConvert_v1_ClusterStatus_To_platform_ClusterStatus(in, out, s)
}

func autoConvert_platform_ClusterStatus_To_v1_ClusterStatus(in *platform.ClusterStatus, out *ClusterStatus, s conversion.Scope) error {
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	out.Version = in.Version
	out.Phase = ClusterPhase(in.Phase)
	out.Conditions = *(*[]ClusterCondition)(unsafe.Pointer(&in.Conditions))
	out.Message = in.Message
	out.Reason = in.Reason
	out.Addresses = *(*[]ClusterAddress)(unsafe.Pointer(&in.Addresses))
	if err := Convert_platform_ClusterResource_To_v1_ClusterResource(&in.Resource, &out.Resource, s); err != nil {
		return err
	}
	out.Components = *(*[]ClusterComponent)(unsafe.Pointer(&in.Components))
	out.ServiceCIDR = in.ServiceCIDR
	out.NodeCIDRMaskSize = in.NodeCIDRMaskSize
	out.DNSIP = in.DNSIP
	out.RegistryIPs = *(*[]string)(unsafe.Pointer(&in.RegistryIPs))
	out.ClusterCIDR = in.ClusterCIDR
	out.SecondaryServiceCIDR = in.SecondaryServiceCIDR
	out.SecondaryClusterCIDR = in.SecondaryClusterCIDR
	out.NodeCIDRMaskSizeIPv4 = in.NodeCIDRMaskSizeIPv4
	out.NodeCIDRMaskSizeIPv6 = in.NodeCIDRMaskSizeIPv6
	out.KubeVendor = KubeVendorType(in.KubeVendor)
	return nil
}

// Convert_platform_ClusterStatus_To_v1_ClusterStatus is an autogenerated conversion function.
func Convert_platform_ClusterStatus_To_v1_ClusterStatus(in *platform.ClusterStatus, out *ClusterStatus, s conversion.Scope) error {
	return autoConvert_platform_ClusterStatus_To_v1_ClusterStatus(in, out, s)
}

func autoConvert_v1_ConfigMap_To_platform_ConfigMap(in *ConfigMap, out *platform.ConfigMap, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Data = *(*map[string]string)(unsafe.Pointer(&in.Data))
	out.BinaryData = *(*map[string][]byte)(unsafe.Pointer(&in.BinaryData))
	return nil
}

// Convert_v1_ConfigMap_To_platform_ConfigMap is an autogenerated conversion function.
func Convert_v1_ConfigMap_To_platform_ConfigMap(in *ConfigMap, out *platform.ConfigMap, s conversion.Scope) error {
	return autoConvert_v1_ConfigMap_To_platform_ConfigMap(in, out, s)
}

func autoConvert_platform_ConfigMap_To_v1_ConfigMap(in *platform.ConfigMap, out *ConfigMap, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	out.Data = *(*map[string]string)(unsafe.Pointer(&in.Data))
	out.BinaryData = *(*map[string][]byte)(unsafe.Pointer(&in.BinaryData))
	return nil
}

// Convert_platform_ConfigMap_To_v1_ConfigMap is an autogenerated conversion function.
func Convert_platform_ConfigMap_To_v1_ConfigMap(in *platform.ConfigMap, out *ConfigMap, s conversion.Scope) error {
	return autoConvert_platform_ConfigMap_To_v1_ConfigMap(in, out, s)
}

func autoConvert_v1_ConfigMapList_To_platform_ConfigMapList(in *ConfigMapList, out *platform.ConfigMapList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.ConfigMap)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_ConfigMapList_To_platform_ConfigMapList is an autogenerated conversion function.
func Convert_v1_ConfigMapList_To_platform_ConfigMapList(in *ConfigMapList, out *platform.ConfigMapList, s conversion.Scope) error {
	return autoConvert_v1_ConfigMapList_To_platform_ConfigMapList(in, out, s)
}

func autoConvert_platform_ConfigMapList_To_v1_ConfigMapList(in *platform.ConfigMapList, out *ConfigMapList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]ConfigMap)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_ConfigMapList_To_v1_ConfigMapList is an autogenerated conversion function.
func Convert_platform_ConfigMapList_To_v1_ConfigMapList(in *platform.ConfigMapList, out *ConfigMapList, s conversion.Scope) error {
	return autoConvert_platform_ConfigMapList_To_v1_ConfigMapList(in, out, s)
}

func autoConvert_v1_CronHPA_To_platform_CronHPA(in *CronHPA, out *platform.CronHPA, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_CronHPASpec_To_platform_CronHPASpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_CronHPAStatus_To_platform_CronHPAStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_CronHPA_To_platform_CronHPA is an autogenerated conversion function.
func Convert_v1_CronHPA_To_platform_CronHPA(in *CronHPA, out *platform.CronHPA, s conversion.Scope) error {
	return autoConvert_v1_CronHPA_To_platform_CronHPA(in, out, s)
}

func autoConvert_platform_CronHPA_To_v1_CronHPA(in *platform.CronHPA, out *CronHPA, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_CronHPASpec_To_v1_CronHPASpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_CronHPAStatus_To_v1_CronHPAStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_CronHPA_To_v1_CronHPA is an autogenerated conversion function.
func Convert_platform_CronHPA_To_v1_CronHPA(in *platform.CronHPA, out *CronHPA, s conversion.Scope) error {
	return autoConvert_platform_CronHPA_To_v1_CronHPA(in, out, s)
}

func autoConvert_v1_CronHPAList_To_platform_CronHPAList(in *CronHPAList, out *platform.CronHPAList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.CronHPA)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_CronHPAList_To_platform_CronHPAList is an autogenerated conversion function.
func Convert_v1_CronHPAList_To_platform_CronHPAList(in *CronHPAList, out *platform.CronHPAList, s conversion.Scope) error {
	return autoConvert_v1_CronHPAList_To_platform_CronHPAList(in, out, s)
}

func autoConvert_platform_CronHPAList_To_v1_CronHPAList(in *platform.CronHPAList, out *CronHPAList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]CronHPA)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_CronHPAList_To_v1_CronHPAList is an autogenerated conversion function.
func Convert_platform_CronHPAList_To_v1_CronHPAList(in *platform.CronHPAList, out *CronHPAList, s conversion.Scope) error {
	return autoConvert_platform_CronHPAList_To_v1_CronHPAList(in, out, s)
}

func autoConvert_v1_CronHPAProxyOptions_To_platform_CronHPAProxyOptions(in *CronHPAProxyOptions, out *platform.CronHPAProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	return nil
}

// Convert_v1_CronHPAProxyOptions_To_platform_CronHPAProxyOptions is an autogenerated conversion function.
func Convert_v1_CronHPAProxyOptions_To_platform_CronHPAProxyOptions(in *CronHPAProxyOptions, out *platform.CronHPAProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_CronHPAProxyOptions_To_platform_CronHPAProxyOptions(in, out, s)
}

func autoConvert_platform_CronHPAProxyOptions_To_v1_CronHPAProxyOptions(in *platform.CronHPAProxyOptions, out *CronHPAProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	return nil
}

// Convert_platform_CronHPAProxyOptions_To_v1_CronHPAProxyOptions is an autogenerated conversion function.
func Convert_platform_CronHPAProxyOptions_To_v1_CronHPAProxyOptions(in *platform.CronHPAProxyOptions, out *CronHPAProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_CronHPAProxyOptions_To_v1_CronHPAProxyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_CronHPAProxyOptions(in *url.Values, out *CronHPAProxyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["namespace"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Namespace, s); err != nil {
			return err
		}
	} else {
		out.Namespace = ""
	}
	if values, ok := map[string][]string(*in)["name"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Name, s); err != nil {
			return err
		}
	} else {
		out.Name = ""
	}
	return nil
}

// Convert_url_Values_To_v1_CronHPAProxyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_CronHPAProxyOptions(in *url.Values, out *CronHPAProxyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_CronHPAProxyOptions(in, out, s)
}

func autoConvert_v1_CronHPASpec_To_platform_CronHPASpec(in *CronHPASpec, out *platform.CronHPASpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_CronHPASpec_To_platform_CronHPASpec is an autogenerated conversion function.
func Convert_v1_CronHPASpec_To_platform_CronHPASpec(in *CronHPASpec, out *platform.CronHPASpec, s conversion.Scope) error {
	return autoConvert_v1_CronHPASpec_To_platform_CronHPASpec(in, out, s)
}

func autoConvert_platform_CronHPASpec_To_v1_CronHPASpec(in *platform.CronHPASpec, out *CronHPASpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_CronHPASpec_To_v1_CronHPASpec is an autogenerated conversion function.
func Convert_platform_CronHPASpec_To_v1_CronHPASpec(in *platform.CronHPASpec, out *CronHPASpec, s conversion.Scope) error {
	return autoConvert_platform_CronHPASpec_To_v1_CronHPASpec(in, out, s)
}

func autoConvert_v1_CronHPAStatus_To_platform_CronHPAStatus(in *CronHPAStatus, out *platform.CronHPAStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_CronHPAStatus_To_platform_CronHPAStatus is an autogenerated conversion function.
func Convert_v1_CronHPAStatus_To_platform_CronHPAStatus(in *CronHPAStatus, out *platform.CronHPAStatus, s conversion.Scope) error {
	return autoConvert_v1_CronHPAStatus_To_platform_CronHPAStatus(in, out, s)
}

func autoConvert_platform_CronHPAStatus_To_v1_CronHPAStatus(in *platform.CronHPAStatus, out *CronHPAStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_CronHPAStatus_To_v1_CronHPAStatus is an autogenerated conversion function.
func Convert_platform_CronHPAStatus_To_v1_CronHPAStatus(in *platform.CronHPAStatus, out *CronHPAStatus, s conversion.Scope) error {
	return autoConvert_platform_CronHPAStatus_To_v1_CronHPAStatus(in, out, s)
}

func autoConvert_v1_Etcd_To_platform_Etcd(in *Etcd, out *platform.Etcd, s conversion.Scope) error {
	out.Local = (*platform.LocalEtcd)(unsafe.Pointer(in.Local))
	out.External = (*platform.ExternalEtcd)(unsafe.Pointer(in.External))
	return nil
}

// Convert_v1_Etcd_To_platform_Etcd is an autogenerated conversion function.
func Convert_v1_Etcd_To_platform_Etcd(in *Etcd, out *platform.Etcd, s conversion.Scope) error {
	return autoConvert_v1_Etcd_To_platform_Etcd(in, out, s)
}

func autoConvert_platform_Etcd_To_v1_Etcd(in *platform.Etcd, out *Etcd, s conversion.Scope) error {
	out.Local = (*LocalEtcd)(unsafe.Pointer(in.Local))
	out.External = (*ExternalEtcd)(unsafe.Pointer(in.External))
	return nil
}

// Convert_platform_Etcd_To_v1_Etcd is an autogenerated conversion function.
func Convert_platform_Etcd_To_v1_Etcd(in *platform.Etcd, out *Etcd, s conversion.Scope) error {
	return autoConvert_platform_Etcd_To_v1_Etcd(in, out, s)
}

func autoConvert_v1_ExternalAuthzWebhookAddr_To_platform_ExternalAuthzWebhookAddr(in *ExternalAuthzWebhookAddr, out *platform.ExternalAuthzWebhookAddr, s conversion.Scope) error {
	out.IP = in.IP
	out.Port = in.Port
	return nil
}

// Convert_v1_ExternalAuthzWebhookAddr_To_platform_ExternalAuthzWebhookAddr is an autogenerated conversion function.
func Convert_v1_ExternalAuthzWebhookAddr_To_platform_ExternalAuthzWebhookAddr(in *ExternalAuthzWebhookAddr, out *platform.ExternalAuthzWebhookAddr, s conversion.Scope) error {
	return autoConvert_v1_ExternalAuthzWebhookAddr_To_platform_ExternalAuthzWebhookAddr(in, out, s)
}

func autoConvert_platform_ExternalAuthzWebhookAddr_To_v1_ExternalAuthzWebhookAddr(in *platform.ExternalAuthzWebhookAddr, out *ExternalAuthzWebhookAddr, s conversion.Scope) error {
	out.IP = in.IP
	out.Port = in.Port
	return nil
}

// Convert_platform_ExternalAuthzWebhookAddr_To_v1_ExternalAuthzWebhookAddr is an autogenerated conversion function.
func Convert_platform_ExternalAuthzWebhookAddr_To_v1_ExternalAuthzWebhookAddr(in *platform.ExternalAuthzWebhookAddr, out *ExternalAuthzWebhookAddr, s conversion.Scope) error {
	return autoConvert_platform_ExternalAuthzWebhookAddr_To_v1_ExternalAuthzWebhookAddr(in, out, s)
}

func autoConvert_v1_ExternalEtcd_To_platform_ExternalEtcd(in *ExternalEtcd, out *platform.ExternalEtcd, s conversion.Scope) error {
	out.Endpoints = *(*[]string)(unsafe.Pointer(&in.Endpoints))
	out.CAFile = in.CAFile
	out.CertFile = in.CertFile
	out.KeyFile = in.KeyFile
	return nil
}

// Convert_v1_ExternalEtcd_To_platform_ExternalEtcd is an autogenerated conversion function.
func Convert_v1_ExternalEtcd_To_platform_ExternalEtcd(in *ExternalEtcd, out *platform.ExternalEtcd, s conversion.Scope) error {
	return autoConvert_v1_ExternalEtcd_To_platform_ExternalEtcd(in, out, s)
}

func autoConvert_platform_ExternalEtcd_To_v1_ExternalEtcd(in *platform.ExternalEtcd, out *ExternalEtcd, s conversion.Scope) error {
	out.Endpoints = *(*[]string)(unsafe.Pointer(&in.Endpoints))
	out.CAFile = in.CAFile
	out.CertFile = in.CertFile
	out.KeyFile = in.KeyFile
	return nil
}

// Convert_platform_ExternalEtcd_To_v1_ExternalEtcd is an autogenerated conversion function.
func Convert_platform_ExternalEtcd_To_v1_ExternalEtcd(in *platform.ExternalEtcd, out *ExternalEtcd, s conversion.Scope) error {
	return autoConvert_platform_ExternalEtcd_To_v1_ExternalEtcd(in, out, s)
}

func autoConvert_v1_File_To_platform_File(in *File, out *platform.File, s conversion.Scope) error {
	out.Src = in.Src
	out.Dst = in.Dst
	return nil
}

// Convert_v1_File_To_platform_File is an autogenerated conversion function.
func Convert_v1_File_To_platform_File(in *File, out *platform.File, s conversion.Scope) error {
	return autoConvert_v1_File_To_platform_File(in, out, s)
}

func autoConvert_platform_File_To_v1_File(in *platform.File, out *File, s conversion.Scope) error {
	out.Src = in.Src
	out.Dst = in.Dst
	return nil
}

// Convert_platform_File_To_v1_File is an autogenerated conversion function.
func Convert_platform_File_To_v1_File(in *platform.File, out *File, s conversion.Scope) error {
	return autoConvert_platform_File_To_v1_File(in, out, s)
}

func autoConvert_v1_HA_To_platform_HA(in *HA, out *platform.HA, s conversion.Scope) error {
	out.TKEHA = (*platform.TKEHA)(unsafe.Pointer(in.TKEHA))
	out.ThirdPartyHA = (*platform.ThirdPartyHA)(unsafe.Pointer(in.ThirdPartyHA))
	return nil
}

// Convert_v1_HA_To_platform_HA is an autogenerated conversion function.
func Convert_v1_HA_To_platform_HA(in *HA, out *platform.HA, s conversion.Scope) error {
	return autoConvert_v1_HA_To_platform_HA(in, out, s)
}

func autoConvert_platform_HA_To_v1_HA(in *platform.HA, out *HA, s conversion.Scope) error {
	out.TKEHA = (*TKEHA)(unsafe.Pointer(in.TKEHA))
	out.ThirdPartyHA = (*ThirdPartyHA)(unsafe.Pointer(in.ThirdPartyHA))
	return nil
}

// Convert_platform_HA_To_v1_HA is an autogenerated conversion function.
func Convert_platform_HA_To_v1_HA(in *platform.HA, out *HA, s conversion.Scope) error {
	return autoConvert_platform_HA_To_v1_HA(in, out, s)
}

func autoConvert_v1_LocalEtcd_To_platform_LocalEtcd(in *LocalEtcd, out *platform.LocalEtcd, s conversion.Scope) error {
	out.DataDir = in.DataDir
	out.ExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.ExtraArgs))
	out.ServerCertSANs = *(*[]string)(unsafe.Pointer(&in.ServerCertSANs))
	out.PeerCertSANs = *(*[]string)(unsafe.Pointer(&in.PeerCertSANs))
	return nil
}

// Convert_v1_LocalEtcd_To_platform_LocalEtcd is an autogenerated conversion function.
func Convert_v1_LocalEtcd_To_platform_LocalEtcd(in *LocalEtcd, out *platform.LocalEtcd, s conversion.Scope) error {
	return autoConvert_v1_LocalEtcd_To_platform_LocalEtcd(in, out, s)
}

func autoConvert_platform_LocalEtcd_To_v1_LocalEtcd(in *platform.LocalEtcd, out *LocalEtcd, s conversion.Scope) error {
	out.DataDir = in.DataDir
	out.ExtraArgs = *(*map[string]string)(unsafe.Pointer(&in.ExtraArgs))
	out.ServerCertSANs = *(*[]string)(unsafe.Pointer(&in.ServerCertSANs))
	out.PeerCertSANs = *(*[]string)(unsafe.Pointer(&in.PeerCertSANs))
	return nil
}

// Convert_platform_LocalEtcd_To_v1_LocalEtcd is an autogenerated conversion function.
func Convert_platform_LocalEtcd_To_v1_LocalEtcd(in *platform.LocalEtcd, out *LocalEtcd, s conversion.Scope) error {
	return autoConvert_platform_LocalEtcd_To_v1_LocalEtcd(in, out, s)
}

func autoConvert_v1_Machine_To_platform_Machine(in *Machine, out *platform.Machine, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_MachineSpec_To_platform_MachineSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_MachineStatus_To_platform_MachineStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Machine_To_platform_Machine is an autogenerated conversion function.
func Convert_v1_Machine_To_platform_Machine(in *Machine, out *platform.Machine, s conversion.Scope) error {
	return autoConvert_v1_Machine_To_platform_Machine(in, out, s)
}

func autoConvert_platform_Machine_To_v1_Machine(in *platform.Machine, out *Machine, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_MachineSpec_To_v1_MachineSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_MachineStatus_To_v1_MachineStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Machine_To_v1_Machine is an autogenerated conversion function.
func Convert_platform_Machine_To_v1_Machine(in *platform.Machine, out *Machine, s conversion.Scope) error {
	return autoConvert_platform_Machine_To_v1_Machine(in, out, s)
}

func autoConvert_v1_MachineAddress_To_platform_MachineAddress(in *MachineAddress, out *platform.MachineAddress, s conversion.Scope) error {
	out.Type = platform.MachineAddressType(in.Type)
	out.Address = in.Address
	return nil
}

// Convert_v1_MachineAddress_To_platform_MachineAddress is an autogenerated conversion function.
func Convert_v1_MachineAddress_To_platform_MachineAddress(in *MachineAddress, out *platform.MachineAddress, s conversion.Scope) error {
	return autoConvert_v1_MachineAddress_To_platform_MachineAddress(in, out, s)
}

func autoConvert_platform_MachineAddress_To_v1_MachineAddress(in *platform.MachineAddress, out *MachineAddress, s conversion.Scope) error {
	out.Type = MachineAddressType(in.Type)
	out.Address = in.Address
	return nil
}

// Convert_platform_MachineAddress_To_v1_MachineAddress is an autogenerated conversion function.
func Convert_platform_MachineAddress_To_v1_MachineAddress(in *platform.MachineAddress, out *MachineAddress, s conversion.Scope) error {
	return autoConvert_platform_MachineAddress_To_v1_MachineAddress(in, out, s)
}

func autoConvert_v1_MachineCondition_To_platform_MachineCondition(in *MachineCondition, out *platform.MachineCondition, s conversion.Scope) error {
	out.Type = in.Type
	out.Status = platform.ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_v1_MachineCondition_To_platform_MachineCondition is an autogenerated conversion function.
func Convert_v1_MachineCondition_To_platform_MachineCondition(in *MachineCondition, out *platform.MachineCondition, s conversion.Scope) error {
	return autoConvert_v1_MachineCondition_To_platform_MachineCondition(in, out, s)
}

func autoConvert_platform_MachineCondition_To_v1_MachineCondition(in *platform.MachineCondition, out *MachineCondition, s conversion.Scope) error {
	out.Type = in.Type
	out.Status = ConditionStatus(in.Status)
	out.LastProbeTime = in.LastProbeTime
	out.LastTransitionTime = in.LastTransitionTime
	out.Reason = in.Reason
	out.Message = in.Message
	return nil
}

// Convert_platform_MachineCondition_To_v1_MachineCondition is an autogenerated conversion function.
func Convert_platform_MachineCondition_To_v1_MachineCondition(in *platform.MachineCondition, out *MachineCondition, s conversion.Scope) error {
	return autoConvert_platform_MachineCondition_To_v1_MachineCondition(in, out, s)
}

func autoConvert_v1_MachineList_To_platform_MachineList(in *MachineList, out *platform.MachineList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.Machine)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_MachineList_To_platform_MachineList is an autogenerated conversion function.
func Convert_v1_MachineList_To_platform_MachineList(in *MachineList, out *platform.MachineList, s conversion.Scope) error {
	return autoConvert_v1_MachineList_To_platform_MachineList(in, out, s)
}

func autoConvert_platform_MachineList_To_v1_MachineList(in *platform.MachineList, out *MachineList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]Machine)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_MachineList_To_v1_MachineList is an autogenerated conversion function.
func Convert_platform_MachineList_To_v1_MachineList(in *platform.MachineList, out *MachineList, s conversion.Scope) error {
	return autoConvert_platform_MachineList_To_v1_MachineList(in, out, s)
}

func autoConvert_v1_MachineSpec_To_platform_MachineSpec(in *MachineSpec, out *platform.MachineSpec, s conversion.Scope) error {
	out.Finalizers = *(*[]platform.FinalizerName)(unsafe.Pointer(&in.Finalizers))
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Type = in.Type
	out.IP = in.IP
	out.Port = in.Port
	out.Username = in.Username
	out.Password = *(*[]byte)(unsafe.Pointer(&in.Password))
	out.PrivateKey = *(*[]byte)(unsafe.Pointer(&in.PrivateKey))
	out.PassPhrase = *(*[]byte)(unsafe.Pointer(&in.PassPhrase))
	out.Labels = *(*map[string]string)(unsafe.Pointer(&in.Labels))
	out.Taints = *(*[]corev1.Taint)(unsafe.Pointer(&in.Taints))
	return nil
}

// Convert_v1_MachineSpec_To_platform_MachineSpec is an autogenerated conversion function.
func Convert_v1_MachineSpec_To_platform_MachineSpec(in *MachineSpec, out *platform.MachineSpec, s conversion.Scope) error {
	return autoConvert_v1_MachineSpec_To_platform_MachineSpec(in, out, s)
}

func autoConvert_platform_MachineSpec_To_v1_MachineSpec(in *platform.MachineSpec, out *MachineSpec, s conversion.Scope) error {
	out.Finalizers = *(*[]FinalizerName)(unsafe.Pointer(&in.Finalizers))
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Type = in.Type
	out.IP = in.IP
	out.Port = in.Port
	out.Username = in.Username
	out.Password = *(*[]byte)(unsafe.Pointer(&in.Password))
	out.PrivateKey = *(*[]byte)(unsafe.Pointer(&in.PrivateKey))
	out.PassPhrase = *(*[]byte)(unsafe.Pointer(&in.PassPhrase))
	out.Labels = *(*map[string]string)(unsafe.Pointer(&in.Labels))
	out.Taints = *(*[]corev1.Taint)(unsafe.Pointer(&in.Taints))
	return nil
}

// Convert_platform_MachineSpec_To_v1_MachineSpec is an autogenerated conversion function.
func Convert_platform_MachineSpec_To_v1_MachineSpec(in *platform.MachineSpec, out *MachineSpec, s conversion.Scope) error {
	return autoConvert_platform_MachineSpec_To_v1_MachineSpec(in, out, s)
}

func autoConvert_v1_MachineStatus_To_platform_MachineStatus(in *MachineStatus, out *platform.MachineStatus, s conversion.Scope) error {
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	out.Phase = platform.MachinePhase(in.Phase)
	out.Conditions = *(*[]platform.MachineCondition)(unsafe.Pointer(&in.Conditions))
	out.Message = in.Message
	out.Reason = in.Reason
	out.Addresses = *(*[]platform.MachineAddress)(unsafe.Pointer(&in.Addresses))
	if err := Convert_v1_MachineSystemInfo_To_platform_MachineSystemInfo(&in.MachineInfo, &out.MachineInfo, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_MachineStatus_To_platform_MachineStatus is an autogenerated conversion function.
func Convert_v1_MachineStatus_To_platform_MachineStatus(in *MachineStatus, out *platform.MachineStatus, s conversion.Scope) error {
	return autoConvert_v1_MachineStatus_To_platform_MachineStatus(in, out, s)
}

func autoConvert_platform_MachineStatus_To_v1_MachineStatus(in *platform.MachineStatus, out *MachineStatus, s conversion.Scope) error {
	out.Locked = (*bool)(unsafe.Pointer(in.Locked))
	out.Phase = MachinePhase(in.Phase)
	out.Conditions = *(*[]MachineCondition)(unsafe.Pointer(&in.Conditions))
	out.Message = in.Message
	out.Reason = in.Reason
	out.Addresses = *(*[]MachineAddress)(unsafe.Pointer(&in.Addresses))
	if err := Convert_platform_MachineSystemInfo_To_v1_MachineSystemInfo(&in.MachineInfo, &out.MachineInfo, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_MachineStatus_To_v1_MachineStatus is an autogenerated conversion function.
func Convert_platform_MachineStatus_To_v1_MachineStatus(in *platform.MachineStatus, out *MachineStatus, s conversion.Scope) error {
	return autoConvert_platform_MachineStatus_To_v1_MachineStatus(in, out, s)
}

func autoConvert_v1_MachineSystemInfo_To_platform_MachineSystemInfo(in *MachineSystemInfo, out *platform.MachineSystemInfo, s conversion.Scope) error {
	out.MachineID = in.MachineID
	out.SystemUUID = in.SystemUUID
	out.BootID = in.BootID
	out.KernelVersion = in.KernelVersion
	out.OSImage = in.OSImage
	out.ContainerRuntimeVersion = in.ContainerRuntimeVersion
	out.KubeletVersion = in.KubeletVersion
	out.KubeProxyVersion = in.KubeProxyVersion
	out.OperatingSystem = in.OperatingSystem
	out.Architecture = in.Architecture
	return nil
}

// Convert_v1_MachineSystemInfo_To_platform_MachineSystemInfo is an autogenerated conversion function.
func Convert_v1_MachineSystemInfo_To_platform_MachineSystemInfo(in *MachineSystemInfo, out *platform.MachineSystemInfo, s conversion.Scope) error {
	return autoConvert_v1_MachineSystemInfo_To_platform_MachineSystemInfo(in, out, s)
}

func autoConvert_platform_MachineSystemInfo_To_v1_MachineSystemInfo(in *platform.MachineSystemInfo, out *MachineSystemInfo, s conversion.Scope) error {
	out.MachineID = in.MachineID
	out.SystemUUID = in.SystemUUID
	out.BootID = in.BootID
	out.KernelVersion = in.KernelVersion
	out.OSImage = in.OSImage
	out.ContainerRuntimeVersion = in.ContainerRuntimeVersion
	out.KubeletVersion = in.KubeletVersion
	out.KubeProxyVersion = in.KubeProxyVersion
	out.OperatingSystem = in.OperatingSystem
	out.Architecture = in.Architecture
	return nil
}

// Convert_platform_MachineSystemInfo_To_v1_MachineSystemInfo is an autogenerated conversion function.
func Convert_platform_MachineSystemInfo_To_v1_MachineSystemInfo(in *platform.MachineSystemInfo, out *MachineSystemInfo, s conversion.Scope) error {
	return autoConvert_platform_MachineSystemInfo_To_v1_MachineSystemInfo(in, out, s)
}

func autoConvert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(in *PersistentBackEnd, out *platform.PersistentBackEnd, s conversion.Scope) error {
	out.CLS = (*platform.StorageBackEndCLS)(unsafe.Pointer(in.CLS))
	out.ES = (*platform.StorageBackEndES)(unsafe.Pointer(in.ES))
	return nil
}

// Convert_v1_PersistentBackEnd_To_platform_PersistentBackEnd is an autogenerated conversion function.
func Convert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(in *PersistentBackEnd, out *platform.PersistentBackEnd, s conversion.Scope) error {
	return autoConvert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(in, out, s)
}

func autoConvert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(in *platform.PersistentBackEnd, out *PersistentBackEnd, s conversion.Scope) error {
	out.CLS = (*StorageBackEndCLS)(unsafe.Pointer(in.CLS))
	out.ES = (*StorageBackEndES)(unsafe.Pointer(in.ES))
	return nil
}

// Convert_platform_PersistentBackEnd_To_v1_PersistentBackEnd is an autogenerated conversion function.
func Convert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(in *platform.PersistentBackEnd, out *PersistentBackEnd, s conversion.Scope) error {
	return autoConvert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(in, out, s)
}

func autoConvert_v1_PersistentEvent_To_platform_PersistentEvent(in *PersistentEvent, out *platform.PersistentEvent, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_PersistentEvent_To_platform_PersistentEvent is an autogenerated conversion function.
func Convert_v1_PersistentEvent_To_platform_PersistentEvent(in *PersistentEvent, out *platform.PersistentEvent, s conversion.Scope) error {
	return autoConvert_v1_PersistentEvent_To_platform_PersistentEvent(in, out, s)
}

func autoConvert_platform_PersistentEvent_To_v1_PersistentEvent(in *platform.PersistentEvent, out *PersistentEvent, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_PersistentEvent_To_v1_PersistentEvent is an autogenerated conversion function.
func Convert_platform_PersistentEvent_To_v1_PersistentEvent(in *platform.PersistentEvent, out *PersistentEvent, s conversion.Scope) error {
	return autoConvert_platform_PersistentEvent_To_v1_PersistentEvent(in, out, s)
}

func autoConvert_v1_PersistentEventList_To_platform_PersistentEventList(in *PersistentEventList, out *platform.PersistentEventList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.PersistentEvent)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_PersistentEventList_To_platform_PersistentEventList is an autogenerated conversion function.
func Convert_v1_PersistentEventList_To_platform_PersistentEventList(in *PersistentEventList, out *platform.PersistentEventList, s conversion.Scope) error {
	return autoConvert_v1_PersistentEventList_To_platform_PersistentEventList(in, out, s)
}

func autoConvert_platform_PersistentEventList_To_v1_PersistentEventList(in *platform.PersistentEventList, out *PersistentEventList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]PersistentEvent)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_PersistentEventList_To_v1_PersistentEventList is an autogenerated conversion function.
func Convert_platform_PersistentEventList_To_v1_PersistentEventList(in *platform.PersistentEventList, out *PersistentEventList, s conversion.Scope) error {
	return autoConvert_platform_PersistentEventList_To_v1_PersistentEventList(in, out, s)
}

func autoConvert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(in *PersistentEventSpec, out *platform.PersistentEventSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	if err := Convert_v1_PersistentBackEnd_To_platform_PersistentBackEnd(&in.PersistentBackEnd, &out.PersistentBackEnd, s); err != nil {
		return err
	}
	out.Version = in.Version
	return nil
}

// Convert_v1_PersistentEventSpec_To_platform_PersistentEventSpec is an autogenerated conversion function.
func Convert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(in *PersistentEventSpec, out *platform.PersistentEventSpec, s conversion.Scope) error {
	return autoConvert_v1_PersistentEventSpec_To_platform_PersistentEventSpec(in, out, s)
}

func autoConvert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(in *platform.PersistentEventSpec, out *PersistentEventSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	if err := Convert_platform_PersistentBackEnd_To_v1_PersistentBackEnd(&in.PersistentBackEnd, &out.PersistentBackEnd, s); err != nil {
		return err
	}
	out.Version = in.Version
	return nil
}

// Convert_platform_PersistentEventSpec_To_v1_PersistentEventSpec is an autogenerated conversion function.
func Convert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(in *platform.PersistentEventSpec, out *PersistentEventSpec, s conversion.Scope) error {
	return autoConvert_platform_PersistentEventSpec_To_v1_PersistentEventSpec(in, out, s)
}

func autoConvert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(in *PersistentEventStatus, out *platform.PersistentEventStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_PersistentEventStatus_To_platform_PersistentEventStatus is an autogenerated conversion function.
func Convert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(in *PersistentEventStatus, out *platform.PersistentEventStatus, s conversion.Scope) error {
	return autoConvert_v1_PersistentEventStatus_To_platform_PersistentEventStatus(in, out, s)
}

func autoConvert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(in *platform.PersistentEventStatus, out *PersistentEventStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_PersistentEventStatus_To_v1_PersistentEventStatus is an autogenerated conversion function.
func Convert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(in *platform.PersistentEventStatus, out *PersistentEventStatus, s conversion.Scope) error {
	return autoConvert_platform_PersistentEventStatus_To_v1_PersistentEventStatus(in, out, s)
}

func autoConvert_v1_ProxyOptions_To_platform_ProxyOptions(in *ProxyOptions, out *platform.ProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_v1_ProxyOptions_To_platform_ProxyOptions is an autogenerated conversion function.
func Convert_v1_ProxyOptions_To_platform_ProxyOptions(in *ProxyOptions, out *platform.ProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_ProxyOptions_To_platform_ProxyOptions(in, out, s)
}

func autoConvert_platform_ProxyOptions_To_v1_ProxyOptions(in *platform.ProxyOptions, out *ProxyOptions, s conversion.Scope) error {
	out.Path = in.Path
	return nil
}

// Convert_platform_ProxyOptions_To_v1_ProxyOptions is an autogenerated conversion function.
func Convert_platform_ProxyOptions_To_v1_ProxyOptions(in *platform.ProxyOptions, out *ProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_ProxyOptions_To_v1_ProxyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_ProxyOptions(in *url.Values, out *ProxyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["path"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Path, s); err != nil {
			return err
		}
	} else {
		out.Path = ""
	}
	return nil
}

// Convert_url_Values_To_v1_ProxyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_ProxyOptions(in *url.Values, out *ProxyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_ProxyOptions(in, out, s)
}

func autoConvert_v1_Registry_To_platform_Registry(in *Registry, out *platform.Registry, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_RegistrySpec_To_platform_RegistrySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Registry_To_platform_Registry is an autogenerated conversion function.
func Convert_v1_Registry_To_platform_Registry(in *Registry, out *platform.Registry, s conversion.Scope) error {
	return autoConvert_v1_Registry_To_platform_Registry(in, out, s)
}

func autoConvert_platform_Registry_To_v1_Registry(in *platform.Registry, out *Registry, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_RegistrySpec_To_v1_RegistrySpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Registry_To_v1_Registry is an autogenerated conversion function.
func Convert_platform_Registry_To_v1_Registry(in *platform.Registry, out *Registry, s conversion.Scope) error {
	return autoConvert_platform_Registry_To_v1_Registry(in, out, s)
}

func autoConvert_v1_RegistryList_To_platform_RegistryList(in *RegistryList, out *platform.RegistryList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.Registry)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_RegistryList_To_platform_RegistryList is an autogenerated conversion function.
func Convert_v1_RegistryList_To_platform_RegistryList(in *RegistryList, out *platform.RegistryList, s conversion.Scope) error {
	return autoConvert_v1_RegistryList_To_platform_RegistryList(in, out, s)
}

func autoConvert_platform_RegistryList_To_v1_RegistryList(in *platform.RegistryList, out *RegistryList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]Registry)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_RegistryList_To_v1_RegistryList is an autogenerated conversion function.
func Convert_platform_RegistryList_To_v1_RegistryList(in *platform.RegistryList, out *RegistryList, s conversion.Scope) error {
	return autoConvert_platform_RegistryList_To_v1_RegistryList(in, out, s)
}

func autoConvert_v1_RegistrySpec_To_platform_RegistrySpec(in *RegistrySpec, out *platform.RegistrySpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.ClusterName = in.ClusterName
	out.URL = in.URL
	out.UserName = (*string)(unsafe.Pointer(in.UserName))
	out.Password = (*string)(unsafe.Pointer(in.Password))
	return nil
}

// Convert_v1_RegistrySpec_To_platform_RegistrySpec is an autogenerated conversion function.
func Convert_v1_RegistrySpec_To_platform_RegistrySpec(in *RegistrySpec, out *platform.RegistrySpec, s conversion.Scope) error {
	return autoConvert_v1_RegistrySpec_To_platform_RegistrySpec(in, out, s)
}

func autoConvert_platform_RegistrySpec_To_v1_RegistrySpec(in *platform.RegistrySpec, out *RegistrySpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.DisplayName = in.DisplayName
	out.ClusterName = in.ClusterName
	out.URL = in.URL
	out.UserName = (*string)(unsafe.Pointer(in.UserName))
	out.Password = (*string)(unsafe.Pointer(in.Password))
	return nil
}

// Convert_platform_RegistrySpec_To_v1_RegistrySpec is an autogenerated conversion function.
func Convert_platform_RegistrySpec_To_v1_RegistrySpec(in *platform.RegistrySpec, out *RegistrySpec, s conversion.Scope) error {
	return autoConvert_platform_RegistrySpec_To_v1_RegistrySpec(in, out, s)
}

func autoConvert_v1_ResourceRequirements_To_platform_ResourceRequirements(in *ResourceRequirements, out *platform.ResourceRequirements, s conversion.Scope) error {
	out.Limits = *(*platform.ResourceList)(unsafe.Pointer(&in.Limits))
	out.Requests = *(*platform.ResourceList)(unsafe.Pointer(&in.Requests))
	return nil
}

// Convert_v1_ResourceRequirements_To_platform_ResourceRequirements is an autogenerated conversion function.
func Convert_v1_ResourceRequirements_To_platform_ResourceRequirements(in *ResourceRequirements, out *platform.ResourceRequirements, s conversion.Scope) error {
	return autoConvert_v1_ResourceRequirements_To_platform_ResourceRequirements(in, out, s)
}

func autoConvert_platform_ResourceRequirements_To_v1_ResourceRequirements(in *platform.ResourceRequirements, out *ResourceRequirements, s conversion.Scope) error {
	out.Limits = *(*ResourceList)(unsafe.Pointer(&in.Limits))
	out.Requests = *(*ResourceList)(unsafe.Pointer(&in.Requests))
	return nil
}

// Convert_platform_ResourceRequirements_To_v1_ResourceRequirements is an autogenerated conversion function.
func Convert_platform_ResourceRequirements_To_v1_ResourceRequirements(in *platform.ResourceRequirements, out *ResourceRequirements, s conversion.Scope) error {
	return autoConvert_platform_ResourceRequirements_To_v1_ResourceRequirements(in, out, s)
}

func autoConvert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS(in *StorageBackEndCLS, out *platform.StorageBackEndCLS, s conversion.Scope) error {
	out.LogSetID = in.LogSetID
	out.TopicID = in.TopicID
	return nil
}

// Convert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS is an autogenerated conversion function.
func Convert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS(in *StorageBackEndCLS, out *platform.StorageBackEndCLS, s conversion.Scope) error {
	return autoConvert_v1_StorageBackEndCLS_To_platform_StorageBackEndCLS(in, out, s)
}

func autoConvert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS(in *platform.StorageBackEndCLS, out *StorageBackEndCLS, s conversion.Scope) error {
	out.LogSetID = in.LogSetID
	out.TopicID = in.TopicID
	return nil
}

// Convert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS is an autogenerated conversion function.
func Convert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS(in *platform.StorageBackEndCLS, out *StorageBackEndCLS, s conversion.Scope) error {
	return autoConvert_platform_StorageBackEndCLS_To_v1_StorageBackEndCLS(in, out, s)
}

func autoConvert_v1_StorageBackEndES_To_platform_StorageBackEndES(in *StorageBackEndES, out *platform.StorageBackEndES, s conversion.Scope) error {
	out.IP = in.IP
	out.Port = in.Port
	out.Scheme = in.Scheme
	out.IndexName = in.IndexName
	out.User = in.User
	out.Password = in.Password
	out.ReserveDays = in.ReserveDays
	return nil
}

// Convert_v1_StorageBackEndES_To_platform_StorageBackEndES is an autogenerated conversion function.
func Convert_v1_StorageBackEndES_To_platform_StorageBackEndES(in *StorageBackEndES, out *platform.StorageBackEndES, s conversion.Scope) error {
	return autoConvert_v1_StorageBackEndES_To_platform_StorageBackEndES(in, out, s)
}

func autoConvert_platform_StorageBackEndES_To_v1_StorageBackEndES(in *platform.StorageBackEndES, out *StorageBackEndES, s conversion.Scope) error {
	out.IP = in.IP
	out.Port = in.Port
	out.Scheme = in.Scheme
	out.IndexName = in.IndexName
	out.User = in.User
	out.Password = in.Password
	out.ReserveDays = in.ReserveDays
	return nil
}

// Convert_platform_StorageBackEndES_To_v1_StorageBackEndES is an autogenerated conversion function.
func Convert_platform_StorageBackEndES_To_v1_StorageBackEndES(in *platform.StorageBackEndES, out *StorageBackEndES, s conversion.Scope) error {
	return autoConvert_platform_StorageBackEndES_To_v1_StorageBackEndES(in, out, s)
}

func autoConvert_v1_TKEHA_To_platform_TKEHA(in *TKEHA, out *platform.TKEHA, s conversion.Scope) error {
	out.VIP = in.VIP
	out.VRID = (*int32)(unsafe.Pointer(in.VRID))
	return nil
}

// Convert_v1_TKEHA_To_platform_TKEHA is an autogenerated conversion function.
func Convert_v1_TKEHA_To_platform_TKEHA(in *TKEHA, out *platform.TKEHA, s conversion.Scope) error {
	return autoConvert_v1_TKEHA_To_platform_TKEHA(in, out, s)
}

func autoConvert_platform_TKEHA_To_v1_TKEHA(in *platform.TKEHA, out *TKEHA, s conversion.Scope) error {
	out.VIP = in.VIP
	out.VRID = (*int32)(unsafe.Pointer(in.VRID))
	return nil
}

// Convert_platform_TKEHA_To_v1_TKEHA is an autogenerated conversion function.
func Convert_platform_TKEHA_To_v1_TKEHA(in *platform.TKEHA, out *TKEHA, s conversion.Scope) error {
	return autoConvert_platform_TKEHA_To_v1_TKEHA(in, out, s)
}

func autoConvert_v1_TappController_To_platform_TappController(in *TappController, out *platform.TappController, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_v1_TappControllerSpec_To_platform_TappControllerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_v1_TappControllerStatus_To_platform_TappControllerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_TappController_To_platform_TappController is an autogenerated conversion function.
func Convert_v1_TappController_To_platform_TappController(in *TappController, out *platform.TappController, s conversion.Scope) error {
	return autoConvert_v1_TappController_To_platform_TappController(in, out, s)
}

func autoConvert_platform_TappController_To_v1_TappController(in *platform.TappController, out *TappController, s conversion.Scope) error {
	out.ObjectMeta = in.ObjectMeta
	if err := Convert_platform_TappControllerSpec_To_v1_TappControllerSpec(&in.Spec, &out.Spec, s); err != nil {
		return err
	}
	if err := Convert_platform_TappControllerStatus_To_v1_TappControllerStatus(&in.Status, &out.Status, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_TappController_To_v1_TappController is an autogenerated conversion function.
func Convert_platform_TappController_To_v1_TappController(in *platform.TappController, out *TappController, s conversion.Scope) error {
	return autoConvert_platform_TappController_To_v1_TappController(in, out, s)
}

func autoConvert_v1_TappControllerList_To_platform_TappControllerList(in *TappControllerList, out *platform.TappControllerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]platform.TappController)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_v1_TappControllerList_To_platform_TappControllerList is an autogenerated conversion function.
func Convert_v1_TappControllerList_To_platform_TappControllerList(in *TappControllerList, out *platform.TappControllerList, s conversion.Scope) error {
	return autoConvert_v1_TappControllerList_To_platform_TappControllerList(in, out, s)
}

func autoConvert_platform_TappControllerList_To_v1_TappControllerList(in *platform.TappControllerList, out *TappControllerList, s conversion.Scope) error {
	out.ListMeta = in.ListMeta
	out.Items = *(*[]TappController)(unsafe.Pointer(&in.Items))
	return nil
}

// Convert_platform_TappControllerList_To_v1_TappControllerList is an autogenerated conversion function.
func Convert_platform_TappControllerList_To_v1_TappControllerList(in *platform.TappControllerList, out *TappControllerList, s conversion.Scope) error {
	return autoConvert_platform_TappControllerList_To_v1_TappControllerList(in, out, s)
}

func autoConvert_v1_TappControllerProxyOptions_To_platform_TappControllerProxyOptions(in *TappControllerProxyOptions, out *platform.TappControllerProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_v1_TappControllerProxyOptions_To_platform_TappControllerProxyOptions is an autogenerated conversion function.
func Convert_v1_TappControllerProxyOptions_To_platform_TappControllerProxyOptions(in *TappControllerProxyOptions, out *platform.TappControllerProxyOptions, s conversion.Scope) error {
	return autoConvert_v1_TappControllerProxyOptions_To_platform_TappControllerProxyOptions(in, out, s)
}

func autoConvert_platform_TappControllerProxyOptions_To_v1_TappControllerProxyOptions(in *platform.TappControllerProxyOptions, out *TappControllerProxyOptions, s conversion.Scope) error {
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Action = in.Action
	return nil
}

// Convert_platform_TappControllerProxyOptions_To_v1_TappControllerProxyOptions is an autogenerated conversion function.
func Convert_platform_TappControllerProxyOptions_To_v1_TappControllerProxyOptions(in *platform.TappControllerProxyOptions, out *TappControllerProxyOptions, s conversion.Scope) error {
	return autoConvert_platform_TappControllerProxyOptions_To_v1_TappControllerProxyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_TappControllerProxyOptions(in *url.Values, out *TappControllerProxyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["namespace"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Namespace, s); err != nil {
			return err
		}
	} else {
		out.Namespace = ""
	}
	if values, ok := map[string][]string(*in)["name"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Name, s); err != nil {
			return err
		}
	} else {
		out.Name = ""
	}
	if values, ok := map[string][]string(*in)["action"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Action, s); err != nil {
			return err
		}
	} else {
		out.Action = ""
	}
	return nil
}

// Convert_url_Values_To_v1_TappControllerProxyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_TappControllerProxyOptions(in *url.Values, out *TappControllerProxyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_TappControllerProxyOptions(in, out, s)
}

func autoConvert_v1_TappControllerSpec_To_platform_TappControllerSpec(in *TappControllerSpec, out *platform.TappControllerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_v1_TappControllerSpec_To_platform_TappControllerSpec is an autogenerated conversion function.
func Convert_v1_TappControllerSpec_To_platform_TappControllerSpec(in *TappControllerSpec, out *platform.TappControllerSpec, s conversion.Scope) error {
	return autoConvert_v1_TappControllerSpec_To_platform_TappControllerSpec(in, out, s)
}

func autoConvert_platform_TappControllerSpec_To_v1_TappControllerSpec(in *platform.TappControllerSpec, out *TappControllerSpec, s conversion.Scope) error {
	out.TenantID = in.TenantID
	out.ClusterName = in.ClusterName
	out.Version = in.Version
	return nil
}

// Convert_platform_TappControllerSpec_To_v1_TappControllerSpec is an autogenerated conversion function.
func Convert_platform_TappControllerSpec_To_v1_TappControllerSpec(in *platform.TappControllerSpec, out *TappControllerSpec, s conversion.Scope) error {
	return autoConvert_platform_TappControllerSpec_To_v1_TappControllerSpec(in, out, s)
}

func autoConvert_v1_TappControllerStatus_To_platform_TappControllerStatus(in *TappControllerStatus, out *platform.TappControllerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = platform.AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_v1_TappControllerStatus_To_platform_TappControllerStatus is an autogenerated conversion function.
func Convert_v1_TappControllerStatus_To_platform_TappControllerStatus(in *TappControllerStatus, out *platform.TappControllerStatus, s conversion.Scope) error {
	return autoConvert_v1_TappControllerStatus_To_platform_TappControllerStatus(in, out, s)
}

func autoConvert_platform_TappControllerStatus_To_v1_TappControllerStatus(in *platform.TappControllerStatus, out *TappControllerStatus, s conversion.Scope) error {
	out.Version = in.Version
	out.Phase = AddonPhase(in.Phase)
	out.Reason = in.Reason
	out.RetryCount = in.RetryCount
	out.LastReInitializingTimestamp = in.LastReInitializingTimestamp
	return nil
}

// Convert_platform_TappControllerStatus_To_v1_TappControllerStatus is an autogenerated conversion function.
func Convert_platform_TappControllerStatus_To_v1_TappControllerStatus(in *platform.TappControllerStatus, out *TappControllerStatus, s conversion.Scope) error {
	return autoConvert_platform_TappControllerStatus_To_v1_TappControllerStatus(in, out, s)
}

func autoConvert_v1_ThirdPartyHA_To_platform_ThirdPartyHA(in *ThirdPartyHA, out *platform.ThirdPartyHA, s conversion.Scope) error {
	out.VIP = in.VIP
	out.VPort = in.VPort
	return nil
}

// Convert_v1_ThirdPartyHA_To_platform_ThirdPartyHA is an autogenerated conversion function.
func Convert_v1_ThirdPartyHA_To_platform_ThirdPartyHA(in *ThirdPartyHA, out *platform.ThirdPartyHA, s conversion.Scope) error {
	return autoConvert_v1_ThirdPartyHA_To_platform_ThirdPartyHA(in, out, s)
}

func autoConvert_platform_ThirdPartyHA_To_v1_ThirdPartyHA(in *platform.ThirdPartyHA, out *ThirdPartyHA, s conversion.Scope) error {
	out.VIP = in.VIP
	out.VPort = in.VPort
	return nil
}

// Convert_platform_ThirdPartyHA_To_v1_ThirdPartyHA is an autogenerated conversion function.
func Convert_platform_ThirdPartyHA_To_v1_ThirdPartyHA(in *platform.ThirdPartyHA, out *ThirdPartyHA, s conversion.Scope) error {
	return autoConvert_platform_ThirdPartyHA_To_v1_ThirdPartyHA(in, out, s)
}

func autoConvert_v1_Upgrade_To_platform_Upgrade(in *Upgrade, out *platform.Upgrade, s conversion.Scope) error {
	out.Mode = platform.UpgradeMode(in.Mode)
	if err := Convert_v1_UpgradeStrategy_To_platform_UpgradeStrategy(&in.Strategy, &out.Strategy, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_Upgrade_To_platform_Upgrade is an autogenerated conversion function.
func Convert_v1_Upgrade_To_platform_Upgrade(in *Upgrade, out *platform.Upgrade, s conversion.Scope) error {
	return autoConvert_v1_Upgrade_To_platform_Upgrade(in, out, s)
}

func autoConvert_platform_Upgrade_To_v1_Upgrade(in *platform.Upgrade, out *Upgrade, s conversion.Scope) error {
	out.Mode = UpgradeMode(in.Mode)
	if err := Convert_platform_UpgradeStrategy_To_v1_UpgradeStrategy(&in.Strategy, &out.Strategy, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_Upgrade_To_v1_Upgrade is an autogenerated conversion function.
func Convert_platform_Upgrade_To_v1_Upgrade(in *platform.Upgrade, out *Upgrade, s conversion.Scope) error {
	return autoConvert_platform_Upgrade_To_v1_Upgrade(in, out, s)
}

func autoConvert_v1_UpgradeStrategy_To_platform_UpgradeStrategy(in *UpgradeStrategy, out *platform.UpgradeStrategy, s conversion.Scope) error {
	if err := metav1.Convert_Pointer_intstr_IntOrString_To_intstr_IntOrString(&in.MaxUnready, &out.MaxUnready, s); err != nil {
		return err
	}
	if err := metav1.Convert_Pointer_bool_To_bool(&in.DrainNodeBeforeUpgrade, &out.DrainNodeBeforeUpgrade, s); err != nil {
		return err
	}
	return nil
}

// Convert_v1_UpgradeStrategy_To_platform_UpgradeStrategy is an autogenerated conversion function.
func Convert_v1_UpgradeStrategy_To_platform_UpgradeStrategy(in *UpgradeStrategy, out *platform.UpgradeStrategy, s conversion.Scope) error {
	return autoConvert_v1_UpgradeStrategy_To_platform_UpgradeStrategy(in, out, s)
}

func autoConvert_platform_UpgradeStrategy_To_v1_UpgradeStrategy(in *platform.UpgradeStrategy, out *UpgradeStrategy, s conversion.Scope) error {
	if err := metav1.Convert_intstr_IntOrString_To_Pointer_intstr_IntOrString(&in.MaxUnready, &out.MaxUnready, s); err != nil {
		return err
	}
	if err := metav1.Convert_bool_To_Pointer_bool(&in.DrainNodeBeforeUpgrade, &out.DrainNodeBeforeUpgrade, s); err != nil {
		return err
	}
	return nil
}

// Convert_platform_UpgradeStrategy_To_v1_UpgradeStrategy is an autogenerated conversion function.
func Convert_platform_UpgradeStrategy_To_v1_UpgradeStrategy(in *platform.UpgradeStrategy, out *UpgradeStrategy, s conversion.Scope) error {
	return autoConvert_platform_UpgradeStrategy_To_v1_UpgradeStrategy(in, out, s)
}
