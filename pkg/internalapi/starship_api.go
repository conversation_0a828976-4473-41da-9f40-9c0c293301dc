package internalapi

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/http_util"
	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/util"

	"github.com/google/uuid"
	"k8s.io/klog/v2"
)

const (
	TKE_CLOUD_GW_URL = "http://%s.tke.caas.tencentyun.com/tke-cloud-gw"
)

var apiExtenderInfo = struct {
	APIModule     string
	Region        string
	Version       string
	RequestSource string
	Language      string
}{
	APIModule:     "API",
	Version:       "2018-05-25",
	RequestSource: "starship-engine",
	Language:      "zh-CN",
}

func UpdateApp(cluster *db.TaskClusterInfo) (string, error) {
	updateAppReq, err := buildUpdateReq(cluster)
	if err != nil {
		return "", err
	}
	shortRg := util.GetShortRegion(cluster.Region)

	body, err := json.Marshal(updateAppReq)
	if err != nil {
		return "", err
	}
	// UpdateApp是非幂等操作，不能重试，但增加超时时间到30秒
	respBody, _, err := http_util.PostRequestNoRetry(fmt.Sprintf(TKE_CLOUD_GW_URL, shortRg), body, nil, 120*time.Second)
	if err != nil {
		return "", err
	}

	klog.Infof("UpdateApp response: %s", string(respBody))
	var respBodyObj UpdateAppResponse
	if err = json.Unmarshal(respBody, &respBodyObj); err != nil {
		return "", err
	}

	if respBodyObj.Response.Error.Message != "" {
		return "", fmt.Errorf(respBodyObj.Response.Error.Message)
	}
	// addon 模式update时，没有taskId
	//if cluster.AppVersion != "" {
	//	return "", nil
	//}
	//if cluster.ImageTag != "" && respBodyObj.Response.TaskId == "" {
	//	return "", fmt.Errorf(respBodyObj.Response.Error.Message)
	//}
	if cluster.Component == consts.APPFABRIC && respBodyObj.Response.TaskId == "" {
		return "", fmt.Errorf(respBodyObj.Response.Error.Message)
	}
	return respBodyObj.Response.TaskId, nil
}

func RollbackApp(cluster *db.TaskClusterInfo) (string, error) {
	rollbackAppReq, err := buildRollbackReq(cluster)
	if err != nil {
		return "", err
	}
	shortRg := util.GetShortRegion(cluster.Region)

	body, err := json.Marshal(rollbackAppReq)
	if err != nil {
		return "", err
	}

	klog.Infof("RollbackApp request: %s", string(body))

	// RollbackApp是非幂等操作，不能重试，但增加超时时间到30秒
	respBody, _, err := http_util.PostRequestNoRetry(fmt.Sprintf(TKE_CLOUD_GW_URL, shortRg), body, nil, 120*time.Second)
	if err != nil {
		return "", err
	}

	klog.Infof("RollbackApp response: %s", string(respBody))
	var respBodyObj UpdateAppResponse // 回滚使用相同的响应结构
	if err = json.Unmarshal(respBody, &respBodyObj); err != nil {
		return "", err
	}

	if respBodyObj.Response.Error.Message != "" {
		return "", fmt.Errorf(respBodyObj.Response.Error.Message)
	}

	return respBodyObj.Response.TaskId, nil
}

func buildUpdateReq(cluster *db.TaskClusterInfo) (UpdateAppReq, error) {
	appAction := ""
	if cluster.AppAction != "publish" {
		appAction = cluster.AppAction
	}
	cluster.StarshipChangeId = cluster.ChangeId + "-" + strconv.FormatInt(time.Now().Unix(), 10)
	extendInfo := cluster.ExtendInfo
	var err error
	if cluster.Component == consts.APPFABRIC || cluster.Component == consts.EKS_POD ||
		cluster.Component == consts.TCR {
		extendInfo, err = buildExtendInfo(extendInfo, cluster)
		if err != nil {
			return UpdateAppReq{}, err
		}
		randomString := util.GenerateRandomString(8)
		cluster.StarshipChangeId = cluster.ChangeId + randomString + "-" + strconv.FormatInt(time.Now().Unix(), 10)
	}

	if cluster.Component == consts.EKLET_AGENT {
		extendInfo, err = buildEkletAgentExtendInfo(extendInfo, cluster)
		if err != nil {
			return UpdateAppReq{}, err
		}
		randomString := util.GenerateRandomString(8)
		cluster.StarshipChangeId = cluster.ChangeId + randomString + "-" + strconv.FormatInt(time.Now().Unix(), 10)
	}

	// 针对ekspod, 写个假的imageTag，防止gw校验报错
	if cluster.Component == consts.EKS_POD || cluster.Component == consts.TCR {
		cluster.ImageTag = "fake"
	}
	var appStrategy string
	if cluster.Component == consts.EKLET {
		if cluster.ImageTag != "" {
			appStrategy = "ekseklet"
		} else if cluster.AppVersion != "" {
			appStrategy = "addon"
		} else {
			appStrategy = "ekseklet"
		}
	}

	// 解析extendInfo中的RawValues并放入Values.Addon中
	addon := make(map[string]interface{})
	if extendInfo != "" {
		var extendInfoStruct model.ExtendInfo
		if err := json.Unmarshal([]byte(extendInfo), &extendInfoStruct); err == nil {
			if extendInfoStruct.RawValues != "" {
				addon["RawValues"] = extendInfoStruct.RawValues
			}
		}
	}

	longRg := util.GetLongRegion(cluster.Region)
	updateAppReq := UpdateAppReq{
		RequestV3Common: RequestV3Common{
			RequestID:     uuid.NewString(),
			Action:        "UpdateApp",
			AppID:         cluster.AppID,
			APIModule:     apiExtenderInfo.APIModule,
			Region:        longRg,
			Version:       apiExtenderInfo.Version,
			RequestSource: apiExtenderInfo.RequestSource,
			Language:      apiExtenderInfo.Language,
			UIN:           cluster.UIN,
			SubAccountUIN: cluster.SubUIN,
		},
		ClusterId: cluster.ClusterId,
		AppName:   cluster.Component,
		AppVersion: AppVersion{
			AppVersion:    cluster.AppVersion,
			PluginVersion: cluster.PluginVersion,
			ImageTag:      cluster.ImageTag,
		},
		AppStrategy: appStrategy,
		ProductName: cluster.Type,
		AppAction:   appAction,
		Values: Values{
			Token:         cluster.Token,
			User:          cluster.User,
			ExtendInfo:    extendInfo,
			ChangeId:      cluster.StarshipChangeId,
			BatchId:       0,
			TaskName:      fmt.Sprintf("%s %s", cluster.Component, cluster.AppAction),
			MetaClusterId: cluster.MetaID,
			Addon:         addon,
		},
	}
	return updateAppReq, nil
}

func buildExtendInfo(extendInfoStr string, cluster *db.TaskClusterInfo) (string, error) {

	var extendInfo model.ExtendInfo
	if extendInfoStr == "" {
		extendInfoStr = "{}"
	}
	if err := json.Unmarshal([]byte(extendInfoStr), &extendInfo); err != nil {
		return "", fmt.Errorf("failed to unmarshal extend info: %w", err)
	}

	// 更新 namespace 和 name
	if cluster.NameSpace != "" {
		extendInfo.Namespace = cluster.NameSpace
	}
	if cluster.Name != "" {
		extendInfo.Name = cluster.Name
	}

	// 重新序列化
	bytes, err := json.Marshal(extendInfo)
	if err != nil {
		return "", fmt.Errorf("failed to marshal updated extend info: %v", err)
	}

	return string(bytes), nil
}

func buildEkletAgentExtendInfo(extendInfoStr string, cluster *db.TaskClusterInfo) (string, error) {

	var extendInfo model.ExtendInfo
	if extendInfoStr == "" {
		extendInfoStr = "{}"
	}
	if err := json.Unmarshal([]byte(extendInfoStr), &extendInfo); err != nil {
		return "", fmt.Errorf("failed to unmarshal extend info: %w", err)
	}

	// 更新 namespace 和 name
	if cluster.NameSpace != "" {
		extendInfo.EksId = cluster.NameSpace
	}

	// 重新序列化
	bytes, err := json.Marshal(extendInfo)
	if err != nil {
		return "", fmt.Errorf("failed to marshal updated extend info: %v", err)
	}

	return string(bytes), nil
}

func buildRollbackReq(cluster *db.TaskClusterInfo) (RollbackAppReq, error) {
	// 确保region不为空
	region := cluster.Region
	if region == "" {
		// 如果region为空，尝试从clusterId获取，优先使用缓存版本
		var err error
		region, err = util.GetRegionAliasFromClusterIDWithCache(cluster.ClusterId)
		if err != nil {
			// 如果缓存版本失败，降级到原版本
			region, err = util.GetRegionAliasFromClusterID(cluster.ClusterId)
			if err != nil {
				return RollbackAppReq{}, fmt.Errorf("failed to get region from cluster ID: %w", err)
			}
		}
	}

	longRg := util.GetLongRegion(region)
	taskId, err := strconv.ParseInt(cluster.StarshipTaskId, 10, 64)
	if err != nil {
		return RollbackAppReq{}, fmt.Errorf("failed to parse task id: %w", err)
	}
	rollbackAppReq := RollbackAppReq{
		RequestV3Common: RequestV3Common{
			RequestID:     uuid.NewString(),
			Action:        "RollbackApp",
			AppID:         cluster.AppID,
			APIModule:     apiExtenderInfo.APIModule,
			Region:        longRg,
			Version:       apiExtenderInfo.Version,
			RequestSource: apiExtenderInfo.RequestSource,
			Language:      apiExtenderInfo.Language,
			UIN:           cluster.UIN,
			SubAccountUIN: cluster.SubUIN,
		},
		TaskId: taskId,
	}
	return rollbackAppReq, nil
}

func DescribeAppUpgradingProgress(cluster *db.TaskClusterInfo) (*DescribeAppUpgradingProgressResponse, error) {
	req, err := buildDescribeAppUpgradingProgressReq(cluster)
	if err != nil {
		return nil, err
	}

	bodyBytes, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	// 定义重试参数
	maxRetries := 5
	initialBackoff := 3 * time.Second
	var lastErr error
	var upgradingProgress DescribeAppUpgradingProgressResponse

	// 重试逻辑
	for attempt := 0; attempt < maxRetries; attempt++ {
		// DescribeAppUpgradingProgress是查询操作，可以重试，增加超时时间到10秒
		respBody, _, err := http_util.PostRequest(fmt.Sprintf(TKE_CLOUD_GW_URL, cluster.Region), bodyBytes, nil, 60*time.Second)
		if err != nil {
			lastErr = err
			time.Sleep(initialBackoff * time.Duration(attempt+1)) // 指数退避
			continue
		}

		klog.Infof("DescribeAppUpgradingProgress response (attempt %d/%d): %s", attempt+1, maxRetries, string(respBody))

		if err = json.Unmarshal(respBody, &upgradingProgress); err != nil {
			lastErr = err
			time.Sleep(initialBackoff * time.Duration(attempt+1))
			continue
		}

		// 检查是否有 "can not find task" 错误
		if upgradingProgress.Response.Error.Code == "ResourceNotFound" &&
			upgradingProgress.Response.Error.Message == "can not find task" {
			klog.Infof("Task not found yet, retrying (%d/%d)...", attempt+1, maxRetries)
			time.Sleep(initialBackoff * time.Duration(attempt+1))
			continue
		}

		// 检查 TaskId 是否有效
		if upgradingProgress.Response.Task.TaskId == 0 {
			// 如果是最后一次尝试，返回错误
			if attempt == maxRetries-1 {
				return nil, fmt.Errorf("describe app upgrading progress failed after %d attempts", maxRetries)
			}

			klog.Infof("Invalid TaskId (0), retrying (%d/%d)...", attempt+1, maxRetries)
			time.Sleep(initialBackoff * time.Duration(attempt+1))
			continue
		}

		// 成功获取到有效的响应
		return &upgradingProgress, nil
	}

	// 所有重试都失败了
	if lastErr != nil {
		return nil, fmt.Errorf("describe app upgrading progress failed after %d attempts: %v", maxRetries, lastErr)
	}

	return nil, fmt.Errorf("describe app upgrading progress failed after %d attempts", maxRetries)
}

func buildDescribeAppUpgradingProgressReq(cluster *db.TaskClusterInfo) (DescribeAppUpgradingProgressReq, error) {
	longRg := util.GetLongRegion(cluster.Region)
	appAction := ""
	if cluster.AppAction != "publish" {
		appAction = cluster.AppAction
	}
	req := DescribeAppUpgradingProgressReq{
		RequestV3Common: RequestV3Common{
			RequestID:     uuid.NewString(),
			Action:        "DescribeAppUpgradingProgress",
			AppID:         cluster.AppID,
			APIModule:     apiExtenderInfo.APIModule,
			Region:        longRg,
			Version:       apiExtenderInfo.Version,
			RequestSource: apiExtenderInfo.RequestSource,
			Language:      apiExtenderInfo.Language,
			UIN:           cluster.UIN,
			SubAccountUIN: cluster.SubUIN,
		},
		AppName:   cluster.Component,
		ClusterId: cluster.ClusterId,
		ChangeId:  cluster.StarshipChangeId,
		AppAction: appAction,
	}
	return req, nil
}
