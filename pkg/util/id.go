// Package util for golint
package util

import (
	"fmt"
	"strings"

	"git.woa.com/kmetis/starship-engine/pkg/cache"
	"git.woa.com/kmetis/starship-engine/pkg/config"

	"k8s.io/klog/v2"
)

const (
	TKE_CLUSTER  = "tke"
	EKS_CLUSTER  = "eks"
	EDGE_CLUSTER = "edge"
)

type dxy struct {
	d int64
	x int64
	y int64
}

type pair struct {
	x uint64 //id
	y uint64 //regionID
}

var prefixLenMap = [3]uint8{32, 36, 40}

var postfixLenMap = [3]uint8{9, 10, 11}

var primeMap = [3]uint64{4294967029, 68719476503, 1000000005721}

var idBase uint8 = 36

var idSEPARATOR byte = '-'

var CLUSTER_PASSWORD = "dcoker-cls"

var RegionIDMap = map[string]int64{
	"gz":          1,
	"sh":          4,
	"hk":          5,
	"ca":          6,
	"shjr":        7,
	"bj":          8,
	"sg":          9,
	"szjr":        11,
	"usw":         15,
	"cd":          16,
	"de":          17,
	"kr":          18,
	"cq":          19,
	"in":          21,
	"use":         22,
	"th":          23,
	"jp":          25,
	"jnec":        31,
	"hzec":        32,
	"nj":          33,
	"fzec":        34,
	"whec":        35,
	"tsn":         36,
	"szx":         37,
	"tpe":         39,
	"csec":        45,
	"bjjr":        46,
	"sjwec":       53,
	"qy":          54,
	"hfeec":       55,
	"sheec":       56,
	"xiyec":       57,
	"xbec":        58,
	"jkt":         72,
	"qyxa":        73,
	"sao":         74,
	"shadc":       78,
	"gzwxzf":      82,
	"shwxzf":      83,
	"szjxcft":     84,
	"shhqcft":     92,
	"cgoec":       71,
	"shhqcftfzhj": 93,
	"shwxzfjpyzc": 99,
}

// init inits config
//func init() {
//	CLUSTER_PASSWORD = os.Getenv("CLUSTER_PASSWORD")
//}

func extendedEuclid(a, b int64) dxy {
	if b <= 0 {
		ret := dxy{a, 1, 0}
		return ret
	}
	tmp := extendedEuclid(b, a%b)
	ret := dxy{tmp.d, tmp.y, tmp.x - (a/b)*tmp.y}
	return ret
}

func inverse(a, b int64) int64 {
	tmp := extendedEuclid(a, b)
	if tmp.x < 0 {
		tmp.x = tmp.x%b + b
	}
	return tmp.x
}

func splitBit(val uint64, pos uint8, len uint8) uint64 {
	return (val >> pos) & ((1 << len) - 1)
}

func stepLen(prefixLen, postfixLen uint8) uint8 {
	return prefixLen / postfixLen
}

func stepPostfixLen(stepIdx, stepLen, prefixLen, postfixLen uint8) uint8 {
	if stepIdx == (postfixLen - 1) {
		return prefixLen - (stepIdx * stepLen)
	} else {
		return stepLen
	}
}

func splitPrefixPostfix(val uint64, prefixLen uint8,
	postfixLen uint8) pair {
	var prefix uint64 = 0
	var postfix uint64 = 0
	var i uint8 = 0
	step := stepLen(prefixLen, postfixLen)
	for i = 0; i < postfixLen; i++ {
		postfix |= splitBit(val, (step+1)*i, 1) << i
		prefix |= splitBit(val, (step+1)*i+1, stepPostfixLen(i, step, prefixLen, postfixLen)) << (step * i)
	}
	ret := pair{prefix, postfix}
	return ret
}

func iPow(x, y uint8) uint64 {
	var ret uint64 = 1
	var i uint8 = 0
	for i = 0; i < y; i++ {
		ret = ret * uint64(x)
	}
	return ret
}

func str2Int(buf []byte, len uint8, base uint8) uint64 {
	var ret uint64 = 0
	var m, i uint8

	for i = 0; i < len-1; i++ {
		m = buf[len-2-i]
		if m >= '0' && m <= '9' {
			m = m - '0'
		} else {
			m = m - 'a' + 10
		}
		ret += iPow(base, i) * uint64(m)
	}
	return ret
}

func encodePassword(buf []byte, len uint8, retLen uint8) uint64 {
	var ret uint64 = 0
	var i uint8 = 0
	for i = 0; i < len-1; i++ {
		ret |= uint64(buf[len-2-i]) << (8 * i)
	}
	if retLen > 0 {
		ret &= ((uint64)(1) << retLen) - 1
	}
	return ret
}

// Decode decodes func
func Decode(buf []byte, prefixLen uint8, postfixLen uint8,
	password []byte, base uint8, prime uint64) pair {
	iPassword := encodePassword(password, uint8(len(password)+1), prefixLen+postfixLen)
	ret := str2Int(buf, uint8(len(buf)+1), base)
	ret ^= iPassword

	pRet := splitPrefixPostfix(ret, prefixLen, postfixLen)
	pRet.x = uint64(inverse(int64(pRet.x), int64(prime)))
	return pRet
}

// DecodeClusterID decodes clusterID
func DecodeClusterID(clusterID string) (id uint64, regionID uint64, err error) {
	return DecodeID(clusterID, CLUSTER_PASSWORD)
}

// DecodeID decodes ID
func DecodeID(uID string, password string) (x uint64, y uint64, err error) {
	p := pair{}

	index := strings.IndexByte(uID, idSEPARATOR)
	if index < 0 {
		return 0, 0, fmt.Errorf("failed to decode id:%s, ret:%v", uID, -1)
	}
	buf := []byte(uID)

	idBuf := buf[index+1:]
	var idLen = len(idBuf)
	if idLen < 8 || idLen > 10 {
		return 0, 0, fmt.Errorf("id %s len after - should between 8 and 10", uID)
	}
	var prefixLen = prefixLenMap[idLen-8]
	var postfixLen = postfixLenMap[idLen-8]
	var prime = primeMap[idLen-8]
	p = Decode(idBuf, prefixLen, postfixLen, []byte(password), idBase, prime)
	return p.x, p.y, nil
}

// GetClusterType returns cluster type
func GetClusterType(regionID uint64) string {
	if regionID > uint64(488) {
		return EKS_CLUSTER
	}

	switch regionID >> 7 {
	case uint64(0):
		return TKE_CLUSTER
	case uint64(1):
		return EKS_CLUSTER
	case uint64(2):
		return EDGE_CLUSTER
	}

	return ""
}

// GetRealRegionID returns region ID
func GetRealRegionID(regionID uint64) uint64 {
	var realRegion uint64
	if regionID > uint64(488) {
		realRegion = regionID - uint64(488)
	} else {
		realRegion = regionID - uint64(128)
	}

	return realRegion
}

// GetRegionAliasFromClusterID returns region alias
func GetRegionAliasFromClusterID(clusterID string) (string, error) {
	// 首先检查是否有配置文件中的覆盖配置
	if overrideRegion, exists := config.GetRegionForCluster(clusterID); exists {
		return overrideRegion, nil
	}

	// 使用原有的解码逻辑
	_, regionID, err := DecodeClusterID(clusterID)
	if err != nil {
		return "", err
	}

	if strings.HasPrefix(clusterID, "cls-") {
		clusterType := GetClusterType(regionID)
		if clusterType == "eks" {
			regionID = GetRealRegionID(regionID)
		}
		//log.Info(clusterType)
	}

	if regionID == 0 {
		regionID = 4
	}

	//log.Info(regionID)
	region := ""
	for key, value := range RegionIDMap {
		if value == int64(regionID) {
			region = key
			break
		}
	}

	if region == "" {
		return "", fmt.Errorf("failed to get region, region == ''")
	}

	return region, nil
}

// GetRegionAliasFromClusterIDWithCache 从ES缓存获取集群region信息的缓存版本
// 优先从ES缓存获取，如果缓存不可用或找不到集群，则跳过当前集群
func GetRegionAliasFromClusterIDWithCache(clusterID string) (string, error) {
	// 首先检查是否有配置文件中的覆盖配置
	if overrideRegion, exists := config.GetRegionForCluster(clusterID); exists {
		return overrideRegion, nil
	}

	// 获取全局缓存管理器
	cacheManager := cache.GetGlobalManager()

	// 检查ES缓存是否可用
	esCache := cacheManager.GetESCache()
	if esCache == nil {
		klog.V(3).Infof("ES cache not available for cluster %s, skipping", clusterID)
		return "", fmt.Errorf("ES cache not available for cluster %s", clusterID)
	}

	// 从缓存中获取集群信息
	clusterInfo, exists := cacheManager.GetClusterFromCache(clusterID)
	if !exists {
		klog.V(3).Infof("Cluster %s not found in ES cache, skipping", clusterID)
		return "", fmt.Errorf("cluster %s not found in ES cache", clusterID)
	}

	// 返回缓存中的region信息
	if clusterInfo.Region == "" {
		klog.V(3).Infof("Cluster %s has empty region in cache, skipping", clusterID)
		return "", fmt.Errorf("cluster %s has empty region in cache", clusterID)
	}

	klog.V(4).Infof("Successfully got region %s for cluster %s from cache", clusterInfo.Region, clusterID)
	return clusterInfo.Region, nil
}

// GetProductByClusterID returns cluster product
func GetProductByClusterID(clusterID string) (string, error) {
	_, regionID, err := DecodeClusterID(clusterID)
	if err != nil {
		return "", err
	}

	if strings.HasPrefix(clusterID, "cls-") {
		clusterType := GetClusterType(regionID)

		return clusterType, nil
	}

	return "", fmt.Errorf("failed to decode cluster %s", clusterID)
}
