/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package internalversion

import (
	v1 "git.code.oa.com/tke/api/platform/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// GPUManagerLister helps list GPUManagers.
// All objects returned here must be treated as read-only.
type GPUManagerLister interface {
	// List lists all GPUManagers in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.GPUManager, err error)
	// Get retrieves the GPUManager from the index for a given name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1.GPUManager, error)
	GPUManagerListerExpansion
}

// gPUManagerLister implements the GPUManagerLister interface.
type gPUManagerLister struct {
	indexer cache.Indexer
}

// NewGPUManagerLister returns a new GPUManagerLister.
func NewGPUManagerLister(indexer cache.Indexer) GPUManagerLister {
	return &gPUManagerLister{indexer: indexer}
}

// List lists all GPUManagers in the indexer.
func (s *gPUManagerLister) List(selector labels.Selector) (ret []*v1.GPUManager, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.GPUManager))
	})
	return ret, err
}

// Get retrieves the GPUManager from the index for a given name.
func (s *gPUManagerLister) Get(name string) (*v1.GPUManager, error) {
	obj, exists, err := s.indexer.GetByKey(name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("gpumanager"), name)
	}
	return obj.(*v1.GPUManager), nil
}
