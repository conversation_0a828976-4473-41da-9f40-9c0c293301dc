package batchStrategy

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	pkgcls "git.woa.com/kmetis/starship-engine/pkg/clusterinfo"
)

const (
	ClusterLevelBatchStrategyName = "ClusterLevelBatchStrategy"
)

// todo: 后续调整阈值
var defaultClusterLevelThresholds = []int{20, 50, 200, 500}

type ClusterLevelBatchStrategy struct {
	Name string
	ID   int64
}

func NewClusterLevelBatchStrategy() *ClusterLevelBatchStrategy {
	return &ClusterLevelBatchStrategy{
		Name: ClusterLevelBatchStrategyName,
		ID:   ClusterLevelBatchStrategyID,
	}
}

func (c *ClusterLevelBatchStrategy) DivideBatches(batches []BatchConfig, isMainBatch bool, options BatchStrategyOptions) ([]BatchConfig, error) {

	if isMainBatch && len(batches) > 1 {
		return nil, fmt.Errorf("main batch should have at most one batch")
	}
	if options.AppName == "" {
		return nil, fmt.Errorf("appName is empty")
	}

	thresholds := options.ClusterLevelThresholds
	if len(thresholds) == 0 {
		thresholds = defaultClusterLevelThresholds
	}
	sort.Ints(thresholds)

	newBatches := make([]BatchConfig, 0)
	helper, err := pkgcls.GetClsInfoHelper()
	if err != nil {
		return nil, err
	}
	for _, batch := range batches {
		if len(batch.Clusters) == 0 {
			continue
		}
		batchPriorityGroups := make(map[int][]string)
		for _, cluster := range batch.Clusters {
			level, err := helper.GetClusterLevel(options.AppName, cluster)
			if err != nil {
				return nil, err
			}
			if strings.TrimSpace(level) == "" {
				return nil, fmt.Errorf("cluster level is empty: %s", cluster)
			}
			levelInt, err := strconv.ParseInt(strings.ReplaceAll(level, "L", ""), 10, 64)
			if err != nil {
				return nil, fmt.Errorf("parse cluster level failed, cluster: %s, level: %s", cluster, level)
			}

			for i, threshold := range thresholds {
				if levelInt <= int64(threshold) {
					if _, ok := batchPriorityGroups[i]; !ok {
						batchPriorityGroups[i] = make([]string, 0)
					}
					batchPriorityGroups[i] = append(batchPriorityGroups[i], cluster)
					break
				}
				if i == len(thresholds)-1 {
					if _, ok := batchPriorityGroups[i+1]; !ok {
						batchPriorityGroups[i+1] = make([]string, 0)
					}
					batchPriorityGroups[i+1] = append(batchPriorityGroups[i+1], cluster)
				}
			}
		}

		index := 0
		for i := 0; i < len(thresholds)+1; i++ {
			if len(batchPriorityGroups[i]) > 0 {
				index++
				batchId := batch.BatchId
				if isMainBatch {
					batchId = int64(index)
				}
				newBatches = append(newBatches, BatchConfig{
					Region:   batch.Region,
					Clusters: batchPriorityGroups[i],
					BatchId:  batchId,
				})
			}
		}
	}
	return newBatches, nil
}

func (c *ClusterLevelBatchStrategy) GetName() string {
	return ClusterLevelBatchStrategyName
}

func (c *ClusterLevelBatchStrategy) GetID() int64 {
	return ClusterLevelBatchStrategyID
}
