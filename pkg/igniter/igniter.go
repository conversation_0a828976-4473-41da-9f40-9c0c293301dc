package igniter

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/pborman/uuid"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"

	"git.woa.com/kmetis/starship-engine/pkg/batchStrategy"
	"git.woa.com/kmetis/starship-engine/pkg/cache"
	"git.woa.com/kmetis/starship-engine/pkg/changeOrder"
	"git.woa.com/kmetis/starship-engine/pkg/cluster"
	"git.woa.com/kmetis/starship-engine/pkg/config"
	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/elasticsearch"
	"git.woa.com/kmetis/starship-engine/pkg/environment"
	"git.woa.com/kmetis/starship-engine/pkg/internalapi"
	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/robot"
	"git.woa.com/kmetis/starship-engine/pkg/ruleengine"
	"git.woa.com/kmetis/starship-engine/pkg/timer"
	"git.woa.com/tke/tops/conf"
)

const (
	RUNNING = "running"
	SUCCESS = "success"
	FAILED  = "failed"
)

var AppMap = map[string]string{
	consts.PUBLISH:   "发布",
	consts.PRECHECK:  "预检",
	consts.POSTCHECK: "后检",
}

type Igniter struct {
	environment environment.EnvSettings

	taskSync    *timer.Timer
	fuseSync    *timer.Timer
	summarySync *timer.Timer
	batchSync   *timer.Timer

	clusters *cluster.ClusterTable

	// ES同步器
	esSyncer *elasticsearch.Syncer
}

func NewIgniter(env environment.EnvSettings, tConf *conf.Config) *Igniter {
	i := &Igniter{
		environment: env,
		clusters:    cluster.NewClustersTable(tConf),
	}

	i.taskSync = timer.NewTimer("tasks-syncer", env.RequestGwDur, i.queryClusterStatus)
	i.fuseSync = timer.NewTimer("fuse-syncer", env.RequestGwDur, i.dealFuse)
	i.batchSync = timer.NewTimer("task-batch-syncer", env.BatchDuration, i.dealUnBatchedTask)
	// 机器人通知现在通过配置文件管理，始终启用定时器
	i.summarySync = timer.NewTimer("summary-syncer", env.RobotTime, i.sendRobot)

	// 初始化ES同步器
	i.initESSync()

	//i.tasks = task.NewTasksTable()
	return i
}

func (i *Igniter) Start(stopCh <-chan struct{}) error {
	// 启动ES同步器
	if i.esSyncer != nil {
		if err := i.esSyncer.Start(); err != nil {
			klog.Errorf("Failed to start ES syncer: %v", err)
		} else {
			klog.Info("ES syncer started successfully")
			// 设置ES缓存到全局管理器
			cache.GetGlobalManager().SetESCache(i.esSyncer.GetCache())
		}
	}

	// Launch workers to process task
	for j := 0; j < i.environment.ConcurrencyWorker; j++ {
		go wait.Until(i.runTaskProcessWorker, time.Duration(i.environment.Duration)*time.Second, stopCh)
	}

	go i.taskSync.Run()
	go i.fuseSync.Run()
	go i.batchSync.Run()
	// 机器人通知现在通过配置文件管理，始终启用
	go i.summarySync.Run()

	klog.Info("Started workers")
	<-stopCh
	klog.Info("Shutting down workers")

	// stop timer
	i.taskSync.Stop()
	i.taskSync.Stop()
	i.batchSync.Stop()
	i.summarySync.Stop()

	// 停止ES同步器
	if i.esSyncer != nil {
		i.esSyncer.Stop()
		klog.Info("ES syncer stopped")
	}

	return nil
}

// initESSync 初始化ES同步器
func (i *Igniter) initESSync() {
	// 获取ES配置
	esConfig := config.GetElasticsearchConfig()

	// 直接使用配置创建ES同步器
	syncer, err := elasticsearch.NewSyncer(&esConfig)
	if err != nil {
		klog.Errorf("Failed to create ES syncer: %v", err)
		return
	}

	i.esSyncer = syncer
	klog.Info("ES syncer created successfully (client will be initialized on first sync)")
}

// GetESCache 获取ES缓存，供其他模块使用
func (i *Igniter) GetESCache() *elasticsearch.Cache {
	if i.esSyncer == nil {
		return nil
	}
	return i.esSyncer.GetCache()
}

// GetESStats 获取ES同步统计信息
func (i *Igniter) GetESStats() elasticsearch.CacheStats {
	if i.esSyncer == nil {
		return elasticsearch.CacheStats{}
	}
	return i.esSyncer.GetStats()
}

// ForceESSync 强制执行ES同步
func (i *Igniter) ForceESSync() error {
	if i.esSyncer == nil {
		return fmt.Errorf("ES syncer not initialized")
	}
	return i.esSyncer.ForceSync()
}

func (i *Igniter) runTaskProcessWorker() {
	// 若任务状态为运行中，执行查询一个待发布的集群进行发布
	unplayedCluster, err := db.GetUnplayedTaskAndSetRunning(i.environment.ConcurrencyTask)
	if err != nil {
		klog.Errorf("Get unplayed task failed: %v", err)
		return
	}
	if unplayedCluster == nil {
		// klog.Info("No pending job")
		return
	}

	if err := i.processTask(unplayedCluster); err != nil {
		klog.Errorf("Process task %v failed: %v", unplayedCluster, err)
		return
	}
}

func (i *Igniter) processTask(cluster *db.TaskClusterInfo) error {
	klog.Infof("Process task %v", cluster)
	err := i.clusters.GetCluster(cluster)
	if err != nil {
		err := i.dealError(cluster, err)
		return err
	}
	if i.environment.Debug && (cluster.Region != "qy" || cluster.ClusterId != "cls-3aeycjut") {
		return i.dealError(cluster, fmt.Errorf("cluster %s not support publish in debug mode", cluster.ClusterId))
	}

	taskId, err := internalapi.UpdateApp(cluster)
	if err != nil {
		err := i.dealError(cluster, err)
		klog.Errorf("%s update cluster db %s failed: %v", cluster.ClusterId, cluster.Component, err)
		return err
	}

	cluster.StarshipTaskId = taskId
	cluster.Status = db.RUNNING
	if err = db.UpdateClusterById(cluster); err != nil {
		klog.Errorf("Update task %v failed: %v", cluster, err)
		return err
	}
	return nil
}

func (i *Igniter) dealError(cluster *db.TaskClusterInfo, err error) error {
	klog.Errorf("Process Task %v failed: %v", cluster, err)
	cluster.Status = FAILED
	cluster.Reason = err.Error()
	if err = db.UpdateClusterById(cluster); err != nil {
		klog.Errorf("Update task %v failed: %v", cluster, err)
		return err
	}
	return nil
}

func (i *Igniter) queryClusterStatus() {
	klog.Info("Query cluster status")
	tasks, err := db.GetClusterInfoByStatus(db.RUNNING)
	if err != nil {
		klog.Errorf("Get task by status failed: %v", err)
		return
	}

	klog.Infof("Query task count: %d", len(tasks))
	if len(tasks) > 0 {
		for _, playedTask := range tasks {
			if playedTask.Region == "" {
				continue
			}
			if err := i.queryClusterResult(playedTask); err != nil {
				klog.Warningf("Process task %v failed: %v", playedTask, err)
			}
		}
	}

	err = i.dealCompleted()
	if err != nil {
		klog.Errorf("Deal completed failed: %v", err)
	}
}

func (i *Igniter) queryClusterResult(cluster *db.TaskClusterInfo) error {
	defer func() {
		if err := db.UpdateClusterById(cluster); err != nil {
			klog.Errorf("Update cluster %v failed: %v", cluster, err)
		}
	}()
	upgradingProgress, err := internalapi.DescribeAppUpgradingProgress(cluster)
	if err != nil {
		cluster.Status = FAILED
		cluster.Reason = err.Error()
		klog.Errorf("Describe app %s upgrading progress %s failed: %v", cluster.Component, cluster.AppAction, err)
		return err
	}

	cluster.Stage, cluster.Status, cluster.Reason = checkClusterStatus(upgradingProgress)

	// 在checkClusterStatus后新增变更日历上报功能
	// 只有当任务是发布任务，且状态发生变化时才上报
	if cluster.AppAction == consts.PUBLISH {
		// 只有成功或失败状态才上报，预检失败不上报
		if cluster.Status == SUCCESS || cluster.Status == FAILED {
			// 异步上报变更日历，避免阻塞主流程
			go func() {
				if err := i.reportChangeCalendarForCluster(cluster); err != nil {
					klog.Warningf("Failed to report change calendar for cluster %s: %v", cluster.ClusterId, err)
				}
			}()
		}
	}

	return nil
}

// reportChangeCalendarForCluster 为集群上报变更日历
func (i *Igniter) reportChangeCalendarForCluster(cluster *db.TaskClusterInfo) error {
	// 获取变更订单信息
	changeOrderResp, err := changeOrder.GetPostOrder(cluster.ChangeId)
	if err != nil {
		klog.Errorf("Failed to get change order %s: %v", cluster.ChangeId, err)
		return fmt.Errorf("failed to get change order: %w", err)
	}

	// 检查变更订单是否有效
	if changeOrderResp.Data.Item.ID == "" {
		klog.Warningf("Change order %s not found or invalid", cluster.ChangeId)
		return fmt.Errorf("change order %s not found", cluster.ChangeId)
	}

	// 创建部署对象
	deployObjects := changeOrder.CreateDeployObjectsFromClusters([]string{cluster.ClusterId}, "cluster")

	// 确定上报状态
	var reportStatus string
	switch cluster.Status {
	case SUCCESS:
		reportStatus = "success"
	case FAILED:
		reportStatus = "failure"
	default:
		// 其他状态不上报
		klog.V(2).Infof("Cluster %s status %s does not need calendar report", cluster.ClusterId, cluster.Status)
		return nil
	}

	// 获取上报配置
	calendarOptions := changeOrder.GetDefaultCalendarReportOptions()

	// 创建上下文
	ctx := context.Background()
	ctx = context.WithValue(ctx, "trace_id", uuid.New()+"-"+cluster.StarshipChangeId)

	// 上报变更日历（使用带扩展信息的版本）
	err = changeOrder.ReportChangeCalendarWithExtension(
		ctx,
		changeOrderResp,
		cluster,
		reportStatus,
		deployObjects,
		calendarOptions,
	)

	if err != nil {
		klog.Errorf("Failed to report change calendar for cluster %s: %v", cluster.ClusterId, err)
		return fmt.Errorf("failed to report change calendar: %w", err)
	}

	klog.Infof("Successfully reported change calendar for cluster %s, status: %s, change_id: %s",
		cluster.ClusterId, reportStatus, cluster.ChangeId)
	return nil
}

func (i *Igniter) dealFuse() {
	failureRate, err := db.GetBatchFailureRate()
	if err != nil {
		klog.Errorf("Get task failure rate failed: %v", err)
		return
	}

	// 遍历失败率列表
	for _, rate := range failureRate {
		if rate.FailRate > i.environment.FuseRate {
			// 更新 Task 表中相应 TaskId 的状态为 Pause
			if err := db.UpdateTaskByTaskId(rate.TaskId, map[string]interface{}{
				"status":      consts.PAUSE_STA,
				"update_time": time.Now(),
			}); err != nil {
				klog.Errorf("Failed to update status for TaskId: %d: %v", rate.TaskId, err)
				return
			}
			// 机器人通知现在通过配置文件管理，始终发送通知
			i.sendCompleteOrFuseToRobot(rate.TaskId, false)
		}
	}
}

// 处理未分批的task
func (i *Igniter) dealUnBatchedTask() {
	task, err := db.GetInBatchTask()
	if err != nil {
		klog.Errorf("failed to get task in batch, err: %v", err)
		return
	}
	if task != nil {
		maxTry := 3
		for n := 0; n < maxTry; n++ {
			if task.BatchStrategies != "" {
				err = doBatchTask(task)
			} else {
				err = doBatchTaskByAppStrategy(task)
			}
			if err != nil {
				klog.Infof("process task in batch failed, task: %d, err: %v", task.ID, err)
				time.Sleep(time.Second * 3)
			} else {
				break
			}
		}
		if err != nil {
			if err = db.UpdateStatusByID(task.ID, consts.BATCH_FAILED); err != nil {
				klog.Errorf("update task status failed, taskid: %d, status: %s", task.ID, consts.BATCH_FAILED)
			}
		}
	}
}

func doBatchTask(task *db.Task) error {
	var clusterOptions []model.ClusterOption
	if err := json.Unmarshal([]byte(task.ClusterOption), &clusterOptions); err != nil {
		return err
	}
	filterConfigs, err := GenerateFilterConfig(clusterOptions)
	if err != nil {
		return err
	}
	clusterPool, err := GetClustersFromESCache(task.ClusterType)
	if err != nil {
		return err
	}

	e := ruleengine.NewRuleEngine()
	matchedClusters, err := e.RunFilter(clusterPool, filterConfigs)
	if err != nil {
		return err
	}

	batchStrategies := make([]batchStrategy.BatchStrategy, 0)
	for _, s := range strings.Split(task.BatchStrategies, ",") {
		strategyID, err := strconv.ParseInt(s, 10, 64)
		if err != nil {
			return fmt.Errorf("invalid batch strategy id: %s", s)
		}
		st, ok := batchStrategy.RegistryBatchStrategyMap[int(strategyID)]
		if !ok {
			return fmt.Errorf("unsupport batch strategy: %s", s)
		}
		batchStrategies = append(batchStrategies, st)
	}
	chain := batchStrategy.NewBatchStrategyChain(batchStrategies...)

	allClusters := make([]string, 0)
	for _, cls := range matchedClusters {
		allClusters = append(allClusters, cls.ClusterID)
	}

	batches, err := chain.Execute(allClusters, batchStrategy.BatchStrategyOptions{
		AppAction: task.AppAction,
		AppName:   task.Component,
		BatchSize: task.BatchSize,
	})
	if err != nil {
		return err
	}

	if err = db.SaveBatchesAndUpdateTaskStatus(task.ID, batches); err != nil {
		return err
	}
	return nil
}

func doBatchTaskByAppStrategy(task *db.Task) error {
	var clusterOptions []model.ClusterOption
	if err := json.Unmarshal([]byte(task.ClusterOption), &clusterOptions); err != nil {
		return err
	}

	filterConfigs, err := GenerateFilterConfig(clusterOptions)
	if err != nil {
		return err
	}
	clusterPool, err := GetClustersFromESCache(task.ClusterType)
	if err != nil {
		return err
	}

	e := ruleengine.NewRuleEngine()
	matchedClusters, err := e.RunFilter(clusterPool, filterConfigs)
	if err != nil {
		return err
	}

	appBatchStrategy, err := db.GetAppBatchStrategyById(int64(task.AppBatchStrategy))
	if err != nil {
		return fmt.Errorf("get app batch strategy failed: %v", err)
	}
	var batchConfig []ruleengine.BatchConfig
	if err := yaml.Unmarshal([]byte(appBatchStrategy.Strategy), &batchConfig); err != nil {
		return fmt.Errorf("unmarshal app batch config failed: %v", err)
	}

	batches, err := e.RunBatch(batchConfig, matchedClusters)
	if err != nil {
		return err
	}
	if err = db.SaveAppStrategyBatchesAndUpdateTaskStatus(task.ID, batches); err != nil {
		return err
	}
	return nil
}

func (i *Igniter) dealCompleted() error {
	klog.Info("Deal completed")
	// deal batch
	batchCount, err := db.GetClusterBatchCount()
	if err != nil {
		klog.Errorf("Get batch count failed: %v", err)
		return err
	}
	taskIds := make([]int64, 0)
	for _, batch := range batchCount {
		if batch.Success+batch.Failed == batch.Total {
			klog.Infof("TaskId: %d, BatchId: %d, SubBatchId: %d has completed", batch.TaskId, batch.BatchId, batch.SubBatchId)
			if err := db.DealSubBatchCompleted(batch.TaskId, batch.BatchId, batch.SubBatchId); err != nil {
				klog.Errorf("Failed to deal subbatch completed: task %d, batch %d, subbatch %d, err: %v", batch.TaskId, batch.BatchId, batch.SubBatchId, err)
				return err
			}
			taskIds = append(taskIds, batch.TaskId)
		}
	}
	if len(taskIds) > 0 {
		// 处理 task 状态
		taskCount, err := db.GetTaskStats("", "", taskIds)
		if err != nil {
			klog.Errorf("Get task count failed: %v", err)
			return err
		}

		for _, task := range taskCount {
			// 处理task完成
			if task.Total == task.Success+task.Failed {
				if err := db.UpdateTaskByTaskId(task.TaskId, map[string]interface{}{
					"status":      consts.COMPLETED,
					"notify":      false,
					"update_time": time.Now(),
				}); err != nil {
					klog.Errorf("Failed to update status for TaskId: %d: %v", task.TaskId, err)
					return err
				}
				// 机器人通知现在通过配置文件管理，始终发送通知
				i.sendCompleteOrFuseToRobot(task.TaskId, true)
			} else {
				// 任务未完成，处理批次完成逻辑
				err := i.dealBatchCompleted(task.TaskId)
				if err != nil {
					klog.Errorf("Failed to deal batch completed: %v", err)
					return err
				}
			}
		}
	}

	return nil
}

func (i *Igniter) dealBatchCompleted(taskId int64) error {
	if err := db.UpdateTaskByTaskId(taskId, map[string]interface{}{
		"notify":      false,
		"update_time": time.Now(),
	}); err != nil {
		klog.Errorf("Failed to update status for TaskId: %d: %v", taskId, err)
		return err
	}
	// 机器人通知现在通过配置文件管理，始终发送通知
	var buf bytes.Buffer
	task, err := db.GetTaskByTaskId(taskId)
	if err != nil {
		klog.Errorf("Get task by id failed: %v", err)
		return err
	}
	i.sendNotify(task, buf, consts.BATCH_COMPLETED)
	return nil
}

func checkClusterStatus(response *internalapi.DescribeAppUpgradingProgressResponse) (string, string, string) {
	var currentStage string
	var failureReasons string
	var hasFailed bool

	// 从 describe 返回结果中获取 ExtendInfo 并解析 SkipWarningPreCheckItem 配置
	skipWarningPreCheck := parseSkipWarningPreCheckItem(response.Response.Task.ExtendInfo)

	currentStage = response.Response.Task.Stage
	if response.Response.Task.Status == "done" {
		if response.Response.Task.Reason == "" {
			for _, subtask := range response.Response.Task.Subtasks {
				for _, risk := range subtask.Risks {
					if risk.Code == "FAILED" || risk.Code == "ERROR" {
						// 如果配置了跳过 warning 级别的预检项，且当前是预检或发布子任务，且错误级别是 warning，则跳过
						if shouldSkipWarningError(skipWarningPreCheck, subtask.Action, risk.Level) {
							klog.Infof("Skipping warning level error in %s subtask: %s (level: %s)", subtask.Action, risk.Name, risk.Level)
							continue
						}

						hasFailed = true
						if strings.Contains(failureReasons, risk.Detail) {
							continue
						}
						failureReasons += risk.Detail
					}
				}
			}
			if hasFailed {
				return response.Response.Task.Stage, "failed", failureReasons
			}
			return response.Response.Task.Stage, "success", ""
		}
		failureReasons = response.Response.Task.Reason + ";"
		for _, subtask := range response.Response.Task.Subtasks {
			if subtask.Status == "pending" {
				break
			}
			currentStage = subtask.Action
			if subtask.Reason != "" {
				hasFailed = true
			}
			for _, risk := range subtask.Risks {
				if risk.Code == "FAILED" || risk.Code == "ERROR" {
					// 如果配置了跳过 warning 级别的预检项，且当前是预检或发布子任务，且错误级别是 warning，则跳过
					if shouldSkipWarningError(skipWarningPreCheck, subtask.Action, risk.Level) {
						klog.Infof("Skipping warning level error in %s subtask: %s (level: %s)", subtask.Action, risk.Name, risk.Level)
						continue
					}

					hasFailed = true
					if strings.Contains(failureReasons, risk.Detail) {
						continue
					}
					failureReasons += risk.Detail
				}
			}
			if hasFailed {
				break
			}
		}
	} else if response.Response.Task.Status == "pending" || response.Response.Task.Status == "processing" {
		for _, subtask := range response.Response.Task.Subtasks {
			if subtask.Status == "pending" || subtask.Status == "processing" {
				currentStage = subtask.Action
				break
			}
		}
	}

	var status string
	if hasFailed || len(failureReasons) > 0 {
		status = "failed"
	} else if len(failureReasons) == 0 {
		status = "running"
	}
	return currentStage, status, failureReasons
}

// parseSkipWarningPreCheckItem 解析 ExtendInfo 中的 SkipWarningPreCheckItem 配置
func parseSkipWarningPreCheckItem(extendInfoStr string) bool {
	if extendInfoStr == "" {
		return false
	}

	var extendInfo model.ExtendInfo
	if err := json.Unmarshal([]byte(extendInfoStr), &extendInfo); err != nil {
		klog.Warningf("Failed to parse ExtendInfo: %v", err)
		return false
	}

	return extendInfo.SkipWarningPreCheckItem
}

// shouldSkipWarningError 判断是否应该跳过 warning 级别的错误
func shouldSkipWarningError(skipWarningPreCheck bool, subtaskAction, riskLevel string) bool {
	// 只有在配置了跳过 warning 预检项时才处理
	if !skipWarningPreCheck {
		return false
	}

	// 只在预检(precheck)和发布(upgrade)子任务中跳过 warning 级别的错误
	if subtaskAction != "precheck" && subtaskAction != "upgrade" {
		return false
	}

	// 只跳过 warning 级别的错误
	return riskLevel == "warning"
}

func (i *Igniter) sendRobot() {
	// 1. 获取task为Running，并且 notify 为true 的taskid list
	tasks, err := db.GetTaskByFields(map[string]interface{}{
		"status": consts.RUNNING,
		"notify": true,
	})
	if err != nil {
		klog.Errorf("Robot get task by fields failed: %v", err)
	}

	// 2.根据tasklist 遍历cluster
	for _, task := range tasks {
		var buf bytes.Buffer
		i.sendNotify(task, buf, "")
	}
}

func (i *Igniter) sendCompleteOrFuseToRobot(taskId int64, isComplete bool) {
	task, err := db.GetTaskByTaskId(taskId)
	if err != nil {
		klog.Errorf("Get task by id failed: %v", err)
	}

	var buf bytes.Buffer
	sendType := ""
	if isComplete {
		fmt.Fprintf(&buf, "**<font color=\"red\">【%s任务%s已完成，请%s及时关注任务结果】</font>**\n", AppMap[task.AppAction], task.TaskName, task.User)
		sendType = consts.COMPLETED
	} else {
		fmt.Fprintf(&buf, "**<font color=\"red\">【%s任务%s失败过多已被熔断，请%s及时关注失败任务】</font>**\n", AppMap[task.AppAction], task.TaskName, task.User)
		sendType = consts.PAUSE_STA
	}
	i.sendNotify(task, buf, sendType)
}

func (i *Igniter) sendNotify(task *db.Task, buf bytes.Buffer, sendType string) {
	// 获取cluster数据
	clusters, err := db.GetClusterByFields(map[string]interface{}{
		"task_id": task.ID,
	})
	if err != nil {
		klog.Errorf("get cluster data failed: %v", err)
		return
	}

	// 按状态分类cluster
	clustersMap := make(map[string][]*db.Cluster)
	for _, cls := range clusters {
		if cls.Status != "" {
			clustersMap[cls.Status] = append(clustersMap[cls.Status], cls)
		}
	}

	// 获取batch数据
	batches, err := db.GetBatchByFields(map[string]interface{}{
		"task_id": task.ID,
	})
	if err != nil {
		klog.Errorf("get batch data failed: %v", err)
		return
	}

	// 按状态分类batch
	batchStatusMap := make(map[string][]*db.Batch)
	for _, batch := range batches {
		if batch.Status != "" {
			batchStatusMap[batch.Status] = append(batchStatusMap[batch.Status], batch)
		}
	}

	uniqueBatches := make(map[int64]bool)
	for _, batch := range batches {
		uniqueBatches[batch.BatchId] = true
	}

	if sendType != "" {
		switch sendType {
		case consts.COMPLETED:
			robot.SendToText(task.Component, fmt.Sprintf("%s任务%s已完成：总任务 %d，已完成 %d，正在运行 %d，失败数量 %d",
				AppMap[task.AppAction], task.TaskName, len(clusters), len(clustersMap[SUCCESS]), len(clustersMap[RUNNING]),
				len(clustersMap[FAILED])), []string{task.User})
		case consts.PAUSE_STA:
			robot.SendToText(task.Component, fmt.Sprintf("%s任务%s已熔断：总任务 %d，已完成 %d，正在运行 %d，失败数量 %d",
				AppMap[task.AppAction], task.TaskName, len(batches), len(clustersMap[SUCCESS]), len(clustersMap[RUNNING]),
				len(clustersMap[FAILED])), []string{task.User})
		case consts.BATCH_COMPLETED:
			lastCompletedBatch := getLastCompletedBatch(batchStatusMap)
			nextBatch := getNextBatch(batchStatusMap)
			if lastCompletedBatch != nil && nextBatch != nil {
				nextBatchTime := lastCompletedBatch.EndTime.Add(time.Duration(lastCompletedBatch.Interval) * time.Second)
				robot.SendToText(task.Component, fmt.Sprintf("%s任务%s正在灰度中, 当前批次 %d-%d,地域 %s 已完成。总批次 %d, 下一批次 %d-%d, 地域 %s，预计执行时间 %v",
					AppMap[task.AppAction], task.TaskName, lastCompletedBatch.BatchId, lastCompletedBatch.SubBatchId, lastCompletedBatch.Region,
					len(uniqueBatches), nextBatch.BatchId, nextBatch.SubBatchId, nextBatch.Region, nextBatchTime.Format("01-02 15:04:05")), []string{task.User})
			}
		}
	}

	now := time.Now()
	formattedTime := now.Format("01-02 15:04:05")
	fmt.Fprintf(&buf, "**【%v, <font color=\"info\">%s任务%s</font>统计】**\n", formattedTime, AppMap[task.AppAction], task.TaskName)
	fmt.Fprintf(&buf, "**发布单: ** %s", task.ChangeId)
	fmt.Fprintf(&buf, "**\t组件: **%s", task.Component)
	fmt.Fprintf(&buf, "**\t用户: **%s\n", task.User)
	if task.ImageTag != "" {
		fmt.Fprintf(&buf, "**镜像版本: **%s\n", task.ImageTag)
	}
	if task.AppVersion != "" {
		fmt.Fprintf(&buf, "**应用版本: **%s\n", task.AppVersion)
	}
	if task.ExtendInfo != "" {
		fmt.Fprintf(&buf, "**额外变更: **%s\n", task.ExtendInfo)
	}
	fmt.Fprintf(&buf, "**总任务: **%d", len(clusters))
	fmt.Fprintf(&buf, "\t**已完成: **%d", len(clustersMap[SUCCESS]))
	fmt.Fprintf(&buf, "\t**运行中: **%d", len(clustersMap[RUNNING]))
	fmt.Fprintf(&buf, "\t**失败数量: **<font color=\"red\">%d</font>", len(clustersMap[FAILED]))
	fmt.Fprintf(&buf, "\t**总批次: **%d", len(uniqueBatches))

	if len(batchStatusMap[consts.RUNNING]) > 0 {
		runningBatch := batchStatusMap[consts.RUNNING][0]
		fmt.Fprintf(&buf, "\t**当前批次: **%d-%d", runningBatch.BatchId, runningBatch.SubBatchId)
		fmt.Fprintf(&buf, "\t**地域: **%s\n", runningBatch.Region)
	}

	if len(batchStatusMap[consts.COMPLETED]) > 0 && len(batchStatusMap[consts.RUNNING]) == 0 && len(batchStatusMap[consts.PENDING]) > 0 {
		lastCompletedBatch := getLastCompletedBatch(batchStatusMap)
		nextBatch := getNextBatch(batchStatusMap)
		fmt.Fprintf(&buf, "\n**<font color=\"blue\">任务正在灰度中，请等待。</font>**\n")
		if lastCompletedBatch != nil {
			nextBatchTime := lastCompletedBatch.EndTime.Add(time.Duration(lastCompletedBatch.Interval) * time.Second)
			fmt.Fprintf(&buf, "**<font color=\"blue\">下一批预计执行时间: </font>**%v", nextBatchTime.Format("01-02 15:04:05"))
		}
		if nextBatch != nil {
			fmt.Fprintf(&buf, "\t**<font color=\"blue\">批次: </font>**%d-%d", nextBatch.BatchId, nextBatch.SubBatchId)
			fmt.Fprintf(&buf, "\t**<font color=\"blue\">地域: </font>**%s\n", nextBatch.Region)
		}
	}

	switch task.Component {
	case consts.APPFABRIC:
		fmt.Fprintf(&buf, "\n")
		fmt.Fprintf(&buf, "**发布中: **\n")
		for index, cls := range clustersMap[RUNNING] {
			fmt.Fprintf(&buf, "> **%d、**%s/%s,**阶段:**%s\n", index+1, cls.NameSpace, cls.Name, cls.Stage)
		}
		fmt.Fprintf(&buf, "\n")
		fmt.Fprintln(&buf, "**[失败](https://zhiyan.woa.com/operate/9595/task/#/task/result/29914): \n**")
		for index, cls := range clustersMap[FAILED] {
			fmt.Fprintf(&buf, "> **%d、**%s/%s,**原因:**%s\n", index+1, cls.NameSpace, cls.Name, cls.Reason)
		}
	case consts.EKS_POD:
		fmt.Fprintf(&buf, "\n")
		fmt.Fprintf(&buf, "**发布中: **\n")
		for index, cls := range clustersMap[RUNNING] {
			fmt.Fprintf(&buf, "> **%d、**%s %s/%s\n", index+1, cls.ClusterId, cls.NameSpace, cls.Name)
		}
		fmt.Fprintf(&buf, "\n")
		fmt.Fprintln(&buf, "**[失败](https://zhiyan.woa.com/operate/9595/task/#/task/result/29914): \n**")
		startIdx := 0
		failedCount := len(clustersMap[FAILED])
		if failedCount > 15 {
			startIdx = failedCount - 15
		}
		if startIdx < 0 {
			startIdx = 0
		}
		for i := startIdx; i < failedCount; i++ {
			cls := clustersMap[FAILED][i]
			reason := cls.Reason
			if len(reason) > 80 {
				reason = reason[:80] + "..."
			}
			fmt.Fprintf(&buf, "> **%d、**%s %s/%s,**原因:**%s\n", i+1, cls.ClusterId, cls.NameSpace, cls.Name, reason)
		}
	case consts.EKLET_AGENT:
		fmt.Fprintf(&buf, "\n")
		fmt.Fprintf(&buf, "**发布中: **\n")
		for index, cls := range clustersMap[RUNNING] {
			fmt.Fprintf(&buf, "**%d、**%s, %s, **阶段:**%s\n", index+1, cls.ClusterId, cls.NameSpace, cls.Stage)
		}
		fmt.Fprintf(&buf, "\n")
		fmt.Fprintln(&buf, fmt.Sprintf("**失败([详细情况](https://xy.woa.com/xy/app/prod/spx_starship/detail?task_name=%s)): \n**", task.TaskName))
		startIdx := 0
		failedCount := len(clustersMap[FAILED])
		if failedCount > 15 {
			startIdx = failedCount - 15
		}
		if startIdx < 0 {
			startIdx = 0
		}
		for i := startIdx; i < failedCount; i++ {
			cls := clustersMap[FAILED][i]
			reason := cls.Reason
			if len(reason) > 80 {
				reason = reason[:80] + "..."
			}
			fmt.Fprintf(&buf, "> **%d、**%s, %s, **原因:**%s\n", i+1, cls.ClusterId, cls.NameSpace, reason)
		}
	default:
		fmt.Fprintf(&buf, "\n**运行中: **\n")
		for index, cls := range clustersMap[RUNNING] {
			fmt.Fprintf(&buf, "> **%d、**%s, %s\n", index+1, cls.ClusterId, cls.Stage)
		}
		fmt.Fprintf(&buf, "\n")
		fmt.Fprintln(&buf, fmt.Sprintf("**失败([详细情况](https://xy.woa.com/xy/app/prod/spx_starship/detail?task_name=%s)): \n**", task.TaskName))
		startIdx := 0
		failedCount := len(clustersMap[FAILED])
		if failedCount > 15 {
			startIdx = failedCount - 15
		}
		if startIdx < 0 {
			startIdx = 0
		}
		for i := startIdx; i < failedCount; i++ {
			cls := clustersMap[FAILED][i]
			reason := cls.Reason
			if len(reason) > 80 {
				reason = reason[:80] + "..."
			}
			fmt.Fprintf(&buf, "> **%d、**%s,**原因:**%s\n", i+1, cls.ClusterId, reason)
		}
	}

	robot.SendToMarkdown(task.Component, buf.String())
}

func getLastCompletedBatch(batchStatusMap map[string][]*db.Batch) *db.Batch {
	// 计算下一批执行时间
	if len(batchStatusMap[consts.COMPLETED]) == 0 {
		return nil
	}
	completedBatches := batchStatusMap[consts.COMPLETED]
	sort.Slice(completedBatches, func(i, j int) bool {
		return completedBatches[i].EndTime.Before(*completedBatches[j].EndTime)
	})
	return completedBatches[len(completedBatches)-1]
}

func getNextBatch(batchStatusMap map[string][]*db.Batch) *db.Batch {
	pendingBatches := batchStatusMap[consts.PENDING]
	if len(pendingBatches) == 0 {
		return nil
	}

	// 双重排序：先按batchid，再按subbatchid
	sort.Slice(pendingBatches, func(i, j int) bool {
		if pendingBatches[i].BatchId != pendingBatches[j].BatchId {
			return pendingBatches[i].BatchId < pendingBatches[j].BatchId
		}
		return pendingBatches[i].SubBatchId < pendingBatches[j].SubBatchId
	})

	// 如果没有completed批次，直接返回第一个pending批次
	if len(batchStatusMap[consts.COMPLETED]) == 0 {
		return pendingBatches[0]
	}

	// 获取最后一个completed批次
	lastCompleted := batchStatusMap[consts.COMPLETED][0]
	for _, batch := range batchStatusMap[consts.COMPLETED] {
		if batch.BatchId > lastCompleted.BatchId ||
			(batch.BatchId == lastCompleted.BatchId && batch.SubBatchId > lastCompleted.SubBatchId) {
			lastCompleted = batch
		}
	}

	// 找到第一个batchid或subbatchid更大的pending批次
	for _, batch := range pendingBatches {
		if batch.BatchId > lastCompleted.BatchId ||
			(batch.BatchId == lastCompleted.BatchId && batch.SubBatchId > lastCompleted.SubBatchId) {
			return batch
		}
	}

	return nil
}
