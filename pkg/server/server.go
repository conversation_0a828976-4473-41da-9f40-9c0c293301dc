package server

import (
	"context"
	"net/http"

	"git.woa.com/kmetis/starship-engine/pkg/server/handler"

	"github.com/gin-gonic/gin"
)

type Server struct {
	server *http.Server
	router *gin.Engine
}

func NewServer() *Server {
	router := gin.Default()

	// 注册路由
	registerRoutes(router)

	return &Server{
		router: router,
		server: &http.Server{
			Addr:    ":8080",
			Handler: router,
		},
	}
}

func (s *Server) Start() error {
	return s.server.ListenAndServe()
}

func (s *Server) Stop(ctx context.Context) error {
	return s.server.Shutdown(ctx)
}

// SetESManager 设置ES管理器
func (s *Server) SetESManager(manager handler.ESManager) {
	handler.SetESManager(manager)
}
