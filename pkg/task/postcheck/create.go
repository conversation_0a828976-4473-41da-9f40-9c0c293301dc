package postcheck

import (
	"git.woa.com/kmetis/starship-engine/pkg/common"

	"k8s.io/klog/v2"
)

func (postcheck *PostCheckHandler) CreateTask(taskName, cluster, region string) error {
	if err := common.CreateTask(taskName, cluster, region, POSTCHECK); err != nil {
		return err
	}
	klog.V(2).InfoS("Creating postcheck task", "taskName", taskName, "cluster", cluster, "region", region)
	return nil
}

func (postcheck *PostCheckHandler) CreateTaskFromFile(fileName string) (string, error) {
	taskName, err := common.CreateTaskFromFile(fileName, POSTCHECK)
	if err != nil {
		klog.V(1).ErrorS(err, "Error processing postcheck file.")
		return "", err
	}
	klog.V(2).InfoS("Creating postcheck task from file", "fileName", fileName)
	return taskName, nil
}
