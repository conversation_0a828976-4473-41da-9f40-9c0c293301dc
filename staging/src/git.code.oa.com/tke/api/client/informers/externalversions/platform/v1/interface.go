/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by informer-gen. DO NOT EDIT.

package v1

import (
	internalinterfaces "git.code.oa.com/tke/api/client/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// AddonTypes returns a AddonTypeInformer.
	AddonTypes() AddonTypeInformer
	// CBSs returns a CBSInformer.
	CBSs() CBSInformer
	// CFSs returns a CFSInformer.
	CFSs() CFSInformer
	// COSs returns a COSInformer.
	COSs() COSInformer
	// Clusters returns a ClusterInformer.
	Clusters() ClusterInformer
	// ClusterAddonKinds returns a ClusterAddonKindInformer.
	ClusterAddonKinds() ClusterAddonKindInformer
	// ClusterAuthentications returns a ClusterAuthenticationInformer.
	ClusterAuthentications() ClusterAuthenticationInformer
	// ConfigMaps returns a ConfigMapInformer.
	ConfigMaps() ConfigMapInformer
	// DNSAutoscalers returns a DNSAutoscalerInformer.
	DNSAutoscalers() DNSAutoscalerInformer
	// DeSchedulers returns a DeSchedulerInformer.
	DeSchedulers() DeSchedulerInformer
	// DynamicSchedulers returns a DynamicSchedulerInformer.
	DynamicSchedulers() DynamicSchedulerInformer
	// EniIpamds returns a EniIpamdInformer.
	EniIpamds() EniIpamdInformer
	// Environments returns a EnvironmentInformer.
	Environments() EnvironmentInformer
	// GPUManagers returns a GPUManagerInformer.
	GPUManagers() GPUManagerInformer
	// GameApps returns a GameAppInformer.
	GameApps() GameAppInformer
	// HPCs returns a HPCInformer.
	HPCs() HPCInformer
	// Helms returns a HelmInformer.
	Helms() HelmInformer
	// ImageP2Ps returns a ImageP2PInformer.
	ImageP2Ps() ImageP2PInformer
	// LBCFs returns a LBCFInformer.
	LBCFs() LBCFInformer
	// LogCollectors returns a LogCollectorInformer.
	LogCollectors() LogCollectorInformer
	// NamespaceSets returns a NamespaceSetInformer.
	NamespaceSets() NamespaceSetInformer
	// NetworkPolicies returns a NetworkPolicyInformer.
	NetworkPolicies() NetworkPolicyInformer
	// NginxIngresses returns a NginxIngressInformer.
	NginxIngresses() NginxIngressInformer
	// NodeLocalDNSCaches returns a NodeLocalDNSCacheInformer.
	NodeLocalDNSCaches() NodeLocalDNSCacheInformer
	// NodeProblemDetectors returns a NodeProblemDetectorInformer.
	NodeProblemDetectors() NodeProblemDetectorInformer
	// OLMs returns a OLMInformer.
	OLMs() OLMInformer
	// OOMGuards returns a OOMGuardInformer.
	OOMGuards() OOMGuardInformer
	// PersistentEvents returns a PersistentEventInformer.
	PersistentEvents() PersistentEventInformer
	// Projects returns a ProjectInformer.
	Projects() ProjectInformer
	// QGPUs returns a QGPUInformer.
	QGPUs() QGPUInformer
	// Registries returns a RegistryInformer.
	Registries() RegistryInformer
	// Tcrs returns a TcrInformer.
	Tcrs() TcrInformer
}

type version struct {
	factory          internalinterfaces.SharedInformerFactory
	namespace        string
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory, namespace string, tweakListOptions internalinterfaces.TweakListOptionsFunc) Interface {
	return &version{factory: f, namespace: namespace, tweakListOptions: tweakListOptions}
}

// AddonTypes returns a AddonTypeInformer.
func (v *version) AddonTypes() AddonTypeInformer {
	return &addonTypeInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// CBSs returns a CBSInformer.
func (v *version) CBSs() CBSInformer {
	return &cBSInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// CFSs returns a CFSInformer.
func (v *version) CFSs() CFSInformer {
	return &cFSInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// COSs returns a COSInformer.
func (v *version) COSs() COSInformer {
	return &cOSInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// Clusters returns a ClusterInformer.
func (v *version) Clusters() ClusterInformer {
	return &clusterInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// ClusterAddonKinds returns a ClusterAddonKindInformer.
func (v *version) ClusterAddonKinds() ClusterAddonKindInformer {
	return &clusterAddonKindInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// ClusterAuthentications returns a ClusterAuthenticationInformer.
func (v *version) ClusterAuthentications() ClusterAuthenticationInformer {
	return &clusterAuthenticationInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// ConfigMaps returns a ConfigMapInformer.
func (v *version) ConfigMaps() ConfigMapInformer {
	return &configMapInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// DNSAutoscalers returns a DNSAutoscalerInformer.
func (v *version) DNSAutoscalers() DNSAutoscalerInformer {
	return &dNSAutoscalerInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// DeSchedulers returns a DeSchedulerInformer.
func (v *version) DeSchedulers() DeSchedulerInformer {
	return &deSchedulerInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// DynamicSchedulers returns a DynamicSchedulerInformer.
func (v *version) DynamicSchedulers() DynamicSchedulerInformer {
	return &dynamicSchedulerInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// EniIpamds returns a EniIpamdInformer.
func (v *version) EniIpamds() EniIpamdInformer {
	return &eniIpamdInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// Environments returns a EnvironmentInformer.
func (v *version) Environments() EnvironmentInformer {
	return &environmentInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// GPUManagers returns a GPUManagerInformer.
func (v *version) GPUManagers() GPUManagerInformer {
	return &gPUManagerInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// GameApps returns a GameAppInformer.
func (v *version) GameApps() GameAppInformer {
	return &gameAppInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// HPCs returns a HPCInformer.
func (v *version) HPCs() HPCInformer {
	return &hPCInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// Helms returns a HelmInformer.
func (v *version) Helms() HelmInformer {
	return &helmInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// ImageP2Ps returns a ImageP2PInformer.
func (v *version) ImageP2Ps() ImageP2PInformer {
	return &imageP2PInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// LBCFs returns a LBCFInformer.
func (v *version) LBCFs() LBCFInformer {
	return &lBCFInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// LogCollectors returns a LogCollectorInformer.
func (v *version) LogCollectors() LogCollectorInformer {
	return &logCollectorInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// NamespaceSets returns a NamespaceSetInformer.
func (v *version) NamespaceSets() NamespaceSetInformer {
	return &namespaceSetInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// NetworkPolicies returns a NetworkPolicyInformer.
func (v *version) NetworkPolicies() NetworkPolicyInformer {
	return &networkPolicyInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// NginxIngresses returns a NginxIngressInformer.
func (v *version) NginxIngresses() NginxIngressInformer {
	return &nginxIngressInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// NodeLocalDNSCaches returns a NodeLocalDNSCacheInformer.
func (v *version) NodeLocalDNSCaches() NodeLocalDNSCacheInformer {
	return &nodeLocalDNSCacheInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// NodeProblemDetectors returns a NodeProblemDetectorInformer.
func (v *version) NodeProblemDetectors() NodeProblemDetectorInformer {
	return &nodeProblemDetectorInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// OLMs returns a OLMInformer.
func (v *version) OLMs() OLMInformer {
	return &oLMInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// OOMGuards returns a OOMGuardInformer.
func (v *version) OOMGuards() OOMGuardInformer {
	return &oOMGuardInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// PersistentEvents returns a PersistentEventInformer.
func (v *version) PersistentEvents() PersistentEventInformer {
	return &persistentEventInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// Projects returns a ProjectInformer.
func (v *version) Projects() ProjectInformer {
	return &projectInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// QGPUs returns a QGPUInformer.
func (v *version) QGPUs() QGPUInformer {
	return &qGPUInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// Registries returns a RegistryInformer.
func (v *version) Registries() RegistryInformer {
	return &registryInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// Tcrs returns a TcrInformer.
func (v *version) Tcrs() TcrInformer {
	return &tcrInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}
