---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: starship-cli
  namespace: starship
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: starship-cli-config
  namespace: starship
data:
  users: |
    jacksontong,jaydenpliu,pikehuang,shang<PERSON>wu,yummy<PERSON><PERSON>,moonliwang,mervynwang,cicirxyang,firmlyzhu,misakazhou,zhikuodu,besseldong,pavleli,xdonggao,leonarliu,luis<PERSON>li<PERSON>,danielxxli,wylenwang,blake<PERSON>li,borger<PERSON>,fanjiankong,ericyyyang,raylhuang,seanyan,betaincao,bgbiaoxu,chestarchen,dianalli,emilwang,engowzhou,ericlezhang,fanzfzhang,finnfan,gordondeng,jansuzhang,jyonghong,keeganliu,kevinazhang,mervinwang,narujjzhang,russwu,sgrpzhou,yuehuazhang
  components: |
    etcd: tke-etcd-operator
    eklet: eklet
    scheduler: scheduler
    apiserver: apiserver
    controller-manager: controller-manager
    kube-proxy: kube-proxy
    user-addon: cbs,gatekeeper,cbs
    应用管理平台审批流: appfabricapplication
    autopilot-controller: autopilot-controller
    自研上云_后端_Coding审批: eks-pod,eklet
    kube-dns: coredns
    tcr-ccr: tcr-cert
    kstone-etcd-operator: kstone-tapp
    eklet-agent: eklet
  config.yaml: |
    db:
      dbHost: "************"
      dbPort: "3306"
      dbUser: "starship"
      dbPasswd: "K@yZ7CbheTxb5nVc6b%L"
      dbDatabase: "starship"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: starship-cli
  name: starship-cli
  namespace: starship
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: starship-cli
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: starship-cli
    spec:
      containers:
        - command:
            - sh
            - -c
            - while true; do sleep 3600; done
          image: mirrors.tencent.com/starship/starship-cli:v1.0.2
          imagePullPolicy: Always
          name: starship-cli
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 200Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
            - mountPath: /etc/config
              name: starship-cli-config
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccountName: starship-cli
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: tz-config
        - name: starship-cli-config
          configMap:
            name: starship-cli-config