/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeNetworkPolicies implements NetworkPolicyInterface
type FakeNetworkPolicies struct {
	Fake *FakePlatformV1
}

var networkpoliciesResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "networkpolicies"}

var networkpoliciesKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "NetworkPolicy"}

// Get takes name of the networkPolicy, and returns the corresponding networkPolicy object, and an error if there is any.
func (c *FakeNetworkPolicies) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.NetworkPolicy, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(networkpoliciesResource, name), &platformv1.NetworkPolicy{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NetworkPolicy), err
}

// List takes label and field selectors, and returns the list of NetworkPolicies that match those selectors.
func (c *FakeNetworkPolicies) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.NetworkPolicyList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(networkpoliciesResource, networkpoliciesKind, opts), &platformv1.NetworkPolicyList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.NetworkPolicyList{ListMeta: obj.(*platformv1.NetworkPolicyList).ListMeta}
	for _, item := range obj.(*platformv1.NetworkPolicyList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested networkPolicies.
func (c *FakeNetworkPolicies) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(networkpoliciesResource, opts))
}

// Create takes the representation of a networkPolicy and creates it.  Returns the server's representation of the networkPolicy, and an error, if there is any.
func (c *FakeNetworkPolicies) Create(ctx context.Context, networkPolicy *platformv1.NetworkPolicy, opts v1.CreateOptions) (result *platformv1.NetworkPolicy, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(networkpoliciesResource, networkPolicy), &platformv1.NetworkPolicy{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NetworkPolicy), err
}

// Update takes the representation of a networkPolicy and updates it. Returns the server's representation of the networkPolicy, and an error, if there is any.
func (c *FakeNetworkPolicies) Update(ctx context.Context, networkPolicy *platformv1.NetworkPolicy, opts v1.UpdateOptions) (result *platformv1.NetworkPolicy, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(networkpoliciesResource, networkPolicy), &platformv1.NetworkPolicy{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NetworkPolicy), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeNetworkPolicies) UpdateStatus(ctx context.Context, networkPolicy *platformv1.NetworkPolicy, opts v1.UpdateOptions) (*platformv1.NetworkPolicy, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(networkpoliciesResource, "status", networkPolicy), &platformv1.NetworkPolicy{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NetworkPolicy), err
}

// Delete takes name of the networkPolicy and deletes it. Returns an error if one occurs.
func (c *FakeNetworkPolicies) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(networkpoliciesResource, name), &platformv1.NetworkPolicy{})
	return err
}

// Patch applies the patch and returns the patched networkPolicy.
func (c *FakeNetworkPolicies) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.NetworkPolicy, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(networkpoliciesResource, name, pt, data, subresources...), &platformv1.NetworkPolicy{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.NetworkPolicy), err
}
