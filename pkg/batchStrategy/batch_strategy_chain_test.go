package batchStrategy

import (
	"fmt"
	"testing"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
)

func TestPodRegionStrategy(t *testing.T) {

	clusters := []string{
		"cls-13ji682f",
		"cls-1gh0tr7j",
		"cls-1rshi3dn",
		"cls-1vxtgp5p",
		"cls-201bw69f",
		"cls-23lfxl4t",
		"cls-085ggpyq",
		"cls-0b0bjuai",
		"cls-0j4u03vg",
		"cls-0j5vm2nw",
		"cls-009x5fzw",
		"cls-00roia0m",
		"cls-09h303b0",
		"cls-0bxebu5f",
		"cls-0jrr6rd3",
		"cls-181e6kz1",
		"cls-33mjfhk9",
		"cls-3mp354ip",
		"cls-0houuths",
		"cls-0sznf91w",
		"cls-0zup1cyc",
		"cls-1kv0a6my",
		"cls-1o67fy0u",
		"cls-04559gse",
		"cls-047c31y6",
		"cls-04brloyi",
		"cls-02cyln8n",
		"cls-0hiqw3nz",
		"cls-0y9rl6dh",
		"cls-1f0hngg3",
		"cls-25bssayx",
	}

	strategies := []BatchStrategy{
		//NewEksRunningPodsStrategy(),
		NewRegionBatchStrategy(),
	}

	chain := NewBatchStrategyChain(strategies...)
	batchConfigs, err := chain.Execute(clusters, BatchStrategyOptions{
		AppAction: consts.PRECHECK,
		AppName:   "eklet",
		BatchSize: 0,
	})
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	for _, batch := range batchConfigs {
		fmt.Printf("batch: %d, subbatch: %d, clusters: %v\n", batch.BatchId, batch.SubBatchId, batch.Clusters)
	}

}
