/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1beta1

import (
	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/portrait.k8s.io/v1beta1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// WorkloadCheckpointLister helps list WorkloadCheckpoints.
// All objects returned here must be treated as read-only.
type WorkloadCheckpointLister interface {
	// List lists all WorkloadCheckpoints in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1beta1.WorkloadCheckpoint, err error)
	// WorkloadCheckpoints returns an object that can list and get WorkloadCheckpoints.
	WorkloadCheckpoints(namespace string) WorkloadCheckpointNamespaceLister
	WorkloadCheckpointListerExpansion
}

// workloadCheckpointLister implements the WorkloadCheckpointLister interface.
type workloadCheckpointLister struct {
	indexer cache.Indexer
}

// NewWorkloadCheckpointLister returns a new WorkloadCheckpointLister.
func NewWorkloadCheckpointLister(indexer cache.Indexer) WorkloadCheckpointLister {
	return &workloadCheckpointLister{indexer: indexer}
}

// List lists all WorkloadCheckpoints in the indexer.
func (s *workloadCheckpointLister) List(selector labels.Selector) (ret []*v1beta1.WorkloadCheckpoint, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1beta1.WorkloadCheckpoint))
	})
	return ret, err
}

// WorkloadCheckpoints returns an object that can list and get WorkloadCheckpoints.
func (s *workloadCheckpointLister) WorkloadCheckpoints(namespace string) WorkloadCheckpointNamespaceLister {
	return workloadCheckpointNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// WorkloadCheckpointNamespaceLister helps list and get WorkloadCheckpoints.
// All objects returned here must be treated as read-only.
type WorkloadCheckpointNamespaceLister interface {
	// List lists all WorkloadCheckpoints in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1beta1.WorkloadCheckpoint, err error)
	// Get retrieves the WorkloadCheckpoint from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1beta1.WorkloadCheckpoint, error)
	WorkloadCheckpointNamespaceListerExpansion
}

// workloadCheckpointNamespaceLister implements the WorkloadCheckpointNamespaceLister
// interface.
type workloadCheckpointNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all WorkloadCheckpoints in the indexer for a given namespace.
func (s workloadCheckpointNamespaceLister) List(selector labels.Selector) (ret []*v1beta1.WorkloadCheckpoint, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1beta1.WorkloadCheckpoint))
	})
	return ret, err
}

// Get retrieves the WorkloadCheckpoint from the indexer for a given namespace and name.
func (s workloadCheckpointNamespaceLister) Get(name string) (*v1beta1.WorkloadCheckpoint, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1beta1.Resource("workloadcheckpoint"), name)
	}
	return obj.(*v1beta1.WorkloadCheckpoint), nil
}
