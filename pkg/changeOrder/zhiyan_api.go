package changeOrder

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/config"
	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/http_util"
	"git.woa.com/kmetis/starship-engine/pkg/util"

	"k8s.io/klog/v2"
)

func GetPostOrder(changeId string) (*GetPostOrderResponse, error) {
	// 获取配置
	zhiyanConfig := config.GetZhiyanChangeOrderConfig()

	requestBody := fmt.Sprintf(`{"id": %s}`, changeId)

	// 设置请求头
	header := http.Header{}
	header.Set("token", zhiyanConfig.Token)
	header.Set("projectname", zhiyanConfig.ProjectName)

	respBody, statusCode, err := http_util.PostRequest(zhiyanConfig.GetOrderUrl, []byte(requestBody), header, 5*time.Second)
	if err != nil {
		klog.Errorf("request zhiyan error: %v", err)
		return &GetPostOrderResponse{}, err
	}
	if statusCode != http.StatusOK {
		err = fmt.Errorf("request zhiyan error, request status code: %d", statusCode)
		klog.Errorf("%v", err)
		return &GetPostOrderResponse{}, err
	}
	klog.V(2).Info("zhiyan response: ", string(respBody))

	var response *GetPostOrderResponse
	err = json.Unmarshal(respBody, &response)
	if err != nil {
		klog.Errorf("unmarshal response error: %v", err)
		return &GetPostOrderResponse{}, err
	}

	return response, nil
}

// PostCalendarReport 调用上报变更日历接口，提交变更日历
func PostCalendarReport(calendarReportRequest CalendarReportRequest, userName string, options *CalendarReportOptions) error {
	// 1. 序列化变更日历数据
	requestBody, err := json.Marshal(calendarReportRequest)
	if err != nil {
		klog.Errorf("Failed to serialize calendar report data: %v", err)
		return fmt.Errorf("serialization of calendar data failed: %w", err)
	}

	// 2. 设置请求头
	header := http.Header{}
	header.Set("token", options.Token)
	header.Set("projectname", options.ProjectName)
	header.Set("staffname", userName)
	header.Set("Content-Type", "application/json; charset=utf-8")

	// 3. 确定上报URL
	reportUrl := options.ReportCalendarUrl
	if reportUrl == "" {
		zhiyanConfig := config.GetZhiyanChangeOrderConfig()
		reportUrl = zhiyanConfig.ReportCalendarUrl
	}

	klog.V(2).Infof("Reporting calendar to URL: %s, data: %s", reportUrl, string(requestBody))

	// 4. 发送POST请求，使用重试机制
	respBody, statusCode, err := http_util.PostRequest(reportUrl, requestBody, header, 5*time.Second)
	if err != nil {
		klog.Errorf("API request to change calendar failed: %v", err)
		return fmt.Errorf("change calendar API request failed: %w", err)
	}

	// 5. 检查响应状态码
	if statusCode != http.StatusOK {
		klog.Errorf("Change calendar API returned non-200 status code: %d, Response: %s", statusCode, string(respBody))
		return fmt.Errorf("change calendar API returned error status: %d", statusCode)
	}

	// 6. 解析响应
	var response struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	if err := json.Unmarshal(respBody, &response); err != nil {
		klog.Errorf("Failed to parse change calendar API response: %v, Raw response: %s", err, string(respBody))
		return fmt.Errorf("failed to parse API response: %w", err)
	}

	// 7. 检查业务码
	if response.Code != 0 {
		klog.Errorf("Calendar report returned non-zero business code: code=%d, message=%s, Request: %s", response.Code, response.Msg, string(requestBody))
		return fmt.Errorf("calendar report error: code=%d, message=%s", response.Code, response.Msg)
	}

	klog.V(2).Infof("Calendar report successful for staff: %s", userName)
	return nil
}

// ReportChangeCalendarWithExtension 上报变更日历的核心函数
func ReportChangeCalendarWithExtension(ctx context.Context, changeOrder *GetPostOrderResponse, cluster *db.TaskClusterInfo, status string, deployObjects []DeployObject, options *CalendarReportOptions) error {
	if changeOrder == nil {
		return fmt.Errorf("change order is nil")
	}

	if len(deployObjects) == 0 {
		return fmt.Errorf("no deploy objects provided")
	}

	// 验证变更对象数据
	for i, obj := range deployObjects {
		if obj.ObjectId == "" || obj.Type == "" {
			return fmt.Errorf("deploy object at index %d is incomplete", i)
		}
	}

	var addonVersion string
	if cluster.ImageTag != "" {
		addonVersion = cluster.ImageTag
	} else {
		addonVersion = cluster.AppVersion
	}

	// 准备扩展信息
	extension := map[string]interface{}{
		"product":      cluster.Type,
		"appID":        cluster.AppID,
		"region":       cluster.Region,
		"clusterId":    cluster.ClusterId,
		"addon":        cluster.Component,
		"addonVersion": addonVersion,
		"platform":     "starship",
		"projectName":  changeOrder.Data.Item.PlmName,
		"operator":     cluster.User,
		"type":         "upgrade",
		"status":       status,
		"endTime":      time.Now().Format("2006-01-02 15:04:05"),
	}

	extensionBytes, _ := json.Marshal(extension)

	// 准备上报数据
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	calendarReport := &CalendarReport{
		Platform:      getDefaultPlatform(changeOrder.Data.Item.PlatformType),
		FlowId:        changeOrder.Data.Item.ID,
		Operation:     "exec_once", // 单次执行操作
		Product:       getDefaultProduct(changeOrder.Data.Item.PlmName),
		OpDesc:        changeOrder.Data.Item.Title,
		Status:        status,
		DeployObjects: deployObjects,
		Operator:      cluster.User,
		ExecTime:      currentTime,
		DeployId:      generateTraceID(ctx),
		Area:          util.GetLongRegion(cluster.Region),
		Extension:     extensionBytes,
		OperationType: getDefaultOperationType(changeOrder.Data.Item.PostName),
	}

	request := CalendarReportRequest{
		Data: []*CalendarReport{calendarReport},
	}

	// 调用上报API
	if err := PostCalendarReport(request, cluster.User, options); err != nil {
		return fmt.Errorf("failed to report calendar: %w", err)
	}

	klog.Infof("Successfully reported change calendar for flow %s, status: %s, operator: %s", changeOrder.Data.Item.ID, status, cluster.User)
	return nil
}

// GetDefaultPlatform 获取默认平台标识（导出函数）
func GetDefaultPlatform(platformType string) string {
	if platformType != "" {
		return platformType
	}
	return "starship-engine"
}

// getDefaultPlatform 内部使用的函数
func getDefaultPlatform(platformType string) string {
	return GetDefaultPlatform(platformType)
}

// GetDefaultProduct 获取默认产品族（导出函数）
func GetDefaultProduct(plmName string) string {
	if plmName != "" {
		return plmName
	}
	return "容器服务 TKE"
}

// getDefaultProduct 内部使用的函数
func getDefaultProduct(plmName string) string {
	return GetDefaultProduct(plmName)
}

// GetDefaultOperationType 获取默认操作类型（导出函数）
func GetDefaultOperationType(postName string) string {
	if postName != "" {
		return postName
	}
	return "starship-engine-operation"
}

// getDefaultOperationType 内部使用的函数
func getDefaultOperationType(postName string) string {
	return GetDefaultOperationType(postName)
}

// GenerateTraceID 生成追踪ID（导出函数）
func GenerateTraceID(ctx context.Context) string {
	// 尝试从context中获取trace ID
	if ctx != nil {
		if traceID := ctx.Value("trace_id"); traceID != nil {
			if id, ok := traceID.(string); ok && id != "" {
				return id
			}
		}
	}

	// 生成默认的trace ID
	return fmt.Sprintf("starship-%d", time.Now().UnixNano())
}

// generateTraceID 内部使用的函数
func generateTraceID(ctx context.Context) string {
	return GenerateTraceID(ctx)
}

// CreateDeployObjectsFromClusters 从集群信息创建部署对象
func CreateDeployObjectsFromClusters(clusterIds []string, objectType string) []DeployObject {
	if objectType == "" {
		objectType = "cluster"
	}

	deployObjects := make([]DeployObject, 0, len(clusterIds))
	for _, clusterId := range clusterIds {
		if clusterId != "" {
			deployObjects = append(deployObjects, DeployObject{
				ObjectId: clusterId,
				Type:     objectType,
			})
		}
	}
	return deployObjects
}

// GetDefaultCalendarReportOptions 获取默认的变更日历上报配置
func GetDefaultCalendarReportOptions() *CalendarReportOptions {
	zhiyanConfig := config.GetZhiyanChangeOrderConfig()
	return &CalendarReportOptions{
		Token:             zhiyanConfig.Token,
		ProjectName:       zhiyanConfig.ProjectName,
		ReportCalendarUrl: zhiyanConfig.ReportCalendarUrl,
	}
}
