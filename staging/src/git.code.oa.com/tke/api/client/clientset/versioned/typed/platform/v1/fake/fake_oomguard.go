/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeOOMGuards implements OOMGuardInterface
type FakeOOMGuards struct {
	Fake *FakePlatformV1
}

var oomguardsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "oomguards"}

var oomguardsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "OOMGuard"}

// Get takes name of the oOMGuard, and returns the corresponding oOMGuard object, and an error if there is any.
func (c *FakeOOMGuards) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.OOMGuard, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(oomguardsResource, name), &platformv1.OOMGuard{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OOMGuard), err
}

// List takes label and field selectors, and returns the list of OOMGuards that match those selectors.
func (c *FakeOOMGuards) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.OOMGuardList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(oomguardsResource, oomguardsKind, opts), &platformv1.OOMGuardList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.OOMGuardList{ListMeta: obj.(*platformv1.OOMGuardList).ListMeta}
	for _, item := range obj.(*platformv1.OOMGuardList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested oOMGuards.
func (c *FakeOOMGuards) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(oomguardsResource, opts))
}

// Create takes the representation of a oOMGuard and creates it.  Returns the server's representation of the oOMGuard, and an error, if there is any.
func (c *FakeOOMGuards) Create(ctx context.Context, oOMGuard *platformv1.OOMGuard, opts v1.CreateOptions) (result *platformv1.OOMGuard, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(oomguardsResource, oOMGuard), &platformv1.OOMGuard{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OOMGuard), err
}

// Update takes the representation of a oOMGuard and updates it. Returns the server's representation of the oOMGuard, and an error, if there is any.
func (c *FakeOOMGuards) Update(ctx context.Context, oOMGuard *platformv1.OOMGuard, opts v1.UpdateOptions) (result *platformv1.OOMGuard, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(oomguardsResource, oOMGuard), &platformv1.OOMGuard{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OOMGuard), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeOOMGuards) UpdateStatus(ctx context.Context, oOMGuard *platformv1.OOMGuard, opts v1.UpdateOptions) (*platformv1.OOMGuard, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(oomguardsResource, "status", oOMGuard), &platformv1.OOMGuard{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OOMGuard), err
}

// Delete takes name of the oOMGuard and deletes it. Returns an error if one occurs.
func (c *FakeOOMGuards) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(oomguardsResource, name), &platformv1.OOMGuard{})
	return err
}

// Patch applies the patch and returns the patched oOMGuard.
func (c *FakeOOMGuards) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.OOMGuard, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(oomguardsResource, name, pt, data, subresources...), &platformv1.OOMGuard{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OOMGuard), err
}
