package util

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"git.woa.com/kmetis/starship-engine/pkg/config"
	"github.com/tencentyun/cos-go-sdk-v5"
	"k8s.io/klog/v2"
)

// COSConfig COS 配置结构体
type COSConfig struct {
	BucketURL string
	SecretID  string
	SecretKey string
}

// COSClient COS 客户端封装
type COSClient struct {
	client *cos.Client
	config COSConfig
}

// NewCOSClient 创建新的 COS 客户端
func NewCOSClient(config COSConfig) (*COSClient, error) {
	if config.BucketURL == "" {
		return nil, fmt.Errorf("bucket URL cannot be empty")
	}
	if config.SecretID == "" {
		return nil, fmt.Errorf("secret ID cannot be empty")
	}
	if config.SecretKey == "" {
		return nil, fmt.Errorf("secret key cannot be empty")
	}

	u, err := url.Parse(config.BucketURL)
	if err != nil {
		return nil, fmt.Errorf("invalid bucket URL: %v", err)
	}

	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.SecretID,
			SecretKey: config.SecretKey,
		},
	})

	return &COSClient{
		client: client,
		config: config,
	}, nil
}

// GetFileContent 从 COS 桶获取文件内容
func (c *COSClient) GetFileContent(fileName string) (string, error) {
	if fileName == "" {
		return "", fmt.Errorf("file name cannot be empty")
	}

	// 确保文件名不以斜杠开头
	key := strings.TrimPrefix(fileName, "/")

	klog.V(2).InfoS("Getting file content from COS", "fileName", fileName, "key", key)

	// 获取文件内容
	resp, err := c.client.Object.Get(context.Background(), key, nil)
	if err != nil {
		klog.ErrorS(err, "Failed to get file from COS", "fileName", fileName, "key", key)
		return "", fmt.Errorf("failed to get file from COS: %v", err)
	}
	defer resp.Body.Close()

	// 读取文件内容
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		klog.ErrorS(err, "Failed to read file content", "fileName", fileName)
		return "", fmt.Errorf("failed to read file content: %v", err)
	}

	klog.V(2).InfoS("Successfully retrieved file content from COS", "fileName", fileName, "contentLength", len(content))
	return string(content), nil
}

// DownloadFile 从 COS 桶下载文件到本地
func (c *COSClient) DownloadFile(fileName, localPath string) error {
	if fileName == "" {
		return fmt.Errorf("file name cannot be empty")
	}
	if localPath == "" {
		return fmt.Errorf("local path cannot be empty")
	}

	// 确保文件名不以斜杠开头
	key := strings.TrimPrefix(fileName, "/")

	klog.V(2).InfoS("Downloading file from COS", "fileName", fileName, "localPath", localPath)

	opt := &cos.MultiDownloadOptions{
		ThreadPoolSize: 5,
	}

	_, err := c.client.Object.Download(context.Background(), key, localPath, opt)
	if err != nil {
		klog.ErrorS(err, "Failed to download file from COS", "fileName", fileName, "localPath", localPath)
		return fmt.Errorf("failed to download file from COS: %v", err)
	}

	klog.V(2).InfoS("Successfully downloaded file from COS", "fileName", fileName, "localPath", localPath)
	return nil
}

// GetDefaultCOSClient 获取默认配置的 COS 客户端
// 使用统一的配置系统
func GetDefaultCOSClient() (*COSClient, error) {
	// 从统一配置系统获取COS配置
	cosConfig := config.GetCOSConfig()

	klog.V(2).InfoS("Loaded COS config from unified config system")

	// 转换为本地COSConfig结构体
	localConfig := COSConfig{
		BucketURL: cosConfig.BucketURL,
		SecretID:  cosConfig.SecretID,
		SecretKey: cosConfig.SecretKey,
	}

	return NewCOSClient(localConfig)
}

// GetClusterFileContentFromCOS 从 COS 获取集群文件内容的便捷函数
func GetClusterFileContentFromCOS(fileName string) (string, error) {
	if fileName == "" {
		return "", fmt.Errorf("file name cannot be empty")
	}

	client, err := GetDefaultCOSClient()
	if err != nil {
		return "", fmt.Errorf("failed to create COS client: %v", err)
	}

	// 如果文件名不包含路径前缀，添加默认的 starship-engine 前缀
	key := fileName
	if !strings.Contains(fileName, "/") {
		key = "starship-engine/" + fileName
	}

	content, err := client.GetFileContent(key)
	if err != nil {
		return "", fmt.Errorf("failed to get cluster file content: %v", err)
	}

	return content, nil
}
