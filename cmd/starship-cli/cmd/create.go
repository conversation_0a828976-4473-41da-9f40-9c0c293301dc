package cmd

import (
	"fmt"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/task"
	"git.woa.com/kmetis/starship-engine/pkg/task/postcheck"
	"git.woa.com/kmetis/starship-engine/pkg/task/precheck"
	"git.woa.com/kmetis/starship-engine/pkg/task/publish"

	"github.com/spf13/cobra"
)

func NewCreateCommand() *cobra.Command {
	createCmd := &cobra.Command{
		Use:   "create",
		Short: "Create a new task",
		Long:  "Create a new task, such as publish, precheck, or postcheck.",
	}

	createCmd.AddCommand(NewCreateTaskCommand(consts.PUBLISH, &publish.PublishHandler{}, "Create a publish task from a YAML file."))
	createCmd.AddCommand(NewCreateTaskCommand(consts.PRECHECK, &precheck.PreCheckHandler{}, "Create a precheck task from a YAML file."))
	createCmd.AddCommand(NewCreateTaskCommand(consts.POSTCHECK, &postcheck.PostCheckHandler{}, "Create a postcheck task from a YAML file."))

	return createCmd
}

func NewCreateTaskCommand(taskType string, handler task.TaskHandler, description string) *cobra.Command {
	var fileName string
	cmd := &cobra.Command{
		Use:   taskType,
		Short: fmt.Sprintf("Create a %s task", taskType),
		Long:  description,
		Run: func(cmd *cobra.Command, args []string) {
			if fileName == "" {
				fmt.Println("Invalid input. Use -f to specify the file name.")
				return
			}
			taskName, err := handler.CreateTaskFromFile(fileName)
			if err != nil {
				klog.V(1).ErrorS(err, "Error creating task from file")
				fmt.Println(err)
			} else {
				klog.V(2).InfoS(taskType, "task created successfully", "fileName", fileName)
				fmt.Printf("%s task %s created successfully from %s\n", taskType, taskName, fileName)
			}
		},
	}
	cmd.Flags().StringVarP(&fileName, "file", "f", "", "File name containing cluster information")
	return cmd
}
