package db

import (
	"fmt"
	"time"
)

// TaskClusterInfo 结构体用于存储 JOIN 操作的结果
type TaskClusterInfo struct {
	ID               int64     `gorm:"column:id"`
	TaskId           int64     `gorm:"column:task_id"`
	BatchId          int64     `gorm:"column:batch_id"`
	SubBatchId       int64     `gorm:"column:sub_batch_id"`
	AppAction        string    `gorm:"column:app_action"`
	Component        string    `gorm:"column:component"`
	ImageTag         string    `gorm:"column:image_tag"`
	AppVersion       string    `gorm:"column:app_version"`
	PluginVersion    string    `gorm:"column:plugin_version"`
	ExtendInfo       string    `gorm:"column:extend_info"`
	User             string    `gorm:"column:user"`
	ChangeId         string    `gorm:"column:change_id"`
	Token            string    `gorm:"column:token"`
	ClusterId        string    `gorm:"column:cluster_id"`
	Region           string    `gorm:"column:region"`
	MetaID           string    `gorm:"column:meta_id"`
	Type             string    `gorm:"column:type"`
	NameSpace        string    `gorm:"column:namespace"`
	Name             string    `gorm:"column:name"`
	AppID            uint64    `gorm:"column:app_id"`
	UIN              string    `gorm:"column:uin"`
	SubUIN           string    `gorm:"column:sub_uin"`
	StarshipTaskId   string    `gorm:"column:starship_task_id"`
	StarshipChangeId string    `gorm:"column:starship_change_id"`
	Stage            string    `gorm:"column:stage"`
	Status           string    `gorm:"column:status"`
	Reason           string    `gorm:"column:reason"`
	CreateTime       time.Time `gorm:"column:create_time"`
}

func (tci TaskClusterInfo) String() string {
	return fmt.Sprintf("TaskClusterInfo{ID: %d, TaskId: %d, BatchId: %d, SubBatchId: %d, AppAction: %s, Component: %s, ImageTag: %s, "+
		"AppVersion: %s, PluginVersion: %s, ExtendInfo: %s, User: %s, ChangeId: %s, Token: %s, ClusterId: %s, "+
		"Region: %s, MetaID: %s, Type: %s, AppID: %d, UIN: %s, SubUIN: %s, StarshipTaskId: %s, StarshipChangeId: %s, Status: %s}",
		tci.ID, tci.TaskId, tci.BatchId, tci.SubBatchId, tci.AppAction, tci.Component, tci.ImageTag, tci.AppVersion, tci.PluginVersion, tci.ExtendInfo, tci.User, tci.ChangeId,
		tci.Token, tci.ClusterId, tci.Region, tci.MetaID, tci.Type, tci.AppID, tci.UIN, tci.SubUIN, tci.StarshipTaskId, tci.StarshipChangeId, tci.Status)
}

func (tci *TaskClusterInfo) TableName() string {
	return ""
}
