/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// ClusterAuthenticationsGetter has a method to return a ClusterAuthenticationInterface.
// A group's client should implement this interface.
type ClusterAuthenticationsGetter interface {
	ClusterAuthentications(namespace string) ClusterAuthenticationInterface
}

// ClusterAuthenticationInterface has methods to work with ClusterAuthentication resources.
type ClusterAuthenticationInterface interface {
	Create(ctx context.Context, clusterAuthentication *v1.ClusterAuthentication, opts metav1.CreateOptions) (*v1.ClusterAuthentication, error)
	Update(ctx context.Context, clusterAuthentication *v1.ClusterAuthentication, opts metav1.UpdateOptions) (*v1.ClusterAuthentication, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.ClusterAuthentication, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.ClusterAuthenticationList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.ClusterAuthentication, err error)
	ClusterAuthenticationExpansion
}

// clusterAuthentications implements ClusterAuthenticationInterface
type clusterAuthentications struct {
	client rest.Interface
	ns     string
}

// newClusterAuthentications returns a ClusterAuthentications
func newClusterAuthentications(c *PlatformV1Client, namespace string) *clusterAuthentications {
	return &clusterAuthentications{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the clusterAuthentication, and returns the corresponding clusterAuthentication object, and an error if there is any.
func (c *clusterAuthentications) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.ClusterAuthentication, err error) {
	result = &v1.ClusterAuthentication{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("clusterauthentications").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of ClusterAuthentications that match those selectors.
func (c *clusterAuthentications) List(ctx context.Context, opts metav1.ListOptions) (result *v1.ClusterAuthenticationList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.ClusterAuthenticationList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("clusterauthentications").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested clusterAuthentications.
func (c *clusterAuthentications) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("clusterauthentications").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a clusterAuthentication and creates it.  Returns the server's representation of the clusterAuthentication, and an error, if there is any.
func (c *clusterAuthentications) Create(ctx context.Context, clusterAuthentication *v1.ClusterAuthentication, opts metav1.CreateOptions) (result *v1.ClusterAuthentication, err error) {
	result = &v1.ClusterAuthentication{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("clusterauthentications").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(clusterAuthentication).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a clusterAuthentication and updates it. Returns the server's representation of the clusterAuthentication, and an error, if there is any.
func (c *clusterAuthentications) Update(ctx context.Context, clusterAuthentication *v1.ClusterAuthentication, opts metav1.UpdateOptions) (result *v1.ClusterAuthentication, err error) {
	result = &v1.ClusterAuthentication{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("clusterauthentications").
		Name(clusterAuthentication.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(clusterAuthentication).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the clusterAuthentication and deletes it. Returns an error if one occurs.
func (c *clusterAuthentications) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("clusterauthentications").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched clusterAuthentication.
func (c *clusterAuthentications) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.ClusterAuthentication, err error) {
	result = &v1.ClusterAuthentication{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("clusterauthentications").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
