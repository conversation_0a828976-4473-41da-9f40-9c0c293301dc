package changeOrder

// GetPostOrderResponse 整体响应结构体
type GetPostOrderResponse struct {
	Code int              `json:"code,omitempty"`
	Data GetPostOrderData `json:"data,omitempty"`
	Msg  string           `json:"msg,omitempty"`
}

// GetPostOrderData 数据部分结构体，包含 item
type GetPostOrderData struct {
	Item Item `json:"item,omitempty"`
}

// Item 包含多个子数据的结构体
type Item struct {
	ID                       string     `json:"id,omitempty"`
	ProjectID                string     `json:"project_id,omitempty"`
	ProjectName              string     `json:"project_name,omitempty"`
	Title                    string     `json:"title,omitempty"`
	App                      []App      `json:"app,omitempty"`
	PostType                 string     `json:"post_type,omitempty"`
	PostName                 string     `json:"post_name,omitempty"`
	Applicant                []string   `json:"applicant,omitempty"`
	PostPlatform             string     `json:"post_platform,omitempty"`
	PlatformType             string     `json:"platform_type,omitempty"`
	Remarks                  string     `json:"remarks,omitempty"`
	TaskID                   string     `json:"task_id,omitempty"`
	TemplateID               string     `json:"template_id,omitempty"`
	TemplateEn               string     `json:"template_en,omitempty"`
	Data                     string     `json:"data,omitempty"`
	Status                   string     `json:"status,omitempty"`
	CreatedAt                string     `json:"created_at,omitempty"`
	UpdatedAt                string     `json:"updated_at,omitempty"`
	System                   string     `json:"system,omitempty"`
	SystemName               string     `json:"system_name,omitempty"`
	Source                   string     `json:"source,omitempty"`
	FlowType                 int        `json:"flow_type,omitempty"`
	TaskCount                string     `json:"task_count,omitempty"`
	TimeWindow               TimeWindow `json:"time_window,omitempty"`
	PlmID                    string     `json:"plm_id,omitempty"`
	PlmName                  string     `json:"plm_name,omitempty"`
	PlmApp                   []PlmApp   `json:"plm_app,omitempty"`
	Creator                  string     `json:"creator,omitempty"`
	SkipUrl                  string     `json:"skip_url,omitempty"`
	Region                   []string   `json:"region,omitempty"`
	Task                     Task       `json:"task,omitempty"`
	StepID                   string     `json:"step_id,omitempty"`
	Informer                 []string   `json:"informer,omitempty"`
	Info                     []Info     `json:"info,omitempty"`
	ReleaseServerApplication string     `json:"release_server_application,omitempty"`
	RiskLevel                string     `json:"risk_level,omitempty"`
	TapdUrl                  string     `json:"tapd_url,omitempty"`
	CheckFlag                int        `json:"checkFlag,omitempty"`
}

// App 应用结构体
type App struct {
	AppID   string `json:"app_id,omitempty"`
	AppName string `json:"app_name,omitempty"`
}

// TimeWindow 时间窗口结构体
type TimeWindow struct {
	StartTime string `json:"start_time,omitempty"`
	EndTime   string `json:"end_time,omitempty"`
}

// Info 信息结构体
type Info struct {
	CallbackURL        string `json:"callback_url,omitempty"`
	CreateAt           string `json:"create_at,omitempty"`
	Creator            string `json:"creator,omitempty"`
	EnvID              int    `json:"env_id,omitempty"`
	EnvName            string `json:"env_name,omitempty"`
	JobSheetInstanceID string `json:"job_sheet_instance_id,omitempty"`
	PostName           string `json:"post_name,omitempty"`
	PostType           string `json:"post_type,omitempty"`
	Self               bool   `json:"self,omitempty"`
	SkipURL            string `json:"skip_url,omitempty"`
	System             string `json:"system,omitempty"`
	SystemName         string `json:"system_name,omitempty"`
	Title              string `json:"title,omitempty"`
}

// PlmApp PLM 应用结构体
type PlmApp struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// Task 任务结构体
type Task struct {
	ID                  int           `json:"id,omitempty"`
	FlowID              string        `json:"flow_id,omitempty"`
	JobSheetInstanceID  string        `json:"job_sheet_instance_id,omitempty"`
	JobSheetName        string        `json:"job_sheet_name,omitempty"`
	System              string        `json:"system,omitempty"`
	SystemName          string        `json:"system_name,omitempty"`
	ApprovalType        int           `json:"approval_type,omitempty"`
	Applicant           string        `json:"applicant,omitempty"`
	Variable            string        `json:"variable,omitempty"`
	ApprovalStatus      int           `json:"approval_status,omitempty"`
	CallbackURL         string        `json:"callback_url,omitempty"`
	CurStep             string        `json:"cur_step,omitempty"`
	Approver            []string      `json:"approver,omitempty"`
	DetailFields        string        `json:"detail_fields,omitempty"`
	CreateTime          string        `json:"create_time,omitempty"`
	UpdateTime          string        `json:"update_time,omitempty"`
	HistoryApprover     []string      `json:"history_approver,omitempty"`
	AdminApprover       []string      `json:"admin_approver,omitempty"`
	CurStepInfo         []CurStepInfo `json:"cur_step_info,omitempty"`
	NeedCurUserApprove  bool          `json:"need_cur_user_approve,omitempty"`
	DetailFieldsMapping string        `json:"detail_fields_mapping,omitempty"`
	DisplayFields       []string      `json:"display_fields,omitempty"`
	IsParallel          bool          `json:"is_parallel,omitempty"`
	CustomID            string        `json:"custom_id,omitempty"`
	Step                []Step        `json:"step,omitempty"`
	SkipURL             string        `json:"skip_url,omitempty"`
	Description         string        `json:"description,omitempty"`
	Project             string        `json:"project,omitempty"`
	ParentID            int           `json:"parent_id,omitempty"`
	AttachedCallbackURL []string      `json:"attached_callback_url,omitempty"`
	OnlyEndResultNotify bool          `json:"only_end_result_notify,omitempty"`
	Banself             bool          `json:"banself,omitempty"`
	BanselfTips         string        `json:"banself_tips,omitempty"`
}

// CurStepInfo 当前步骤信息结构体
type CurStepInfo struct {
	AdminApprover   []string `json:"admin_approver,omitempty"`
	Approver        []string `json:"approver,omitempty"`
	CurStep         string   `json:"cur_step,omitempty"`
	HistoryApprover []string `json:"history_approver,omitempty"`
}

// Step 步骤结构体
type Step struct {
	ID            string         `json:"id,omitempty"`
	Name          string         `json:"name,omitempty"`
	Type          string         `json:"type,omitempty"`
	Approver      string         `json:"approver,omitempty"`
	AdminApprover string         `json:"admin_approver,omitempty"`
	CompleteType  int            `json:"complete_type,omitempty"`
	Status        int            `json:"status,omitempty"`
	PostTime      string         `json:"post_time,omitempty"`
	Summary       string         `json:"summary,omitempty"`
	User          string         `json:"user,omitempty"`
	IsSkipStep    bool           `json:"is_skip_step,omitempty"`
	StepApprover  []StepApprover `json:"step_approver,omitempty"`
}

// StepApprover 步骤审批人结构体
type StepApprover struct {
	User     string `json:"user,omitempty"`
	Status   int    `json:"status,omitempty"`
	PostTime string `json:"post_time,omitempty"`
	Summary  string `json:"summary,omitempty"`
}

// CalendarReport 变更日历上报数据结构
type CalendarReport struct {
	Platform      string         `json:"Platform"`      // 平台标识
	FlowId        string         `json:"FlowId"`        // 流程ID
	Operation     string         `json:"Operation"`     // exec_start/exec_end/exec_once
	Product       string         `json:"Product"`       // 产品族
	OpDesc        string         `json:"OpDesc"`        // 操作描述
	Status        string         `json:"Status"`        // success/failure/timeout/ignore
	DeployObjects []DeployObject `json:"DeployObject"`  // 部署对象数组
	Operator      string         `json:"Operator"`      // 操作人
	ExecTime      string         `json:"ExecTime"`      // 执行时间
	DeployId      string         `json:"DeployId"`      // 变更任务ID
	Area          string         `json:"Area"`          // 地域
	Extension     []byte         `json:"Extension"`     // 扩展信息
	OperationType string         `json:"OperationType"` // 变更操作类型
}

// DeployObject 部署对象信息
type DeployObject struct {
	ObjectId string `json:"ObjectId"` // 实例ID/IP/名称
	Type     string `json:"Type"`     // 实例类型
}

// CalendarReportRequest 变更日历上报请求
type CalendarReportRequest struct {
	Data []*CalendarReport `json:"data"`
}

// CalendarReportOptions 变更日历上报配置选项
type CalendarReportOptions struct {
	Token             string
	ProjectName       string
	ReportCalendarUrl string
}
