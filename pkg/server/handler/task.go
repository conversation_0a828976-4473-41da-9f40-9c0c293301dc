package handler

import (
	"fmt"
	"net/http"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/api/v1/common"
	"git.woa.com/kmetis/starship-engine/api/v1/task"
	"git.woa.com/kmetis/starship-engine/pkg/changeOrder"
	taskCommon "git.woa.com/kmetis/starship-engine/pkg/common"
	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/util"
)

func ListTasks(c *gin.Context) {
	var req task.ListRequest
	if err := c.ShouldBind<PERSON>uery(&req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 参数校验
	if err := req.Validate(); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 计算分页参数
	offset := (req.PageNum - 1) * req.PageSize
	limit := req.PageSize

	// 从数据库查询数据
	tasks, total, err := db.GetTaskList(req, offset, limit)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	data := task.ListData{
		Items:    tasks,
		Total:    total,
		Page:     req.PageNum,
		PageSize: req.PageSize,
	}
	c.JSON(http.StatusOK, common.NewSuccess(data))
}

func CreateTask(c *gin.Context) {
	var req task.CreateTaskRequest

	// 使用 Gin 的 ShouldBind 自动根据 Content-Type 选择绑定方式
	// 支持 application/json, application/x-www-form-urlencoded, multipart/form-data
	if err := c.ShouldBind(&req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 处理 COS 文件，从文件名获取文件内容
	var clusterFileContent string
	var clusterFileName string
	if req.ClusterFileName != "" {
		clusterFileName = strings.TrimSpace(req.ClusterFileName)
		content, err := util.GetClusterFileContentFromCOS(clusterFileName)
		if err != nil {
			handleError(c, http.StatusBadRequest, fmt.Errorf("failed to get file content from COS: %v", err))
			return
		}
		clusterFileContent = content
	}

	// 构建Task对象
	taskModel := model.Task{
		UserData: model.UserData{
			User:     req.User,
			ChangeId: req.ChangeID,
		},
		Component: model.Component{
			Name:       req.Component,
			ImageTag:   strings.TrimSpace(req.ImageTag),
			AppVersion: strings.TrimSpace(req.AppVersion),
			ExtendInfo: req.ExtendInfo,
		},
		Description:        req.Description,
		OrchestrationMode:  *req.OrchestrationMode,
		Cluster:            req.Clusters,
		ClusterFileContent: clusterFileContent, // 从 COS 获取的文件内容
		ClusterType:        req.ClusterType,
		ClusterOption:      req.ClusterCondition,
		BatchStrategies:    req.BatchStrategies,
		AppBatchStrategy:   req.AppBatchStrategy,
		AutoConfirm:        req.AutoConfirm,
	}
	// 模式1处理逻辑
	if *req.OrchestrationMode == 1 {
		// 直接使用UploadClusters中的集群信息
		if err := taskCommon.CreateTaskFromRequest(&taskModel, req.AppAction); err != nil {
			handleError(c, http.StatusInternalServerError, err)
			return
		}
	} else {
		// 编排模式仅允许apiserver/scheduler/controller-manager组件发布
		if req.Component != "scheduler" && req.Component != "apiserver" && req.Component != "controller-manager" && req.Component != "eklet" {
			handleError(c, http.StatusBadRequest, fmt.Errorf("invalid component: only support scheduler/apiserver/controller-manager"))
			return
		}
		// 模式2处理逻辑（原有逻辑）
		if err := taskCommon.CreateTaskByClusterOptions(&taskModel, req.AppAction); err != nil {
			handleError(c, http.StatusInternalServerError, err)
			return
		}
	}

	data := task.CreateData{
		TaskName: taskModel.Name,
	}
	c.JSON(http.StatusOK, common.NewSuccess(data))
}

func UpdateTaskStatus(c *gin.Context) {
	var req task.UpdateTaskRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}
	if err := req.Validate(); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 判断当前用户是否是实施人
	changeOrder, err := changeOrder.GetPostOrder(req.ChangeID)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}
	applicants := changeOrder.Data.Item.Applicant
	isApplicant := util.Contains(applicants, req.User)
	if !isApplicant {
		handleError(c, http.StatusForbidden, fmt.Errorf("user is not applicant"))
		return
	}

	if err := db.UpdateStatusByName(req.AppAction, req.TaskName, req.Status); err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	data := task.UpdateData{
		TaskName: req.TaskName,
		Status:   req.Status,
	}
	c.JSON(http.StatusOK, common.NewSuccess(data))
}

func BatchTerminateTasks(c *gin.Context) {
	var req task.BatchTerminateTasksRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}
	if err := req.Validate(); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 验证用户权限 - 需要检查每个任务的权限
	var validTaskIds []int64
	var unauthorizedTaskNames []string

	for _, taskId := range req.TaskIds {
		// 获取任务信息
		taskInfo, err := db.GetTaskByTaskId(taskId)
		if err != nil {
			klog.Errorf("获取任务失败 %d: %v", taskId, err)
			continue
		}

		// 判断当前用户是否是实施人
		order, err := changeOrder.GetPostOrder(taskInfo.ChangeId)
		if err != nil {
			klog.Errorf("获取变更单失败 %s: %v", taskInfo.TaskName, err)
			continue
		}

		applicants := order.Data.Item.Applicant
		isApplicant := util.Contains(applicants, req.User)
		if isApplicant {
			validTaskIds = append(validTaskIds, taskId)
		} else {
			unauthorizedTaskNames = append(unauthorizedTaskNames, taskInfo.TaskName)
		}
	}

	if len(unauthorizedTaskNames) > 0 {
		handleError(c, http.StatusForbidden, fmt.Errorf("用户无权限操作以下任务: %v", unauthorizedTaskNames))
		return
	}

	if len(validTaskIds) == 0 {
		handleError(c, http.StatusBadRequest, fmt.Errorf("不存在可操作的任务"))
		return
	}

	// 执行批量终止
	result, err := db.BatchTerminateTasksByIds(validTaskIds)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	// 转换结果格式
	responseResults := make([]task.TaskTerminateResult, len(result.Results))
	for i, r := range result.Results {
		responseResults[i] = task.TaskTerminateResult{
			TaskId:   r.TaskId,
			TaskName: r.TaskName,
			Status:   r.Status,
			Message:  r.Message,
		}
	}

	data := task.BatchTerminateData{
		SuccessCount: result.SuccessCount,
		SkippedCount: result.SkippedCount,
		FailedCount:  result.FailedCount,
		Results:      responseResults,
	}

	c.JSON(http.StatusOK, common.NewSuccess(data))
}

// GetTaskBatchByTaskName 根据任务名称获取批次详情
func GetTaskBatchByTaskName(c *gin.Context) {
	var req struct {
		TaskName string `form:"task_name" binding:"required"`
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	batchDetails, taskInfo, err := db.GetBatchDetailsAndTaskInfoByTaskName(req.TaskName)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	batchMap := make(map[int64]gin.H)
	for _, detail := range batchDetails {
		batch, exists := batchMap[detail.BatchId]
		if !exists {
			batch = gin.H{
				"batch_id":    detail.BatchId,
				"batch_name":  util.GetBatchNameByType(detail.BatchId, int64(taskInfo.BatchType), detail.Region),
				"sub_batches": []gin.H{},
			}
			batchMap[detail.BatchId] = batch
		}
		batch["sub_batches"] = append(batch["sub_batches"].([]gin.H), gin.H{
			"sub_batch_id": detail.SubBatchId,
			"region":       detail.Region,
			"region_cn":    util.GetRegionCNName(detail.Region),
			"status":       detail.Status,
		})
	}

	batchIDs := make([]int64, 0, len(batchMap))
	for id := range batchMap {
		batchIDs = append(batchIDs, id)
	}
	sort.Slice(batchIDs, func(i, j int) bool { return batchIDs[i] < batchIDs[j] })

	batches := make([]gin.H, 0, len(batchMap))
	for _, id := range batchIDs {
		batches = append(batches, batchMap[id])
	}

	order, err := changeOrder.GetPostOrder(taskInfo.ChangeId)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	data := gin.H{
		"task_name":   req.TaskName,
		"component":   taskInfo.Component,
		"user":        taskInfo.User,
		"name":        order.Data.Item.Title,
		"image_tag":   taskInfo.ImageTag,
		"app_version": taskInfo.AppVersion,
		"applicants":  order.Data.Item.Applicant,
		"time_window": gin.H{
			"start_time": order.Data.Item.TimeWindow.StartTime,
			"end_time":   order.Data.Item.TimeWindow.EndTime,
		},
		"extend_info":     taskInfo.ExtendInfo,
		"batch_type":      taskInfo.BatchType,
		"cluster_options": taskInfo.ClusterOption,
		"batches":         batches,
		"status":          taskInfo.Status,
	}
	c.JSON(http.StatusOK, common.NewSuccess(data))
}
