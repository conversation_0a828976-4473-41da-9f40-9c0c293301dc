package precheck

import (
	"git.woa.com/kmetis/starship-engine/pkg/common"

	"k8s.io/klog/v2"
)

func (precheck *PreCheckHandler) CreateTask(taskName, cluster, region string) error {
	if err := common.CreateTask(taskName, cluster, region, PRECHECK); err != nil {
		return err
	}
	klog.V(2).InfoS("Creating precheck task", "taskName", taskName, "cluster", cluster, "region", region)
	return nil
}

func (precheck *PreCheckHandler) CreateTaskFromFile(fileName string) (string, error) {
	taskName, err := common.CreateTaskFromFile(fileName, PRECHECK)
	if err != nil {
		klog.V(1).ErrorS(err, "Error processing precheck file.")
		return "", err
	}
	klog.V(2).InfoS("Creating precheck task from file", "fileName", fileName)
	return taskName, nil
}
