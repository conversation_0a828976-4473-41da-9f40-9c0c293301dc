package ruleengine

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/util"
)

var userFuncMap = map[string]interface{}{
	"timeBefore": func(args ...interface{}) (bool, error) {
		if len(args) != 2 {
			return false, fmt.Errorf("timeCompare: args length is not 2")
		}
		t1, ok := args[0].(string)
		if !ok {
			return false, fmt.Erro<PERSON>("timeCompare: args[0] is not string")
		}
		t2, ok := args[1].(string)
		if !ok {
			return false, fmt.Errorf("timeCompare: args[1] is not string")
		}
		t1Time, err := time.Parse("2006-01-02T15:04:05+08:00", t1)
		if err != nil {
			return false, err
		}
		t2Time, err := time.Parse("2006-01-02T15:04:05+08:00", t2)
		if err != nil {
			return false, err
		}
		return t1Time.Before(t2Time), nil
	},
	"timeAfter": func(args ...interface{}) (bool, error) {
		if len(args) != 2 {
			return false, fmt.Errorf("timeCompare: args length is not 2")
		}
		t1, ok := args[0].(string)
		if !ok {
			return false, fmt.Errorf("timeCompare: args[0] is not string")
		}
		t2, ok := args[1].(string)
		if !ok {
			return false, fmt.Errorf("timeCompare: args[1] is not string")
		}
		t1Time, err := time.Parse("2006-01-02T15:04:05+08:00", t1)
		if err != nil {
			return false, err
		}
		t2Time, err := time.Parse("2006-01-02T15:04:05+08:00", t2)
		if err != nil {
			return false, err
		}
		return t1Time.After(t2Time), nil
	},
	"clusterLevelBiggerThan": func(args ...interface{}) (bool, error) {
		if len(args) != 2 {
			return false, fmt.Errorf("clusterLevelBiggerThan: args length is not 2")
		}
		src, ok := args[0].(string)
		if !ok {
			return false, fmt.Errorf("clusterLevelBiggerThan: args[0] is not string")
		}
		target, ok := args[1].(string)
		if !ok {
			return false, fmt.Errorf("clusterLevelBiggerThan: args[1] is not string")
		}

		srcLevel, err := strconv.Atoi(strings.TrimPrefix(src, "L"))
		if err != nil {
			return false, fmt.Errorf("clusterLevelBiggerThan: args[0] is not cluster level format")
		}
		targetLevel, err := strconv.Atoi(strings.TrimPrefix(target, "L"))
		if err != nil {
			return false, fmt.Errorf("clusterLevelBiggerThan: args[1] is not cluster level format")
		}

		return srcLevel > targetLevel, nil
	},
	"clusterLevelLowerThan": func(args ...interface{}) (bool, error) {
		if len(args) != 2 {
			return false, fmt.Errorf("clusterLevelLowerThan: args length is not 2")
		}
		src, ok := args[0].(string)
		if !ok {
			return false, fmt.Errorf("clusterLevelLowerThan: args[0] is not string")
		}
		target, ok := args[1].(string)
		if !ok {
			return false, fmt.Errorf("clusterLevelLowerThan: args[1] is not string")
		}

		srcLevel, err := strconv.Atoi(strings.TrimPrefix(src, "L"))
		if err != nil {
			return false, fmt.Errorf("clusterLevelLowerThan: args[0] is not cluster level format")
		}
		targetLevel, err := strconv.Atoi(strings.TrimPrefix(target, "L"))
		if err != nil {
			return false, fmt.Errorf("clusterLevelLowerThan: args[1] is not cluster level format")
		}

		return srcLevel < targetLevel, nil
	},
	"addonVersionBiggerOrEqualThan": func(args ...interface{}) (bool, error) {
		if len(args) != 2 {
			return false, fmt.Errorf("addonVersionBiggerOrEqualThan: args length is not 2")
		}
		v1, ok := args[0].(string)
		if !ok {
			return false, fmt.Errorf("addonVersionBiggerOrEqualThan: args[0] is not string")
		}
		v2, ok := args[1].(string)
		if !ok {
			return false, fmt.Errorf("addonVersionBiggerOrEqualThan: args[1] is not string")
		}

		ret, err := util.CompareVersion(v1, v2)
		if err != nil {
			return false, fmt.Errorf("addonVersionBiggerOrEqualThan: compare version failed: %v", err)
		}
		return ret >= 0, nil
	},
	"addonVersionLowerOrEqualThan": func(args ...interface{}) (bool, error) {
		if len(args) != 2 {
			return false, fmt.Errorf("addonVersionLowerOrEqualThan: args length is not 2")
		}
		v1, ok := args[0].(string)
		if !ok {
			return false, fmt.Errorf("addonVersionLowerOrEqualThan: args[0] is not string")
		}
		v2, ok := args[1].(string)
		if !ok {
			return false, fmt.Errorf("addonVersionLowerOrEqualThan: args[1] is not string")
		}

		ret, err := util.CompareVersion(v1, v2)
		if err != nil {
			return false, fmt.Errorf("addonVersionLowerOrEqualThan: compare version failed: %v", err)
		}
		return ret <= 0, nil
	},
}

func GetUserFuncs() map[string]interface{} {
	return userFuncMap
}

func ExistUserFunc(name string) bool {
	_, ok := userFuncMap[name]
	return ok
}
