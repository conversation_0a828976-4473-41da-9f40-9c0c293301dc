# TaskWrapper JSON Compatibility Layer

## Overview

The `TaskWrapper` provides a flexible JSON unmarshaling solution that handles API response compatibility issues between protobuf (snake_case) and cloud API (PascalCase) JSON formats.

## Problem Statement

The starship-engine codebase previously used `pb.DescribeTaskReply` which follows protobuf naming conventions (snake_case), but some regions now return responses using cloud API standards (PascalCase). This created compatibility issues when unmarshaling JSON responses.

## Solution

### Core Components

1. **TaskWrapper** - Main wrapper that handles both JSON formats
2. **SubTaskWrapper** - Handles subtask information with dual format support  
3. **RiskWrapper** - Handles risk information with dual format support

### Key Features

- **Flexible Field Mapping**: Supports multiple naming conventions for each field
- **Backward Compatibility**: Drop-in replacement for `pb.DescribeTaskReply`
- **Performance Optimized**: Single-pass unmarshaling with smart field detection
- **Comprehensive Testing**: Covers both formats, mixed formats, and edge cases

### Supported Field Mappings

#### TaskWrapper Fields
- `TaskId`: "TaskId", "taskId", "task_id"
- `Name`: "Name", "name", "task_name"
- `AppName`: "AppName", "appName", "app_name"
- `Status`: "Status", "status", "task_status"
- `Stage`: "Stage", "stage", "task_stage"
- `ExtendInfo`: "ExtendInfo", "extendInfo", "extend_info"
- `SubTasks`: "SubTasks", "subTasks", "sub_tasks", "subtasks"

#### SubTaskWrapper Fields
- `SubTaskId`: "SubTaskId", "subTaskId", "sub_task_id", "subtask_id"
- `ParentTaskId`: "ParentTaskId", "parentTaskId", "parent_task_id"
- `Action`: "Action", "action", "task_action"
- `Risks`: "Risks", "risks"

#### RiskWrapper Fields
- `Id`: "Id", "id", "risk_id"
- `SubTaskId`: "SubTaskId", "subTaskId", "sub_task_id", "subtask_id"
- `Name`: "Name", "name", "risk_name"
- `Code`: "Code", "code", "risk_code"
- `Level`: "Level", "level", "risk_level"
- `Detail`: "Detail", "detail", "risk_detail"

## Usage Examples

### PascalCase Format (Cloud API Standard)
```json
{
  "TaskId": 123,
  "Name": "eklet upgrade",
  "AppName": "eklet",
  "Status": "done",
  "SubTasks": [
    {
      "SubTaskId": 1,
      "Action": "precheck",
      "Status": "done",
      "Risks": [
        {
          "Id": 1,
          "Name": "Test Risk",
          "Code": "ERROR",
          "Level": "warning"
        }
      ]
    }
  ]
}
```

### snake_case Format (Protobuf Standard)
```json
{
  "task_id": 123,
  "name": "eklet upgrade", 
  "app_name": "eklet",
  "status": "done",
  "sub_tasks": [
    {
      "sub_task_id": 1,
      "action": "precheck",
      "status": "done",
      "risks": [
        {
          "id": 1,
          "name": "Test Risk",
          "code": "ERROR",
          "level": "warning"
        }
      ]
    }
  ]
}
```

### Mixed Format Support
The TaskWrapper can handle mixed naming conventions within the same JSON response.

## Implementation Details

### Smart Field Mapping
The implementation uses helper functions to check multiple possible field names:

```go
func getFieldValue(data map[string]interface{}, fieldNames ...string) interface{} {
    for _, name := range fieldNames {
        if value, exists := data[name]; exists {
            return value
        }
    }
    return nil
}
```

### Type Conversion
Supports flexible type conversion for numeric fields:
- int64, int, float64 → int64
- int32, int, int64, float64 → int32
- String representations of numbers are parsed automatically

### Performance Characteristics
- Unmarshaling: ~8.1 μs per operation
- Marshaling: ~1.2 μs per operation
- Single-pass parsing without double unmarshaling
- Memory efficient with minimal allocations

## Testing

### Test Coverage
- **Format Compatibility**: PascalCase, snake_case, and mixed formats
- **Real-world Data**: Tests with actual API response data
- **Edge Cases**: Empty arrays, invalid JSON, type mismatches
- **Performance**: Benchmark tests for both marshaling and unmarshaling

### Running Tests
```bash
# Run all tests
go test ./pkg/internalapi -v

# Run specific test
go test ./pkg/internalapi -v -run TestTaskWrapper_UnmarshalJSON

# Run benchmarks
go test ./pkg/internalapi -bench=BenchmarkTaskWrapper -run=^$
```

## Migration Guide

### Before (using pb.DescribeTaskReply)
```go
type DescribeAppUpgradingProgressResponseImp struct {
    RequestId string                `json:"RequestId"`
    Task      pb.DescribeTaskReply  `json:"Task"`
    Error     ResponseV3ErrorDetail `json:"Error,omitempty"`
}
```

### After (using TaskWrapper)
```go
type DescribeAppUpgradingProgressResponseImp struct {
    RequestId string                `json:"RequestId"`
    Task      TaskWrapper           `json:"Task"`
    Error     ResponseV3ErrorDetail `json:"Error,omitempty"`
}
```

### Code Changes Required
- Replace `pb.DescribeTaskReply` with `TaskWrapper` in type definitions
- Update test files to use `internalapi.TaskWrapper` instead of `pb.DescribeTaskReply`
- No changes required in business logic code that accesses fields

## Error Handling

The TaskWrapper provides detailed error messages for debugging:
- JSON parsing errors include context about which field failed
- Type conversion errors specify the expected and actual types
- Nested structure errors indicate the exact location of the problem

## Future Enhancements

1. **Schema Validation**: Add optional JSON schema validation
2. **Custom Field Mappings**: Allow configuration of custom field name mappings
3. **Caching**: Cache field mapping results for repeated operations
4. **Metrics**: Add instrumentation for monitoring format usage patterns
