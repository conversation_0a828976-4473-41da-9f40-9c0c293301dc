/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by lister-gen. DO NOT EDIT.

package v1

// CSIOperatorListerExpansion allows custom methods to be added to
// CSIOperatorLister.
type CSIOperatorListerExpansion interface{}

// ClusterListerExpansion allows custom methods to be added to
// ClusterLister.
type ClusterListerExpansion interface{}

// ClusterAddonListerExpansion allows custom methods to be added to
// ClusterAddonLister.
type ClusterAddonListerExpansion interface{}

// ClusterCredentialListerExpansion allows custom methods to be added to
// ClusterCredentialLister.
type ClusterCredentialListerExpansion interface{}

// ClusterGroupAPIResourceItemsListerExpansion allows custom methods to be added to
// ClusterGroupAPIResourceItemsLister.
type ClusterGroupAPIResourceItemsListerExpansion interface{}

// ConfigMapListerExpansion allows custom methods to be added to
// ConfigMapLister.
type ConfigMapListerExpansion interface{}

// CronHPAListerExpansion allows custom methods to be added to
// CronHPALister.
type CronHPAListerExpansion interface{}

// MachineListerExpansion allows custom methods to be added to
// MachineLister.
type MachineListerExpansion interface{}

// PersistentEventListerExpansion allows custom methods to be added to
// PersistentEventLister.
type PersistentEventListerExpansion interface{}

// RegistryListerExpansion allows custom methods to be added to
// RegistryLister.
type RegistryListerExpansion interface{}

// TappControllerListerExpansion allows custom methods to be added to
// TappControllerLister.
type TappControllerListerExpansion interface{}
