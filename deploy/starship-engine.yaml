---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: starship-engine
  namespace: starship
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: starship-engine
  name: starship-engine
  namespace: starship
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: starship-engine
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: starship-engine
    spec:
      containers:
        - command:
            - starship-engine
          image: mirrors.tencent.com/starship/starship-engine:v1.0.2
          imagePullPolicy: Always
          name: starship-engine
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 200Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
            - mountPath: /etc/tops/conf/tops-readonly.yaml
              name: tops-readonly-config
              subPath: tops-readonly.yaml
            - mountPath: /etc/tops/conf/eks-platform-cert/eks-platform.pem
              name: eks-platform-secret
              subPath: eks-platform.pem
            - mountPath: /etc/tops/conf/eks-platform-cert/eks-platform-key.pem
              name: eks-platform-secret
              subPath: eks-platform-key.pem
            - mountPath: /root/conf
              name: starship-engine-config
            - mountPath: /etc/tone/conf/eks-pod-db.yaml
              name: eks-pod-db-config
              subPath: eks-pod-db.yaml
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccountName: starship-engine
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: tz-config
        - name: tops-readonly-config
          configMap:
            defaultMode: 420
            name: tops-readonly-config
        - name: eks-platform-secret
          secret:
            defaultMode: 420
            secretName: eks-platform-secret
        - name: starship-engine-config
          configMap:
            defaultMode: 420
            name: starship-engine-config
        - name: eks-pod-db-config
          configMap:
            defaultMode: 420
            name: eks-pod-db-config
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: starship-engine
rules:
  - apiGroups:
      - batch
    resources:
      - jobs
    verbs:
      - '*'
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - create
      - delete
      - get
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - configmaps
      - configmaps/finalizers
    verbs:
      - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: starship-engine
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: starship-engine
subjects:
  - kind: ServiceAccount
    name: starship-engine
    namespace: starship
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tops-readonly-config
  namespace: starship
data:
  tops-readonly.yaml: |
    dashboard_db:
      cq:
        name: "dashboard"
        host: "*************"
        port: 3306
        user: "dash_dev_ro"
        pass: "UcpX3j68ZPdDB4hM"
      in:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'Tp8MmkDRyUQjKPJZ'
      sg:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'HzeZhK3R2476EyxQ'
      usw:
        name: "dashboard"
        host: '************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'M2eE74QgdkCPhAqW'
      use:
        name: "dashboard"
        host: '************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'JgqVm4NwRbDuFnTL'
      jp:
        name: "dashboard"
        host: '************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'X8qvucy2HA3UbpYR'
      kr:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'HFetCEfVap298SZM'
      ru:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'SgrUFuwDzGvhQ63T'
      th:
        name: "dashboard"
        host: '************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'WT94AP8SQvMpCNrB'
      hk:
        name: "dashboard"
        host: '***********'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'Hekn73NjvWqBMgLM'
      bj:
        name: "dashboard"
        host: '***********'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'FEqV4jDsC3zmuWaX'
      sh:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'HzeZhK3R2476EyxQ'
      gz:
        name: "dashboard"
        host: "************"
        port: 3306
        user: "dash_dev_ro"
        pass: "UKeugyjs5WvrkYpT"
      cd:
        name: "dashboard"
        host: '***********'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'FEqV4jDsC3zmuWaX'
      ca:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: "dash_dev_ro"
        pass: "VDWw6G94quXbpJeM"
      de:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'PrqUzjEHD4TnAM5C'
      szjr:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'R3pkMLCWj29xUGsM'
      shjr:
        name: "dashboard"
        host: '**************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'ZQH8apV6GgLcTwKV'
      nj:
        name: "dashboard"
        host: '************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'XWm35KezDfYGq4LA'
      tsn:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'T9Lg6HYq3ySbnfUC'
      szx:
        name: "dashboard"
        host: '************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'QnjVXxs6LZHE5cFW'
      tpe:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'QyJTgu9C5EUZbKkG'
      qy:
        name: "dashboard"
        host: "************"
        port: 3306
        user: "dash_dev_ro"
        pass: "Go54n61QO4ZZfJ0P"
      bjjr:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'UKeugyjs5WvrkYpT'
      xbec:
        name: "dashboard"
        host: '************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'Hekn73NjvWqBMgLM'
      jkt:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'TJ4RHfke8qvh2aBL'
      hzec:
        name: "dashboard"
        host: '***********'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'X6s2PKwq9pf8JZ5N'
      jnec:
        name: "dashboard"
        host: '**********'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'K34RtuzyxdRz3CjJ'
      fzec:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'Hekn73NjvWqBMgLM'
      whec:
        name: "dashboard"
        host: '************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'FEqV4jDsC3zmuWaX'
      csec:
        name: "dashboard"
        host: '***********'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'HzeZhK3R2476EyxQ'
      sheec:
        name: "dashboard"
        host: '***********'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'UcpX3j68ZPdDB4hM'
      sjwec:
        name: "dashboard"
        host: '*************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'DBALFSH6eTxdgbyA'
      hfeec:
        name: "dashboard"
        host: '**************'
        port: 3306
        user: 'dash_dev_ro'
        pass: 'KNzH5hXsjb3xam4B'
      sao:
        name: "dashboard"
        host: "*************"
        port: 3306
        user: "dash_dev_ro"
        pass: "V8gSzyuqa2UkWnrP"
      xiyec:
        name: "dashboard"
        host: "*************"
        port: 3306
        user: "dash_dev_ro"
        pass: "GM2Xt4kQxRua3vZC"
      shadc:
        name: "dashboard"
        host: "************"
        port: 3306
        user: "dash_dev_ro"
        pass: "FXEsnQ8RbY7aw32R"
      shwxzf:
        name: "dashboard"
        host: "************"
        port: 3306
        user: "dash_dev_ro"
        pass: "36UjJqBz5q9gNUhX"
      gzwxzf:
        name: "dashboard"
        host: "************"
        port: 3306
        user: "dash_dev_ro"
        pass: "K8gTTtAsBFT7DxKG"
    cauth:
      server:
        cq: "http://logical.server.console.tencentyun.com:8080/interface.php"
        jkt: "http://logical.server.console.tencentyun.com:8080/interface.php"
        jp: "http://logical.server.console.tencentyun.com:8080/interface.php"
        ca: "http://logical.server.console.tencentyun.com:8080/interface.php"
        use: "http://logical.server.console.tencentyun.com:8080/interface.php"
        th: "http://logical.server.console.tencentyun.com:8080/interface.php"
        in: "http://logical.server.console.tencentyun.com:8080/interface.php"
        ru: "http://logical.server.console.tencentyun.com:8080/interface.php"
        kr: "http://logical.server.console.tencentyun.com:8080/interface.php"
        de: "http://logical.server.console.tencentyun.com:8080/interface.php"
        sao: "http://logical.server.console.tencentyun.com:8080/interface.php"
        usw: "http://logical.server.console.tencentyun.com:8080/interface.php"
        cd: "http://logical.server.console.tencentyun.com:8080/interface.php"
        hk: "http://logical.server.console.tencentyun.com:8080/interface.php"
        sg: "http://logical.server.console.tencentyun.com:8080/interface.php"
        tsn: "http://logical.server.console.tencentyun.com:8080/interface.php"
        szx: "http://logical.server.console.tencentyun.com:8080/interface.php"
        gz: "http://logical.server.console.tencentyun.com:8080/interface.php"
        bj: "http://logical.server.console.tencentyun.com:8080/interface.php"
        nj: "http://logical.server.console.tencentyun.com:8080/interface.php"
        sh: "http://logical.server.console.tencentyun.com:8080/interface.php"
        shjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
        szjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
        bjjr: "http://logical.server.console.tencentyun.com:8080/interface.php"
        tpe: "http://logical.server.console.tencentyun.com:8080/interface.php"
        qy: "http://logical.server.console.tencentyun.com:8080/interface.php"
        xbec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        hzec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        jnec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        fzec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        whec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        csec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        sheec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        sjwec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        hfeec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        xiyec: "http://logical.server.console.tencentyun.com:8080/interface.php"
        shadc: "http://logical.server.console.tencentyun.com:8080/interface.php"
    vpc:
      vpcOss:
        url: "http://REGION.oss.vpc.tencentyun.com:8080/tvpc/api"
      apiV3:
        url: "http://REGION.vpcapiv3.tencentyun.com:8520"
    domain:
      url: "http://api.gslb.tencentyun.com"
      sysId: 1923420120
    roleCfg:
      tkeQcsRole:
        secretId: "AKIDXco1EUjCkIuZK5qfaXWbKQTWcq3JdyOq"
        secretKey: "Y6X2Z7kO6sGe2VXZXVQNOQUZYTXZdkaQ"
        rolePrefix: "roleName"
        roleName: "TKE_QCSRole"
    tke:
      dashboard:
        url: "http://REGION.tke.caas.tencentyun.com/dashboard"
      tkeApiserver:
        url: "https://REGION.api.tke.caas.tencentyun.com"
        cert: "/etc/tops/conf/tke-api-cert/admin.pem"
        key: "/etc/tops/conf/tke-api-cert/admin-key.pem"
      tkePlatform:
        url: "https://REGION.tke.caas.tencentyun.com:9443"
        cert: "/etc/tops/conf/tke-platform-cert/platform.pem"
        key: "/etc/tops/conf/tke-platform-cert/platform-key.pem"
      tkeApplication:
        url: "https://REGION.tke.caas.tencentyun.com:9463"
        cert: "/etc/tops/conf/tke-platform-cert/platform.pem"
        key: "/etc/tops/conf/tke-platform-cert/platform-key.pem"
      defaultMetaCluster:
        gz: "cls-rbnichlc"
        szjr: "cls-6e4edn6y"
        sh: "cls-rb4l34r9"
        shjr: "cls-7we9vmys"
        bj: "cls-cnas00yb"
        cd: "cls-lizl7svv"
        cq: "cls-lr2pvld2"
        hk: "cls-ntwx40ak"
        sg: "cls-qbc3zefo"
        th: "cls-7wycntkc"
        in: "cls-kftyr0my"
        kr: "cls-4i52bq23"
        jp: "cls-nx3v4et0"
        usw: "cls-7xvp0tcw"
        use: "cls-52jdb4dl"
        de: "cls-3r01ruwo"
        ru: "cls-prfp27at"
        nj: "cls-3qs30g2e"
        tsn: "cls-jyi5zllr"
        szx: "cls-mh1d842k"
        tpe: "cls-qz9ifaxa"
        ca: "cls-nkxkhgvb"
        qy: "cls-h4629lhl"
        bjjr: "cls-e2d8nvih"
        xbec: "cls-4tqcs5rb"
        jkt: "cls-bydrsad1"
        jnec: "cls-46jrda2y"
        hzec: "cls-epm3yjlh"
        whec: "cls-epm3yjl0"
        csec: "cls-epm3ymyc"
        sheec: "cls-epm3x86t"
        sjwec: "cls-epm3x584"
        fzec: "cls-epm3yjl1"
        hfeec: "cls-epm3x57o"
        sao: "cls-3js0y19h"
        xiyec: "cls-9cgvijy6"
        shadc: "cls-3zcffj0n"
    eks:
      cloudGwUrl: "http://REGION.tke.caas.tencentyun.com/tke-cloud-gw"
      eksServerUrl: "http://REGION.eks.caas.tencentyun.com/eks-server"
      eksPlatform:
        url: "https://REGION.eks.caas.tencentyun.com:30201"
        cert: "/etc/tops/conf/eks-platform-cert/eks-platform.pem"
        key: "/etc/tops/conf/eks-platform-cert/eks-platform-key.pem"
    migration:
      global:
        cvmEtcdRootClientCertFile: "/etc/tops/conf/etcd-cert-cvm/etcd-client.crt"
        cvmEtcdRootClientKeyFile: "/etc/tops/conf/etcd-cert-cvm/etcd-client.key"
        kstoneEtcdRootClientCertFile: "/etc/tops/conf/etcd-cert-kstone/etcd-client.crt"
        kstoneEtcdRootClientKeyFile: "/etc/tops/conf/etcd-cert-kstone/etcd-client.key"
        oldCvmEtcdCACertFile: "/etc/tops/conf/etcd-root-ca/old-etcd-ca.crt"
        newCvmEtcdCACertFile: "/etc/tops/conf/etcd-root-ca/new-etcd-ca.crt"
        backupCluster:
          kubeconfig: "/etc/tops/conf/kube/config/backup-resource-kubeconfig-cd"
        backupEtcd:
          prefix: "/cls-bfpx4mje"
          endpoint: "https://**************:18336"
          cert: "/etc/tops/conf/etcd-cert-backup/etcd-client.crt"
          key: "/etc/tops/conf/etcd-cert-backup/etcd-client.key"
      metaclusters:
        cq:
          srcMetaCluster: "cls-lr2pvld2"
          dstMetaCluster: "cls-igj9pcga"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "*********"
            port: 36000
        jkt:
          srcMetaCluster: "cls-bydrsad1"
          dstMetaCluster: "cls-afgk2s1j"
        jp:
          srcMetaCluster: "cls-nx3v4et0"
          dstMetaCluster: "cls-fd0y2nas"
        ca:
          srcMetaCluster: "cls-nkxkhgvb"
          dstMetaCluster: "cls-1zmp6uy3"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "*********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "*********"
            port: 36000
        use:
          srcMetaCluster: "cls-52jdb4dl"
          dstMetaCluster: "cls-mq5b0wtf"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "***********"
            port: 36000
        in:
          srcMetaCluster: "cls-kftyr0my"
          dstMetaCluster: "cls-5hnnf5ay"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "***********"
            port: 36000
        th:
          srcMetaCluster: "cls-7wycntkc"
          dstMetaCluster: "cls-el7if67c"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "**********"
            port: 36000
        kr:
          srcMetaCluster: "cls-4i52bq23"
          dstMetaCluster: "cls-p9g45e01"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "**********"
            port: 36000
        ru:
          srcMetaCluster: "cls-prfp27at"
          dstMetaCluster: "cls-4623qke7"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "*********"
            port: 36000
        de:
          srcMetaCluster: "cls-3r01ruwo"
          dstMetaCluster: "cls-1fl6rdoy"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "**********"
            port: 36000
        usw:
          srcMetaCluster: "cls-7xvp0tcw"
          dstMetaCluster: "cls-jn7xp6cm"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "**********"
            port: 36000
        cd:
          srcMetaCluster: "cls-lizl7svv"
          dstMetaCluster: "cls-3efjbva1"
          srcMetaTunnelConfig:
            user: "root"
            password: "TEG#@rpqd@4327"
            host: "**************"
            port: 22022
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "***********"
            port: 36000
          srcMetaEtcd:
            endpoint: "http://*************:11706"
        sh:
          srcMetaCluster: "cls-rb4l34r9"
          dstMetaCluster: "cls-qvvptqc3"
          srcMetaTunnelConfig:
            user: "root"
            password: "TEG#@rpqd@4327"
            host: "**************"
            port: 22022
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "*********"
            port: 36000
          srcMetaEtcd:
            endpoint: "http://**********:10565"
        gz:
          srcMetaCluster: "cls-rbnichlc"
          dstMetaCluster: "cls-pgl5mqfo"
          srcMetaTunnelConfig:
            user: "root"
            password: "TEG#@rpqd@4327"
            host: "**************"
            port: 22022
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "**********"
            port: 36000
          srcMetaEtcd:
            endpoint: "http://**************:14800"
        hk:
          srcMetaCluster: "cls-ntwx40ak"
          dstMetaCluster: "cls-9o38eplq"
          srcMetaTunnelConfig:
            user: "root"
            password: "TEG#@rpqd@4327"
            host: "*************"
            port: 22022
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "**********"
            port: 36000
          srcMetaEtcd:
            endpoint: "http://************:10726"
        sg:
          srcMetaCluster: "cls-qbc3zefo"
          dstMetaCluster: "cls-gcz1z1f6"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "*********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "***********"
            port: 36000
        tsn:
          srcMetaCluster: "cls-jyi5zllr"
          dstMetaCluster: "cls-ky90hr3p"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "***********"
            port: 36000
        szx:
          srcMetaCluster: "cls-mh1d842k"
          dstMetaCluster: "cls-8d0h5nyy"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "************"
            port: 36000
        bj:
          srcMetaCluster: "cls-cnas00yb"
          dstMetaCluster: "cls-mlwo2v7b"
          srcMetaTunnelConfig:
            user: "root"
            password: "TEG#@rpqd@4327"
            host: "**************"
            port: 22022
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "**********"
            port: 36000
          srcMetaEtcd:
            endpoint: "http://************:11353"
        sao:
          srcMetaCluster: "cls-3js0y19h"
          dstMetaCluster: "cls-oo4yn6y1"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "********"
            port: 36000
        nj:
          srcMetaCluster: "cls-3qs30g2e"
          dstMetaCluster: "cls-rzasdlks"
          srcMetaTunnelConfig:
            user: "ubuntu"
            keyFile: "/etc/tops/conf/key/meta.key"
            host: "**********"
            port: 22
          dstMetaTunnelConfig:
            user: "root"
            keyFile: "/etc/tops/conf/key/ziyan_meta.key"
            host: "**********"
            port: 36000
    #      srcMetaEtcd:
    #        endpoint: "http://*******:2379"

---
apiVersion: v1
kind: Secret
metadata:
  name: eks-platform-secret
  namespace: starship
type: Opaque
data:
  eks-platform.pem: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURXRENDQWtBQ0ZFYnBUTEpsTmtqTGNTT3EwYUk5U2h5OTZ3TkFNQTBHQ1NxR1NJYjNEUUVCQ3dVQU1HUXgKQ3pBSkJnTlZCQVlUQWtOT01SRXdEd1lEVlFRSURBaFRhR1Z1ZW1obGJqRVJNQThHQTFVRUJ3d0lVMmhsYm5wbwpaVzR4RURBT0JnTlZCQW9NQjNSbGJtTmxiblF4RHpBTkJnTlZCQXNNQmxONWMzUmxiVEVNTUFvR0ExVUVBd3dECmRHdGxNQjRYRFRJeE1EVXlOekV4TVRVeU0xb1hEVE14TURVeU5URXhNVFV5TTFvd2JURUxNQWtHQTFVRUJoTUMKUTA0eEVUQVBCZ05WQkFnTUNGTm9aVzU2YUdWdU1SRXdEd1lEVlFRSERBaFRhR1Z1ZW1obGJqRVhNQlVHQTFVRQpDZ3dPYzNsemRHVnRPbTFoYzNSbGNuTXhEekFOQmdOVkJBc01CbE41YzNSbGJURU9NQXdHQTFVRUF3d0ZZV1J0CmFXNHdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFEeWp5V2d2SWdhOXdpMXFtRXUKZTB2ZjNtUU53akorUUVteDNlZmxCWTVnbmxReXo3LzB6Z0cxQm82bFZJYmVHU1BWcEVZcFVFcGp0dWVkME9CWQp4bmpUUTBBWEIvUS82clB4bk01MHJHUEEzR1pLZ04yOEc0YU1ZOGZIN1pRMDNJdEVqZzd5cjFoMUIyUnBvdzBhCjk4Y2xxcFpmOHhDWC9pbCtSVzJNSWxWZUZyakR3RGNxSTBZUy9CUkltcEJmYlgycXJ5WjExMXBIWmlsaDhoR2wKR0hqbWNmWWxpejF6c1h2RHg1RGNVNEFOUGpIWDJidFplQkRRWHNlTGpORzJxeE5yVzB6cmh5R1dHSDhWQUQ1NwpNM01XRG5yTklMWEdEN0pUOENyNmNlTVpvS3VqZCtubm9VR2lRcjVoTjZXb1FTOC9CMW9JYjJHb1ZsbVoxQjhLCmlJblZBZ01CQUFFd0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFIK0EyNHRydm1IL3RFanpWOXBIS1ZBSUVIRGwKL0IvVDB6Tlh4Q3YvM090MnczMG5KaXo3SVMzcDlkdU1QSy8xTUxaYmFyQlJPV2R5Z0hlL3czWFJKQjBpUjFjdgprRHVka1BiWXRiME91MVR3bXUyUC9Xc25LRXpxS2J5MTFnbEs5bDZhNWRPbzdNZTFadVlIamtLSFV0bmZJTFA3CnU3K2E5WjJaclJpVWlqRHE2SXlDZWdGd211ZlJ0NHp6Q0Z0a2M2UHp6aFJzZ2hGTDNWV2RYcmxPOEtIc2habysKK1YyeEo1eElFdUFjTXdFU2VDR0dtSDRFUjRHWjkrWHpwM1lpckN1bHc3WnJRRFArNlQ2aDMxNjJ6VmFhTzEydwo4K1V5UXZvSEJyUUE2T3pRTEV5emFPVzRoVUdrcFhNMWxNVHpuN245a2QyL0VWdFRFeUJFTzNuZW9Xaz0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  eks-platform-key.pem: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: starship-engine-config
  namespace: starship
data:
  config.yaml: |
    db:
      dbHost: "************"
      dbPort: "3306"
      dbUser: "starship"
      dbPasswd: "K@yZ7CbheTxb5nVc6b%L"
      dbDatabase: "starship"
    syncClusterDB:
      dbHost: "lb-p3pcroiw-u10rbxwf278n3mit.clb.gz-tencentclb.cloud"
      dbPort: "3306"
      dbUser: "root"
      dbPasswd: "]K^B4~urzQVv6n8"
      dbDatabase: "starship"
    eksPodClickhouseDBConfig:
      dbHost: "*************"
      dbPort: "9000"
      dbUser: "eks"
      dbPasswd: "Zh2AHVTFqsYCUJcK"
      dbDatabase: "eks"
    batchInterval:
      default:
        batchInterval: 3600
        subBatchInterval: 300