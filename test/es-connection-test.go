package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
)

func main() {
	// 测试不同的ES连接配置
	testConfigs := []struct {
		name      string
		addresses []string
		transport *http.Transport
	}{
		{
			name:      "HTTP without TLS",
			addresses: []string{"http://localhost:9200"},
			transport: &http.Transport{
				MaxIdleConnsPerHost:   10,
				ResponseHeaderTimeout: 30 * time.Second,
			},
		},
		{
			name:      "HTTP with InsecureSkipVerify",
			addresses: []string{"http://localhost:9200"},
			transport: &http.Transport{
				MaxIdleConnsPerHost:   10,
				ResponseHeaderTimeout: 30 * time.Second,
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		},
		{
			name:      "HTTPS with InsecureSkipVerify",
			addresses: []string{"https://localhost:9200"},
			transport: &http.Transport{
				MaxIdleConnsPerHost:   10,
				ResponseHeaderTimeout: 30 * time.Second,
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		},
	}

	for _, config := range testConfigs {
		fmt.Printf("\n=== Testing %s ===\n", config.name)
		fmt.Printf("Addresses: %v\n", config.addresses)

		cfg := elasticsearch.Config{
			Addresses: config.addresses,
			Transport: config.transport,
		}

		es, err := elasticsearch.NewClient(cfg)
		if err != nil {
			fmt.Printf("❌ Failed to create client: %v\n", err)
			continue
		}

		// 测试连接
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		res, err := es.Info(es.Info.WithContext(ctx))
		cancel()

		if err != nil {
			fmt.Printf("❌ Connection failed: %v\n", err)
			continue
		}

		if res.IsError() {
			fmt.Printf("❌ ES returned error: %s\n", res.Status())
			continue
		}

		fmt.Printf("✅ Connection successful!\n")

		// 打印ES信息
		defer res.Body.Close()
		var info map[string]interface{}
		if err := json.NewDecoder(res.Body).Decode(&info); err == nil {
			if version, ok := info["version"].(map[string]interface{}); ok {
				if number, ok := version["number"].(string); ok {
					fmt.Printf("ES Version: %s\n", number)
				}
			}
			if clusterName, ok := info["cluster_name"].(string); ok {
				fmt.Printf("Cluster Name: %s\n", clusterName)
			}
		}
	}

	// 测试直接HTTP请求
	fmt.Printf("\n=== Testing direct HTTP request ===\n")
	testDirectHTTP()
}

func testDirectHTTP() {
	urls := []string{
		"http://localhost:9200",
		"https://localhost:9200",
	}

	for _, url := range urls {
		fmt.Printf("Testing %s\n", url)

		client := &http.Client{
			Timeout: 10 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		}

		resp, err := client.Get(url)
		if err != nil {
			fmt.Printf("❌ HTTP request failed: %v\n", err)
			continue
		}
		defer resp.Body.Close()

		fmt.Printf("✅ HTTP request successful! Status: %s\n", resp.Status)
	}
}
