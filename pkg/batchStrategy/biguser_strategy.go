package batchStrategy

import (
	"fmt"

	pkgcls "git.woa.com/kmetis/starship-engine/pkg/clusterinfo"
)

const (
	BigUserBatchStrategyName = "BigUserBatchStrategy"
)

type BigUserBatchStrategy struct {
	Name string
	ID   int64
}

func NewBigUserBatchStrategy() *BigUserBatchStrategy {
	return &BigUserBatchStrategy{
		Name: BigUserBatchStrategyName,
		ID:   BigUserBatchStrategyID,
	}
}

func (b *BigUserBatchStrategy) DivideBatches(batches []BatchConfig, isMainBatch bool, options BatchStrategyOptions) ([]BatchConfig, error) {
	if isMainBatch && len(batches) > 1 {
		return nil, fmt.Errorf("main batch should have at most one batch")
	}
	if options.AppName == "" {
		return nil, fmt.Errorf("appName is empty")
	}

	newBatches := make([]BatchConfig, 0)
	helper, err := pkgcls.GetClsInfoHelper()
	if err != nil {
		return nil, err
	}
	for _, batch := range batches {
		if len(batch.Clusters) == 0 {
			continue
		}
		batchPriorityGroups := make(map[int][]string)
		for _, cluster := range batch.Clusters {
			isBigUser, err := helper.IsBigUser(options.AppName, cluster)
			if err != nil {
				return nil, err
			}
			if isBigUser {
				if _, ok := batchPriorityGroups[1]; !ok {
					batchPriorityGroups[1] = make([]string, 0)
				}
				batchPriorityGroups[1] = append(batchPriorityGroups[1], cluster)
			} else {
				if _, ok := batchPriorityGroups[0]; !ok {
					batchPriorityGroups[0] = make([]string, 0)
				}
				batchPriorityGroups[0] = append(batchPriorityGroups[0], cluster)
			}
		}

		index := 0
		for i := 0; i <= 1; i++ {
			if len(batchPriorityGroups[i]) > 0 {
				index++
				batchId := batch.BatchId
				if isMainBatch {
					batchId = int64(index)
				}
				newBatches = append(newBatches, BatchConfig{
					Region:   batch.Region,
					Clusters: batchPriorityGroups[i],
					BatchId:  batchId,
				})
			}
		}
	}
	return newBatches, nil
}

func (b *BigUserBatchStrategy) GetName() string {
	return BigUserBatchStrategyName
}

func (b *BigUserBatchStrategy) GetID() int64 {
	return BigUserBatchStrategyID
}
