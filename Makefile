# init project path
HOMEDIR := $(shell pwd)
BUILDER := $(HOMEDIR)/builder
OUTDIR  := $(HOMEDIR)/builder/_output
tag=$(shell git describe --abbrev=0 --tags)
CLI-BIN := starship-cli
ENGINE-BIN := starship-engine

VERSION=`git describe --always`
BUILDTIME=`date +%FT%T%z`
GOVERSION=`go version`

# init command params: darwin/arm64
GO      := go
GOOS    := linux
GOARCH  := amd64
GOBUILD := $(GO) build

# Setup the -ldflags option for go build here, interpolate the variable values
LDFLAGS=-ldflags "-w -s -X main.Version=${VERSION} -X 'main.BuildTime=${BUILDTIME}' -X 'main.GoVersion=${GOVERSION}'"

build: build-cli build-engine

test: build
	@$(OUTDIR)/$(CLI-BIN)
	@$(OUTDIR)/$(ENGINE-BIN)

build-cli:
	@mkdir -p $(OUTDIR)
	@GOOS=$(GOOS) GOARCH=$(GOARCH) $(GOBUILD) ${LDFLAGS} -o $(OUTDIR)/$(CLI-BIN) cmd/starship-cli/main.go

build-engine:
	@mkdir -p $(OUTDIR)
	@GOOS=$(GOOS) GOARCH=$(GOARCH) $(GOBUILD) ${LDFLAGS} -o $(OUTDIR)/$(ENGINE-BIN) cmd/starship-engine/main.go

image-engine: build-engine
	docker build -t ccr.ccs.tencentyun.com/tcr_test/starship-engine:${tag} -f ${BUILDER}/Dockerfile_engine ${BUILDER}

image-cli: build-cli
	docker build -t ccr.ccs.tencentyun.com/tcr_test/starship-cli:${tag} -f ${BUILDER}/Dockerfile_cli ${BUILDER}

image: image-engine image-cli

push-cli: image-cli
	docker push ccr.ccs.tencentyun.com/tcr_test/starship-cli:${tag}

push-engine: image-engine
	docker push ccr.ccs.tencentyun.com/tcr_test/starship-engine:${tag}

push: push-cli push-engine

# make clean
clean:
	rm -rf $(OUTDIR)

# avoid filename conflict and speed up build 
.PHONY: build clean
