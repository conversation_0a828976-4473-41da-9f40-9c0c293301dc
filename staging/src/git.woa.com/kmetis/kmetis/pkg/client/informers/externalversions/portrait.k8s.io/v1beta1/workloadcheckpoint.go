/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by informer-gen. DO NOT EDIT.

package v1beta1

import (
	"context"
	time "time"

	portraitk8siov1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/portrait.k8s.io/v1beta1"
	versioned "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned"
	internalinterfaces "git.woa.com/kmetis/kmetis/pkg/client/informers/externalversions/internalinterfaces"
	v1beta1 "git.woa.com/kmetis/kmetis/pkg/client/listers/portrait.k8s.io/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// WorkloadCheckpointInformer provides access to a shared informer and lister for
// WorkloadCheckpoints.
type WorkloadCheckpointInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1beta1.WorkloadCheckpointLister
}

type workloadCheckpointInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
	namespace        string
}

// NewWorkloadCheckpointInformer constructs a new informer for WorkloadCheckpoint type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewWorkloadCheckpointInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredWorkloadCheckpointInformer(client, namespace, resyncPeriod, indexers, nil)
}

// NewFilteredWorkloadCheckpointInformer constructs a new informer for WorkloadCheckpoint type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredWorkloadCheckpointInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options v1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.PortraitV1beta1().WorkloadCheckpoints(namespace).List(context.TODO(), options)
			},
			WatchFunc: func(options v1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.PortraitV1beta1().WorkloadCheckpoints(namespace).Watch(context.TODO(), options)
			},
		},
		&portraitk8siov1beta1.WorkloadCheckpoint{},
		resyncPeriod,
		indexers,
	)
}

func (f *workloadCheckpointInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredWorkloadCheckpointInformer(client, f.namespace, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *workloadCheckpointInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&portraitk8siov1beta1.WorkloadCheckpoint{}, f.defaultInformer)
}

func (f *workloadCheckpointInformer) Lister() v1beta1.WorkloadCheckpointLister {
	return v1beta1.NewWorkloadCheckpointLister(f.Informer().GetIndexer())
}
