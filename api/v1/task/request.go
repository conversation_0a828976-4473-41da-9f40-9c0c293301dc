package task

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"github.com/go-playground/validator/v10"
)

// 列表请求参数
type ListRequest struct {
	TaskName  string `json:"task_name" form:"task_name" validate:"omitempty,max=50,task_name_format"`
	User      string `json:"user" form:"user" validate:"omitempty,max=20"`
	AppAction string `json:"app_action" form:"app_action" validate:"omitempty,oneof=precheck publish postcheck"`
	Component string `json:"component" form:"component" validate:"omitempty,max=30"`
	Status    string `json:"status" form:"status" validate:"omitempty,oneof=Created Prepare Running BatchCompleted Completed Pause Failed Terminated"`
	PageNum   int    `json:"page_num" form:"page_num" validate:"required,min=1"`
	PageSize  int    `json:"page_size" form:"page_size" validate:"required,min=1,max=100"`
	ClusterId string `json:"cluster_id" form:"cluster_id" validate:"omitempty,max=30,clusterID"`
}

type CreateTaskRequest struct {
	AppAction string `json:"app_action" form:"app_action" validate:"required,oneof=precheck publish postcheck"`
	ChangeID  string `json:"change_id" form:"change_id" validate:"required"`
	User      string `json:"user" form:"user" validate:"required"`
	Component string `json:"component" form:"component" validate:"required,min=3,max=20"`

	Description string `json:"description" form:"description" validate:"omitempty,max=1024"`

	// 版本信息 (二选一)
	ImageTag   string `json:"image_tag,omitempty" form:"image_tag" validate:"omitempty"`
	AppVersion string `json:"app_version,omitempty" form:"app_version" validate:"omitempty"`
	// 扩展信息
	ExtendInfo string `json:"extend_info,omitempty" form:"extend_info" validate:"omitempty,json"`

	// 编排配置
	OrchestrationMode *int `json:"orchestration_mode" form:"orchestration_mode" validate:"required,oneof=1 2"`

	// 模式1专属字段 (clusters 和 ClusterFileName 二选一)
	Clusters        []string `json:"clusters,omitempty" form:"clusters" validate:"omitempty"`
	ClusterFileName string   `json:"cluster_file_name,omitempty" form:"cluster_file_name" validate:"omitempty"` // COS 文件名

	// 模式2专属字段
	ClusterType      string `json:"cluster_type,omitempty" form:"cluster_type" validate:"omitempty,oneof=tke eks,required_if=OrchestrationMode 2"`
	ClusterCondition string `json:"cluster_condition,omitempty" form:"cluster_condition"`
	// 基础分批策略
	BatchStrategies []int `json:"batch_strategies,omitempty" form:"batch_strategies"`
	// 业务分批策略
	AppBatchStrategy int `json:"app_batch_strategy,omitempty" form:"app_batch_strategy"`
	BatchSize        int `json:"batch_size" form:"batch_size"`

	//自动确认
	AutoConfirm bool `json:"auto_confirm" form:"auto_confirm"`
}

type UpdateTaskRequest struct {
	AppAction string `json:"app_action" validate:"required,oneof=precheck publish postcheck"`
	ChangeID  string `json:"change_id" validate:"required"`
	User      string `json:"user" validate:"required"`
	TaskName  string `json:"task_name" validate:"required,max=50"`
	Status    string `json:"status" validate:"required,oneof=Prepare Pause Running"`
}

type BatchTerminateTasksRequest struct {
	User    string  `json:"user" validate:"required"`
	TaskIds []int64 `json:"task_ids" validate:"required,min=1,max=100,dive,min=1"`
}

type ListClustersRequest struct {
	TaskName   string `form:"task_name" binding:"required"`
	BatchId    int64  `form:"batch_id"`
	SubBatchId int64  `form:"sub_batch_id"`
	ClusterId  string `form:"cluster_id"`
	Reason     string `form:"reason"`
	Status     string `form:"status"`
	Region     string `form:"region"`
	PageNum    int    `form:"page_num" binding:"required,min=1"`
	PageSize   int    `form:"page_size" binding:"required,min=1,max=100"`
}

type ExportClustersRequest struct {
	TaskName   string `json:"task_name" binding:"required"`
	BatchId    int64  `json:"batch_id"`
	SubBatchId int64  `json:"sub_batch_id"`
	ClusterId  string `json:"cluster_id"`
	Reason     string `json:"reason"`
	Status     string `json:"status" validate:"omitempty,oneof=success failed all"`
	Region     string `json:"region"`
	Format     string `json:"format" validate:"omitempty,oneof=csv json" binding:"omitempty"`
}

type RollbackClusterRequest struct {
	TaskName   string   `json:"task_name" binding:"required"`
	ClusterIds []string `json:"cluster_ids,omitempty"` // 批量回滚时使用
	User       string   `json:"user" binding:"required"`
}

var validate *validator.Validate

func init() {
	validate = validator.New()

	validate.RegisterValidation("task_name_format", func(fl validator.FieldLevel) bool {
		return regexp.MustCompile(`^(precheck|publish|postcheck)-[a-zA-Z0-9]{6}$`).MatchString(fl.Field().String())
	})
	validate.RegisterValidation("clusterID", func(fl validator.FieldLevel) bool {
		return regexp.MustCompile(`^cls-[a-zA-Z0-9]{8}$`).MatchString(fl.Field().String())
	})

	// 注册matches验证函数，用于正则匹配
	validate.RegisterValidation("matches", func(fl validator.FieldLevel) bool {
		param := fl.Param()
		if param == "" {
			return false
		}
		matched, err := regexp.MatchString(param, fl.Field().String())
		if err != nil {
			return false
		}
		return matched
	})

	validate.RegisterValidation("json", func(fl validator.FieldLevel) bool {
		if fl.Field().String() == "" {
			return true
		}
		var js map[string]interface{}
		return json.Unmarshal([]byte(fl.Field().String()), &js) == nil
	})
}
func (r *ListRequest) Validate() error {
	return validate.Struct(r)
}

func (r *CreateTaskRequest) Validate() error {
	// 先执行基本的结构体验证
	if err := validate.Struct(r); err != nil {
		return err
	}

	// 编排模式1的特殊验证：clusters、cluster_file_name 二选一
	if r.OrchestrationMode != nil && *r.OrchestrationMode == 1 {
		hasClusters := len(r.Clusters) > 0
		hasClusterFileName := strings.TrimSpace(r.ClusterFileName) != ""

		// 统计有值的字段数量
		fieldCount := 0
		if hasClusters {
			fieldCount++
		}
		if hasClusterFileName {
			fieldCount++
		}
		// 必须提供其中一个
		if fieldCount == 0 {
			return fmt.Errorf("编排模式1下必须提供clusters、cluster_file_name其中之一")
		}

		// 不能同时提供多个
		if fieldCount > 1 {
			return fmt.Errorf("编排模式1下clusters、cluster_file_name不能同时提供，请选择其中一种方式")
		}

		// 验证clusters格式（如果提供了clusters）
		if hasClusters {
			if err := r.validateClustersFormat(); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateClustersFormat 根据组件类型验证clusters格式
func (r *CreateTaskRequest) validateClustersFormat() error {
	if len(r.Clusters) == 0 {
		return nil
	}

	// 根据组件类型确定期望的格式
	isEkletAgent := r.Component == consts.EKLET_AGENT

	pureClusterPattern := regexp.MustCompile(`^cls-[a-zA-Z0-9]{8}$`)
	clusterNamespacePattern := regexp.MustCompile(`^cls-[a-zA-Z0-9]{8}\s+\S+$`)

	for i, cluster := range r.Clusters {
		cluster = strings.TrimSpace(cluster)
		if cluster == "" {
			continue
		}

		if isEkletAgent {
			// ekletagent组件必须使用格式2：集群ID+命名空间
			if !clusterNamespacePattern.MatchString(cluster) {
				return fmt.Errorf("ekletagent组件的clusters[%d]必须使用'集群ID 命名空间'格式，当前值: %s", i, cluster)
			}
		} else {
			// 其他组件必须使用格式1：纯集群ID
			if !pureClusterPattern.MatchString(cluster) {
				return fmt.Errorf("组件%s的clusters[%d]必须使用纯集群ID格式，当前值: %s", r.Component, i, cluster)
			}
		}
	}

	return nil
}

func (r *UpdateTaskRequest) Validate() error {
	return validate.Struct(r)
}

func (r *BatchTerminateTasksRequest) Validate() error {
	return validate.Struct(r)
}

func (r *ExportClustersRequest) Validate() error {
	return validate.Struct(r)
}

func (r *RollbackClusterRequest) Validate() error {
	// 验证基本结构
	if err := validate.Struct(r); err != nil {
		return err
	}

	// 验证集群ID：必须提供单个集群ID或集群ID列表，但不能同时提供
	if len(r.ClusterIds) == 0 {
		return fmt.Errorf("必须提供cluster_ids")
	}

	// 验证集群ID格式
	clusterIdRegex := regexp.MustCompile(`^cls-[a-zA-Z0-9]{8}$`)

	if len(r.ClusterIds) > 0 {
		if len(r.ClusterIds) > 50 { // 限制批量回滚的集群数量
			return fmt.Errorf("批量回滚集群数量不能超过50个")
		}

		for _, clusterId := range r.ClusterIds {
			if !clusterIdRegex.MatchString(clusterId) {
				return fmt.Errorf("cluster_ids中包含格式不正确的集群ID: %s", clusterId)
			}
		}
	}

	return nil
}
