/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	scheme "git.code.oa.com/tke/api/client/clientset/versioned/scheme"
	v1 "git.code.oa.com/tke/api/platform/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// COSsGetter has a method to return a COSInterface.
// A group's client should implement this interface.
type COSsGetter interface {
	COSs() COSInterface
}

// COSInterface has methods to work with COS resources.
type COSInterface interface {
	Create(ctx context.Context, cOS *v1.COS, opts metav1.CreateOptions) (*v1.COS, error)
	Update(ctx context.Context, cOS *v1.COS, opts metav1.UpdateOptions) (*v1.COS, error)
	UpdateStatus(ctx context.Context, cOS *v1.COS, opts metav1.UpdateOptions) (*v1.COS, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.COS, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.COSList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.COS, err error)
	COSExpansion
}

// cOSs implements COSInterface
type cOSs struct {
	client rest.Interface
}

// newCOSs returns a COSs
func newCOSs(c *PlatformV1Client) *cOSs {
	return &cOSs{
		client: c.RESTClient(),
	}
}

// Get takes name of the cOS, and returns the corresponding cOS object, and an error if there is any.
func (c *cOSs) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.COS, err error) {
	result = &v1.COS{}
	err = c.client.Get().
		Resource("coss").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of COSs that match those selectors.
func (c *cOSs) List(ctx context.Context, opts metav1.ListOptions) (result *v1.COSList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.COSList{}
	err = c.client.Get().
		Resource("coss").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested cOSs.
func (c *cOSs) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("coss").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a cOS and creates it.  Returns the server's representation of the cOS, and an error, if there is any.
func (c *cOSs) Create(ctx context.Context, cOS *v1.COS, opts metav1.CreateOptions) (result *v1.COS, err error) {
	result = &v1.COS{}
	err = c.client.Post().
		Resource("coss").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(cOS).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a cOS and updates it. Returns the server's representation of the cOS, and an error, if there is any.
func (c *cOSs) Update(ctx context.Context, cOS *v1.COS, opts metav1.UpdateOptions) (result *v1.COS, err error) {
	result = &v1.COS{}
	err = c.client.Put().
		Resource("coss").
		Name(cOS.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(cOS).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *cOSs) UpdateStatus(ctx context.Context, cOS *v1.COS, opts metav1.UpdateOptions) (result *v1.COS, err error) {
	result = &v1.COS{}
	err = c.client.Put().
		Resource("coss").
		Name(cOS.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(cOS).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the cOS and deletes it. Returns an error if one occurs.
func (c *cOSs) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("coss").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched cOS.
func (c *cOSs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.COS, err error) {
	result = &v1.COS{}
	err = c.client.Patch(pt).
		Resource("coss").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
