package model

import (
	"fmt"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
)

// Component 结构体，存储组件信息
type Component struct {
	Name          string `yaml:"Name"`
	AppVersion    string `yaml:"AppVersion,omitempty"`
	PluginVersion string `yaml:"PluginVersion,omitempty"`
	ImageTag      string `yaml:"ImageTag,omitempty"`
	ExtendInfo    string `yaml:"ExtendInfo,omitempty"`
}

// UserData 结构体，存储用户相关信息
type UserData struct {
	User     string `yaml:"User"`
	AuthType string `yaml:"AuthType"`
	Token    string `yaml:"Token"`
	ChangeId string `yaml:"ChangeId"`
}

type Batch struct {
	Strategy      string `yaml:"Strategy"`
	BatchSize     int    `yaml:"BatchSize"`
	BatchInterval int    `yaml:"BatchInterval"`
}

// Task 结构体，存储 YAML 文件中的数据
type Task struct {
	Name               string    `yaml:"Name"`
	Description        string    `yaml:"Description"`
	Component          Component `yaml:"Component"`
	UserData           UserData  `yaml:"Userdata"`
	Batch              Batch     `yaml:"Batch"`
	Cluster            []string  `yaml:"Cluster"`
	ClusterFile        string    `yaml:"ClusterFile"`
	ClusterFileContent string    `yaml:"ClusterFileContent,omitempty"` // 新增文件内容字段
	ClusterType        string
	ClusterOption      string
	// 基础分批策略
	BatchStrategies []int
	// 业务分批策略
	AppBatchStrategy  int
	ProjectFile       string `yaml:"ProjectFile"`
	OrchestrationMode int    `yaml:"OrchestrationMode"`
	AutoConfirm       bool   `yaml:"AutoConfirm"`
}

func (t *Task) Validate() error {
	if t.Component.Name == "" {
		return fmt.Errorf("component name cannot be empty")
	}
	if t.UserData.User == "" {
		return fmt.Errorf("user cannot be empty")
	}
	if t.UserData.AuthType != "cert" && t.UserData.Token == "" {
		return fmt.Errorf("token cannot be empty when authtype is not cert")
	}
	if t.UserData.ChangeId == "" {
		return fmt.Errorf("ChangeId cannot be empty")
	}

	if t.Component.Name == consts.APPFABRIC {
		if len(t.Cluster) == 0 {
			return fmt.Errorf("cluster cannot be empty for appfabricapplication")
		}
		if t.ProjectFile == "" {
			return fmt.Errorf("projectFile cannot be empty for appfabricapplication")
		}
		if t.Component.ExtendInfo == "" {
			return fmt.Errorf("ExtendInfo cannot be empty for appfabricapplication")
		}
	} else {
		if t.ClusterFile == "" {
			return fmt.Errorf("clusterFile cannot be empty")
		}
	}
	return nil
}
