/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "git.code.oa.com/tke/api/client/clientset/versioned/typed/platform/v1"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
)

type FakePlatformV1 struct {
	*testing.Fake
}

func (c *FakePlatformV1) AddonTypes() v1.AddonTypeInterface {
	return &FakeAddonTypes{c}
}

func (c *FakePlatformV1) CBSs() v1.CBSInterface {
	return &FakeCBSs{c}
}

func (c *FakePlatformV1) CFSs() v1.CFSInterface {
	return &FakeCFSs{c}
}

func (c *FakePlatformV1) COSs() v1.COSInterface {
	return &FakeCOSs{c}
}

func (c *FakePlatformV1) Clusters() v1.ClusterInterface {
	return &FakeClusters{c}
}

func (c *FakePlatformV1) ClusterAPIResources() v1.ClusterAPIResourceInterface {
	return &FakeClusterAPIResources{c}
}

func (c *FakePlatformV1) ClusterAddons() v1.ClusterAddonInterface {
	return &FakeClusterAddons{c}
}

func (c *FakePlatformV1) ClusterAddonKinds() v1.ClusterAddonKindInterface {
	return &FakeClusterAddonKinds{c}
}

func (c *FakePlatformV1) ClusterAddonTypes() v1.ClusterAddonTypeInterface {
	return &FakeClusterAddonTypes{c}
}

func (c *FakePlatformV1) ClusterAuthentications(namespace string) v1.ClusterAuthenticationInterface {
	return &FakeClusterAuthentications{c, namespace}
}

func (c *FakePlatformV1) ConfigMaps() v1.ConfigMapInterface {
	return &FakeConfigMaps{c}
}

func (c *FakePlatformV1) DNSAutoscalers() v1.DNSAutoscalerInterface {
	return &FakeDNSAutoscalers{c}
}

func (c *FakePlatformV1) DeSchedulers() v1.DeSchedulerInterface {
	return &FakeDeSchedulers{c}
}

func (c *FakePlatformV1) DynamicSchedulers() v1.DynamicSchedulerInterface {
	return &FakeDynamicSchedulers{c}
}

func (c *FakePlatformV1) EniIpamds() v1.EniIpamdInterface {
	return &FakeEniIpamds{c}
}

func (c *FakePlatformV1) Environments() v1.EnvironmentInterface {
	return &FakeEnvironments{c}
}

func (c *FakePlatformV1) GPUManagers() v1.GPUManagerInterface {
	return &FakeGPUManagers{c}
}

func (c *FakePlatformV1) GameApps() v1.GameAppInterface {
	return &FakeGameApps{c}
}

func (c *FakePlatformV1) HPCs() v1.HPCInterface {
	return &FakeHPCs{c}
}

func (c *FakePlatformV1) Helms() v1.HelmInterface {
	return &FakeHelms{c}
}

func (c *FakePlatformV1) ImageP2Ps() v1.ImageP2PInterface {
	return &FakeImageP2Ps{c}
}

func (c *FakePlatformV1) LBCFs() v1.LBCFInterface {
	return &FakeLBCFs{c}
}

func (c *FakePlatformV1) LogCollectors() v1.LogCollectorInterface {
	return &FakeLogCollectors{c}
}

func (c *FakePlatformV1) NamespaceSets() v1.NamespaceSetInterface {
	return &FakeNamespaceSets{c}
}

func (c *FakePlatformV1) NetworkPolicies() v1.NetworkPolicyInterface {
	return &FakeNetworkPolicies{c}
}

func (c *FakePlatformV1) NginxIngresses() v1.NginxIngressInterface {
	return &FakeNginxIngresses{c}
}

func (c *FakePlatformV1) NodeLocalDNSCaches() v1.NodeLocalDNSCacheInterface {
	return &FakeNodeLocalDNSCaches{c}
}

func (c *FakePlatformV1) NodeProblemDetectors() v1.NodeProblemDetectorInterface {
	return &FakeNodeProblemDetectors{c}
}

func (c *FakePlatformV1) OLMs() v1.OLMInterface {
	return &FakeOLMs{c}
}

func (c *FakePlatformV1) OOMGuards() v1.OOMGuardInterface {
	return &FakeOOMGuards{c}
}

func (c *FakePlatformV1) PersistentEvents() v1.PersistentEventInterface {
	return &FakePersistentEvents{c}
}

func (c *FakePlatformV1) Projects() v1.ProjectInterface {
	return &FakeProjects{c}
}

func (c *FakePlatformV1) QGPUs() v1.QGPUInterface {
	return &FakeQGPUs{c}
}

func (c *FakePlatformV1) Registries() v1.RegistryInterface {
	return &FakeRegistries{c}
}

func (c *FakePlatformV1) Tcrs() v1.TcrInterface {
	return &FakeTcrs{c}
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *FakePlatformV1) RESTClient() rest.Interface {
	var ret *rest.RESTClient
	return ret
}
