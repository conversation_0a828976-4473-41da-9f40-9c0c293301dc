package clusterinfo

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/Masterminds/semver"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"git.woa.com/kmetis/starship-engine/pkg/config"
)

var (
	clsInfoHelper *ClusterInfoHelper
	once          sync.Once
)

const (
	dataSource = "%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=10s"
)

type SyncAppClusters struct {
	ClusterID      string `gorm:"column:cluster_id"`
	ClusterVersion string `gorm:"column:cluster_version"`
	CLusterLevel   string `gorm:"column:cluster_level"`
	AppID          string `gorm:"column:appid"`
	Region         string `gorm:"column:region"`
	Status         string `gorm:"column:status"`
	Type           string `gorm:"column:type"`
	AppVersion     string `gorm:"column:app_version"`
	IsBigUser      bool   `gorm:"column:is_big_user"`
	CreatedAt      string `gorm:"column:created_at"`
}

type EksPodNumberStat struct {
	ClusterID string `gorm:"column:cluster_id"`
	Cnt       int64  `gorm:"column:cnt"`
}

type FilterClusterOptions struct {
	Type            string   `json:"type"`
	Versions        []string `json:"versions"`
	AppIDs          []string `json:"appIDs"`
	ExcludeClusters []string `json:"excludeClusters"`
}

type EksPodNumberCache struct {
	cache    map[string]int64
	mu       sync.Mutex
	UpdateAt int64
}

type ClusterInfoHelper struct {
	syncClsDB *gorm.DB
	// 缓存cluster对应的eks pod数量
	eksPodNumberCache EksPodNumberCache
}

func NewClusterInfoHelper(syncClsDBConfig config.Database) (*ClusterInfoHelper, error) {
	syncClsDB, err := gorm.Open(mysql.Open(fmt.Sprintf(dataSource, syncClsDBConfig.DbUser, syncClsDBConfig.DbPasswd, syncClsDBConfig.DbHost, syncClsDBConfig.DbPort, syncClsDBConfig.DbDatabase)), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	return &ClusterInfoHelper{
		syncClsDB: syncClsDB,
		eksPodNumberCache: EksPodNumberCache{
			cache: make(map[string]int64),
		},
	}, nil
}

func (c *ClusterInfoHelper) GetEksPodNumber(clusterID string) (int64, error) {
	if time.Now().Unix()-c.eksPodNumberCache.UpdateAt < 3600 {
		n, _ := c.eksPodNumberCache.cache[clusterID]
		return n, nil
	}

	podNumber, err := StatEksPodNumber()
	if err != nil {
		return 0, err
	}
	c.eksPodNumberCache.mu.Lock()
	defer c.eksPodNumberCache.mu.Unlock()
	c.eksPodNumberCache.cache = podNumber
	c.eksPodNumberCache.UpdateAt = time.Now().Unix()
	n, _ := c.eksPodNumberCache.cache[clusterID]
	return n, nil
}

func (c *ClusterInfoHelper) CountClusterNodes(appName, cluster string) (int, error) {
	var n int
	err := c.syncClsDB.Table(fmt.Sprintf("%s_cluster", appName)).
		Select("node_count").
		Where("cluster_id = ? and created_at = max(created_at) ", cluster).
		Find(&n).
		Error
	if err != nil {
		return n, err
	}
	return n, nil
}

func (c *ClusterInfoHelper) IsBigUser(appName, cluster string) (bool, error) {
	var isBigUser int
	err := c.syncClsDB.Table(fmt.Sprintf("%s_cluster", appName)).
		Select("is_big_user").
		Where("cluster_id = ? and created_at = max(created_at)", cluster).
		Find(&isBigUser).
		Error
	return isBigUser == 1, err
}

func (c *ClusterInfoHelper) GetClusterLevel(appName, cluster string) (string, error) {
	var clusterLevel string
	err := c.syncClsDB.Table(fmt.Sprintf("%s_cluster", appName)).
		Select("cluster_level").
		Where("cluster_id = ? and created_at = max(created_at)", cluster).
		Find(&clusterLevel).
		Error
	return clusterLevel, err
}

func CompareVersion(v1, v2 string) (int, error) {
	version1, err := semver.NewVersion(v1)
	if err != nil {
		return 0, fmt.Errorf("invalid version format: %s (%v)", v1, err)
	}
	version2, err := semver.NewVersion(v2)
	if err != nil {
		return 0, fmt.Errorf("invalid version format: %s (%v)", v2, err)
	}
	return version1.Compare(version2), nil
}

func CompareClusterLevel(l1, l2 string) (int, error) {
	if !strings.HasPrefix(l1, "L") || !strings.HasPrefix(l2, "L") {
		return 0, fmt.Errorf("invalid cluster level format: %s, %s", l1, l2)
	}
	if l1 == l2 {
		return 0, nil
	}
	leve1, err := strconv.Atoi(l1[1:])
	if err != nil {
		return 0, fmt.Errorf("invalid cluster level format: %s, %s", l1, l2)
	}
	level2, err := strconv.Atoi(l2[1:])
	if err != nil {
		return 0, fmt.Errorf("invalid cluster level format: %s, %s", l1, l2)
	}
	return leve1 - level2, nil
}

// 解析逗号分隔的字符串列表
func parseStringList(input string) []string {
	cleaned := strings.ReplaceAll(input, " ", "")
	cleaned = strings.NewReplacer("(", "", ")", "", "\"", "", "'", "").Replace(cleaned)

	var values []string
	for _, v := range strings.Split(cleaned, ",") {
		if trimmed := strings.TrimSpace(v); trimmed != "" {
			values = append(values, fmt.Sprintf("'%s'", trimmed))
		}
	}
	return values
}

func initClsInfoHelper() error {
	var err error
	once.Do(func() {
		clsInfoHelper, err = NewClusterInfoHelper(config.GetSyncClusterDatabaseConfig())
	})
	return err
}

func GetClsInfoHelper() (*ClusterInfoHelper, error) {
	return nil, fmt.Errorf("method not implemented")
	//if err := initClsInfoHelper(); err != nil {
	//	return nil, fmt.Errorf("failed to init ClusterInfoHelper: %v", err)
	//}
	//return clsInfoHelper, nil
}
