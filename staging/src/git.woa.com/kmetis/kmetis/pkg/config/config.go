package config

import (
	"io/ioutil"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"
)

const (
	workloadKindDeployment  = "Deployment"
	workloadAPIVersion      = "apps/v1"
	TKEProductName          = "tke"
	EKSProductName          = "eks"
	TKEClusterType          = "cloud.tencent.com/tke-cluster-type"
	TKEClusterAppId         = "cloud.tencent.com/tke-cluster-appid"
	TKEClusterLevel         = "cloud.tencent.com/tke-cluster-level"
	EKSClusterAppId         = "cloud.tencent.com/eks-cluster-appid"
	TKEClusterRegion        = "cloud.tencent.com/tke-cluster-region"
	EKSClusterRegion        = "cloud.tencent.com/eks-cluster-region"
	EKSClusterConfigName    = "extra-config"
	TKEOperatorScaleEnable  = "cloud.tencent.com/autoscale-enabled"
	TKEMasterScalerName     = "cloud.tencent.com/tke-master-scaler-name"
	DefaultMasterScalerName = "autopilot-controller"
	// DefaultScaleDownCPUUtilPercentageThreshold defines the cpu scaledown threshold,,
	// If the ratio of actual used cpu resources divided by request resources is less than DefaultScaleDownCPUUtilPercentageThreshold,
	// it will trigger cpu scaledown.
	DefaultScaleDownCPUUtilPercentageThreshold = int32(30)
	// DefaultScaleUpCPUUtilPercentageThreshold defines the cpu scaleup threshold,,
	// If the ratio of actual used cpu resources divided by limit resources is greater than DefaultScaleUpCPUUtilPercentageThreshold,
	// it will trigger cpu scaleup.
	DefaultScaleUpCPUUtilPercentageThreshold = int32(95)
	// DefaultScaleDownMemoryUtilPercentageThreshold defines the memory scaledown threshold,,
	// If the ratio of actual used memory resources divided by request resources is less than DefaultScaleDownMemoryUtilPercentageThreshold,
	// it will trigger memory scaledown.
	DefaultScaleDownMemoryUtilPercentageThreshold = int32(30)
	// DefaultScaleUpMemoryUtilPercentageThreshold defines the memory scaleup threshold,,
	// If the ratio of actual used cpu resources divided by limit resources is greater than DefaultScaleUpCPUUtilPercentageThreshold,
	// it will trigger memory scaleup.
	DefaultScaleUpMemoryUtilPercentageThreshold           = int32(95)
	DefaultReplicasScaleDownCPUUtilPercentageThreshold    = int32(50)
	DefaultReplicasScaleDownMemoryUtilPercentageThreshold = int32(50)
	DefaultReconcileClusterLevelStabWindowSeconds         = int32(45)
	DefaultClusterScaleDownStabWindowSeconds              = int32(600)
	DefaultClusterScaleDownReplicasStabWindowSeconds      = int32(1800)
	DefaultClusterScaleUpStabWindowSeconds                = int32(120)
	DefaultClusterScaleUpReplicasStabWindowSeconds        = int32(300)
	DefaultComponentScaleDownStabWindowSeconds            = int32(43200)
	DefaultComponentScaleUpStabWindowSeconds              = int32(150)

	LabelMasterName                           = "master.cloud.tencent.com/name"
	LabelMasterComponent                      = "master.cloud.tencent.com/component"
	LabelTKEComponent                         = "cloud.tencent.com/tke-component"
	LabelTKEClusterMonitorComponent           = "k8s-app"
	LabelTKEClusterAutoScalerComponent        = "qcloud-app"
	LabelTKEClusterId                         = "cloud.tencent.com/tke-cluster-id"
	TkeAnnotationClusterComponentOOMKilled    = "cloud.tencent.com/tke-component-oomkilled-record"
	TKEMasterEmptyRecycle                     = "cloud.tencent.com/tke-cluster-idle"
	TKEMasterIsolateCluster                   = "cloud.tencent.com/tke-cluster-isolate"
	AutopilotAutoscalingFlag                  = "cloud.tencent.com/autopilot-autoscale-enabled"
	LabelTKEClusterLevel                      = "cloud.tencent.com/tke-cluster-level"
	LabelTKEForceResyncClusterLevel           = "cloud.tencent.com/force-resync-cluster-level"
	LableTKESkipResetTerminatedPodGCThreshold = "cloud.tencent.com/skip-reset-terminated-pod-gc-threshold"
	LabelMaintenanceWindow                    = "cloud.tencent.com/maintenance-window"

	LowResourceUtilizationTrigger  = "LowResourceUtilization"
	HighResourceUtilizationTrigger = "HighResourceUtilization"
	TooManyNodesTrigger            = "TooManyNodes"
	OOMEventTrigger                = "OOMEvent"
	ClusterLevelChangeTrigger      = "ClusterLevelChange"

	DefaultMaxRetryNum                    = 2
	DefaultClusterCreationSecond          = 600
	DefaultSmallClusterAge                = 4 * time.Hour
	DefaultMinReplicas                    = 1
	DefaultMaxReplicas                    = 3
	DefaultOtherComponentMinCpuMillAmount = 30                     // 30m
	DefaultOtherComponentMinMemoryBytes   = 50 * 1024 * 1024       // 50Mi
	DefaultKubeApiserverMinCpuMillAmount  = 100                    // 100m
	DefaultKubeApiserverMinMemoryBytes    = 512 * 1024 * 1024      // 512Mi
	DefaultComponentMaxCpuMillAmount      = 3 * 1000               // 3 core
	DefaultComponentMaxMemoryBytes        = 8 * 1024 * 1024 * 1024 // 8G
	DefaultMaxCPUMemoryGapRatio           = 4.0
	DefaultMinCPUMemoryGapRatio           = 1.0
	DefaultComponentMinLimitCpuMillAmount = 200                    // 200m core
	DefaultComponentMinLimitMemoryBytes   = 512 * 1024 * 1024      // 512Mi
	DefaultLargeCPUMillAmount             = 4000                   // 4 core
	DefaultLargeMemoryBytes               = 8 * 1024 * 1024 * 1024 // 8G
	DefaultResyncClusterNodeIntervalTime  = 1 * time.Minute
	DefaultResyncClusterPodIntervalTime   = 1 * time.Minute
	DefaultSystemNamespace                = "kmetis"
	DefaultMaxOOMInterval                 = 24 * time.Hour
	DefaultOOMValidTime                   = 7 * 24 * time.Hour
	DefaultMetricsTimeout                 = 3 * time.Second
	DefaultMaxClusterPod                  = 50
	DefaultExpensiveOpCostMsThreshold     = 600
	DefaultBatchToken                     = 6
	DefaultApiServerSecurePort            = 60002
	DefaultLegacyClusterLevel             = "L1"
	DefaultLegacyIndependentEtcdPrefix    = "169"
	DefaultMaxClusterNode                 = 500
	DefaultMaxClusterLevel                = "L500"
	DefaultEtcdTypeKey                    = "etcdType"
	ShareEtcd                             = "Share"
	IndependentEtcd                       = "Independent"

	MaxSourceNode                = 50
	DisableScaleDownFlag         = "disable"
	EnableScaleDownFlag          = "enable"
	MaxEvictNodePodTimeout       = 30 * time.Minute
	MinReplicas                  = 2
	MinRecommendationHistoryTime = 10 * time.Minute

	ComponentApiserver         = "apiserver"
	ComponentScheduler         = "scheduler"
	ComponentControllerManager = "controller-manager"
	ComponentServiceController = "service-controller"
	ComponentClusterMonitor    = "cluster-monitor"
	ComponentKubeJarvis        = "kube-jarvis"
	ComponentProxy             = "proxy"
	ComponentCSIController     = "csi-cbs-controller"
	ComponentEklet             = "eklet"
	ComponentEksController     = "eks-controllers"
	ComponentClusterAutoScaler = "cluster-autoscaler"
	ComponentHpaMetricsServer  = "hpa-metrics-server"

	DefaultWhitelistAppidCmName                   = "whitelist"
	DefaultAutopilotNamespace                     = "kmetis"
	KubeApiMaxRequestsInflight                    = "max-requests-inflight"
	KubeApiMaxMutatingRequestsInflight            = "max-mutating-requests-inflight"
	KubeApiQps                                    = "kube-api-qps"
	KubeApiBurst                                  = "kube-api-burst"
	KubeControllerManagerTerminatedPodGCThreshold = "terminated-pod-gc-threshold"
	DefaultAffinityClusterNameKey                 = "master.cloud.tencent.com/name"
	DefaultAffinityComponentNameKey               = "master.cloud.tencent.com/component"
	DefaultTerminatedPodGCThreshold               = 50
	DefaultKubeSystemNamespace                    = "kube-system"
	TKEClusterLevelPrefix                         = "L"
	DefaultMinIntervalTime                        = 3 * time.Minute
	DefaultUpdateAvaiComponentIntevalSecond       = 60 * time.Second
	DefaultUpdateClusterResourceIntevalSecond     = 60 * time.Minute
	MemoryGBUnit                                  = 1 * 1024 * 1024 * 1024
	IndependentCluster                            = "independent"

	NodeConditionKernelDeadlock = "KernelDeadlock"
)

var (
	DefaultExcludeComponentList           = []string{ComponentKubeJarvis, ComponentProxy, ComponentHpaMetricsServer}
	K8sCoreComponentList                  = []string{ComponentApiserver, ComponentControllerManager, ComponentScheduler}
	NonClusterNamePrefixComponentList     = []string{}
	DisableScaleDownComponentList         = []string{}
	DefaultClusterNamePrefixComponentList = []string{ComponentClusterMonitor, ComponentEklet, ComponentEksController, ComponentClusterAutoScaler}
)

type Config struct {
	Product                        string
	AutopilotDbAddr                string
	MinRecommendationHistoryTime   time.Duration
	QPS                            float64
	Burst                          int
	EnableAutoScaling              bool
	EnableRateLimiter              bool
	EnableClusterLevelFeature      bool
	EnableScaleDownReplicasFeature bool
	EnableScaleUpReplicasFeature   bool
	Percentage                     int
	MaxUpdateComponentsInflight    uint64
	FeatureGates                   FeatureGates
	ClusterLevelConf               *ClusterConfig
}

type ClusterConfig struct {
	ClusterLevel TKEClusterLevelConfig `yaml:"clusterLevel"`
	AutoScaling  AutoScalingConfig     `yaml:"autoScaling"`
}

type TKEClusterLevelConfig struct {
	L5    ClusterLevelConfig `yaml:"l5"`
	L20   ClusterLevelConfig `yaml:"l20"`
	L50   ClusterLevelConfig `yaml:"l50"`
	L100  ClusterLevelConfig `yaml:"l100"`
	L200  ClusterLevelConfig `yaml:"l200"`
	L500  ClusterLevelConfig `yaml:"l500"`
	L1000 ClusterLevelConfig `yaml:"l1000"`
	L3000 ClusterLevelConfig `yaml:"l3000"`
	L5000 ClusterLevelConfig `yaml:"l5000"`
}

type ClusterLevelConfig struct {
	LevelName                     string                  `yaml:"levelName"`
	Apiserver                     ComponentResourceConfig `yaml:"apiserver"`
	ControllerManager             ComponentResourceConfig `yaml:"controllerManager"`
	Scheduler                     ComponentResourceConfig `yaml:"scheduler"`
	OtherComponent                ComponentResourceConfig `yaml:"otherComponent"`
	EnableScaleDown               bool                    `yaml:"enableScaleDown"`
	EnableScaleUp                 bool                    `yaml:"enableScaleUp"`
	EnableEventEtcd               bool                    `yaml:"enableEventEtcd"`
	TotalMillCpuAmount            int64                   `yaml:"totalMillCpuAmount"`
	TotalMemoryBytesAmount        int64                   `yaml:"totalMemoryBytesAmount"`
	MaxTotalMillCpuAmount         int64                   `yaml:"maxTotalMillCpuAmount"`
	MaxTotalMemoryBytesAmount     int64                   `yaml:"maxTotalMemoryBytesAmount"`
	MaxNode                       int                     `yaml:"maxNode"`
	Affinity                      corev1.Affinity         `yaml:"affinity,omitempty"`
	Tolerations                   []corev1.Toleration     `yaml:"tolerations,omitempty"`
	MaxMutatingRequestsInflight   int                     `yaml:"maxMutatingRequestsInflight,omitempty"`
	MaxRequestsInflight           int                     `yaml:"maxRequestsInflight,omitempty"`
	SchedulerKubeApiQps           int                     `yaml:"schedulerKubeApiQps,omitempty"`
	SchedulerKubeApiBurst         int                     `yaml:"schedulerKubeApiBurst,omitempty"`
	ControllerManagerKubeApiQps   int                     `yaml:"controllerManagerKubeApiQps,omitempty"`
	ControllerManagerKubeApiBurst int                     `yaml:"controllerManagerKubeApiBurst,omitempty"`
	EnableScaleDownReplicas       bool                    `yaml:"enableScaleDownReplicas,omitempty"`
	TerminatedPodGCThreshold      int                     `yaml:"terminatedPodGCThreshold,omitempty"`
}

type ComponentResourceConfig struct {
	Min corev1.ResourceRequirements `yaml:"min"`
	Max corev1.ResourceRequirements `yaml:"max"`
}

type AutoScalingConfig struct {
	MaxClusterNodeThreshold           int    `yaml:"maxClusterNodeThreshold"`
	MaxClusterLevel                   string `yaml:"maxClusterLevel"`
	MinClusterLevelForIndependentEtcd string `yaml:"minClusterLevelForIndependentEtcd"`
}

// GetClusterConfig get the global config
func GetClusterConfig(filename string) (*ClusterConfig, error) {
	b, err := ioutil.ReadFile(filename)
	if err != nil {
		klog.Errorf("failed to read config %s: %v", filename, err)
		return nil, err
	}
	conf := &ClusterConfig{}
	err = yaml.Unmarshal(b, conf)
	if err != nil {
		klog.Errorf("unmarshal config: %v", err)
		return nil, err
	}
	return conf, nil
}

func SetNonClusterNamePrefixComponentList(components []string) {
	NonClusterNamePrefixComponentList = components
	klog.V(2).Infof("NonClusterNamePrefixComponentList is %v", NonClusterNamePrefixComponentList)
}

func SetDisableScaleDownComponentList(components []string) {
	DisableScaleDownComponentList = components
	klog.V(2).Infof("DisableScaleDownComponentList is %v", components)
}
