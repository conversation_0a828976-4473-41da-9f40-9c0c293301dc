package util

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// BindAndValidate 统一处理参数绑定和校验
// 支持同时绑定URI和JSON参数，并进行校验
func BindAndValidate(c *gin.Context, req interface{}) error {
	// 绑定URI参数
	if err := c.ShouldBind<PERSON>ri(req); err != nil {
		return err
	}
	if c.Request.Method == http.MethodPost || c.Request.Method == http.MethodPut || c.Request.Method == http.MethodDelete {
		// 绑定JSON参数
		c.ShouldBindJSON(req)
	}
	if c.Request.Method == http.MethodGet {
		c.ShouldBind<PERSON>uery(req)
	}
	// 执行参数校验
	if err := validator.New().Struct(req); err != nil {
		return err
	}
	return nil
}
