/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2020 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */
// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: tkestack.io/tke/api/application/v1/generated.proto

package v1

import (
	fmt "fmt"

	io "io"

	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_sortkeys "github.com/gogo/protobuf/sortkeys"

	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func (m *App) Reset()      { *m = App{} }
func (*App) ProtoMessage() {}
func (*App) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{0}
}
func (m *App) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *App) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *App) XXX_Merge(src proto.Message) {
	xxx_messageInfo_App.Merge(m, src)
}
func (m *App) XXX_Size() int {
	return m.Size()
}
func (m *App) XXX_DiscardUnknown() {
	xxx_messageInfo_App.DiscardUnknown(m)
}

var xxx_messageInfo_App proto.InternalMessageInfo

func (m *AppHistory) Reset()      { *m = AppHistory{} }
func (*AppHistory) ProtoMessage() {}
func (*AppHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{1}
}
func (m *AppHistory) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AppHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppHistory.Merge(m, src)
}
func (m *AppHistory) XXX_Size() int {
	return m.Size()
}
func (m *AppHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_AppHistory.DiscardUnknown(m)
}

var xxx_messageInfo_AppHistory proto.InternalMessageInfo

func (m *AppHistorySpec) Reset()      { *m = AppHistorySpec{} }
func (*AppHistorySpec) ProtoMessage() {}
func (*AppHistorySpec) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{2}
}
func (m *AppHistorySpec) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppHistorySpec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AppHistorySpec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppHistorySpec.Merge(m, src)
}
func (m *AppHistorySpec) XXX_Size() int {
	return m.Size()
}
func (m *AppHistorySpec) XXX_DiscardUnknown() {
	xxx_messageInfo_AppHistorySpec.DiscardUnknown(m)
}

var xxx_messageInfo_AppHistorySpec proto.InternalMessageInfo

func (m *AppList) Reset()      { *m = AppList{} }
func (*AppList) ProtoMessage() {}
func (*AppList) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{3}
}
func (m *AppList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AppList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppList.Merge(m, src)
}
func (m *AppList) XXX_Size() int {
	return m.Size()
}
func (m *AppList) XXX_DiscardUnknown() {
	xxx_messageInfo_AppList.DiscardUnknown(m)
}

var xxx_messageInfo_AppList proto.InternalMessageInfo

func (m *AppResource) Reset()      { *m = AppResource{} }
func (*AppResource) ProtoMessage() {}
func (*AppResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{4}
}
func (m *AppResource) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AppResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppResource.Merge(m, src)
}
func (m *AppResource) XXX_Size() int {
	return m.Size()
}
func (m *AppResource) XXX_DiscardUnknown() {
	xxx_messageInfo_AppResource.DiscardUnknown(m)
}

var xxx_messageInfo_AppResource proto.InternalMessageInfo

func (m *AppResourceSpec) Reset()      { *m = AppResourceSpec{} }
func (*AppResourceSpec) ProtoMessage() {}
func (*AppResourceSpec) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{5}
}
func (m *AppResourceSpec) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppResourceSpec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AppResourceSpec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppResourceSpec.Merge(m, src)
}
func (m *AppResourceSpec) XXX_Size() int {
	return m.Size()
}
func (m *AppResourceSpec) XXX_DiscardUnknown() {
	xxx_messageInfo_AppResourceSpec.DiscardUnknown(m)
}

var xxx_messageInfo_AppResourceSpec proto.InternalMessageInfo

func (m *AppSpec) Reset()      { *m = AppSpec{} }
func (*AppSpec) ProtoMessage() {}
func (*AppSpec) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{6}
}
func (m *AppSpec) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppSpec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AppSpec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppSpec.Merge(m, src)
}
func (m *AppSpec) XXX_Size() int {
	return m.Size()
}
func (m *AppSpec) XXX_DiscardUnknown() {
	xxx_messageInfo_AppSpec.DiscardUnknown(m)
}

var xxx_messageInfo_AppSpec proto.InternalMessageInfo

func (m *AppStatus) Reset()      { *m = AppStatus{} }
func (*AppStatus) ProtoMessage() {}
func (*AppStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{7}
}
func (m *AppStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AppStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppStatus.Merge(m, src)
}
func (m *AppStatus) XXX_Size() int {
	return m.Size()
}
func (m *AppStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_AppStatus.DiscardUnknown(m)
}

var xxx_messageInfo_AppStatus proto.InternalMessageInfo

func (m *AppValues) Reset()      { *m = AppValues{} }
func (*AppValues) ProtoMessage() {}
func (*AppValues) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{8}
}
func (m *AppValues) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppValues) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *AppValues) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppValues.Merge(m, src)
}
func (m *AppValues) XXX_Size() int {
	return m.Size()
}
func (m *AppValues) XXX_DiscardUnknown() {
	xxx_messageInfo_AppValues.DiscardUnknown(m)
}

var xxx_messageInfo_AppValues proto.InternalMessageInfo

func (m *Chart) Reset()      { *m = Chart{} }
func (*Chart) ProtoMessage() {}
func (*Chart) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{9}
}
func (m *Chart) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Chart) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *Chart) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Chart.Merge(m, src)
}
func (m *Chart) XXX_Size() int {
	return m.Size()
}
func (m *Chart) XXX_DiscardUnknown() {
	xxx_messageInfo_Chart.DiscardUnknown(m)
}

var xxx_messageInfo_Chart proto.InternalMessageInfo

func (m *ConfigMap) Reset()      { *m = ConfigMap{} }
func (*ConfigMap) ProtoMessage() {}
func (*ConfigMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{10}
}
func (m *ConfigMap) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConfigMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ConfigMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigMap.Merge(m, src)
}
func (m *ConfigMap) XXX_Size() int {
	return m.Size()
}
func (m *ConfigMap) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigMap.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigMap proto.InternalMessageInfo

func (m *ConfigMapList) Reset()      { *m = ConfigMapList{} }
func (*ConfigMapList) ProtoMessage() {}
func (*ConfigMapList) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{11}
}
func (m *ConfigMapList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConfigMapList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ConfigMapList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigMapList.Merge(m, src)
}
func (m *ConfigMapList) XXX_Size() int {
	return m.Size()
}
func (m *ConfigMapList) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigMapList.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigMapList proto.InternalMessageInfo

func (m *History) Reset()      { *m = History{} }
func (*History) ProtoMessage() {}
func (*History) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{12}
}
func (m *History) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *History) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *History) XXX_Merge(src proto.Message) {
	xxx_messageInfo_History.Merge(m, src)
}
func (m *History) XXX_Size() int {
	return m.Size()
}
func (m *History) XXX_DiscardUnknown() {
	xxx_messageInfo_History.DiscardUnknown(m)
}

var xxx_messageInfo_History proto.InternalMessageInfo

func (m *ResourceValues) Reset()      { *m = ResourceValues{} }
func (*ResourceValues) ProtoMessage() {}
func (*ResourceValues) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{13}
}
func (m *ResourceValues) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResourceValues) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ResourceValues) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourceValues.Merge(m, src)
}
func (m *ResourceValues) XXX_Size() int {
	return m.Size()
}
func (m *ResourceValues) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourceValues.DiscardUnknown(m)
}

var xxx_messageInfo_ResourceValues proto.InternalMessageInfo

func (m *RollbackProxyOptions) Reset()      { *m = RollbackProxyOptions{} }
func (*RollbackProxyOptions) ProtoMessage() {}
func (*RollbackProxyOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f4cb6939211b33c, []int{14}
}
func (m *RollbackProxyOptions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RollbackProxyOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *RollbackProxyOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollbackProxyOptions.Merge(m, src)
}
func (m *RollbackProxyOptions) XXX_Size() int {
	return m.Size()
}
func (m *RollbackProxyOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_RollbackProxyOptions.DiscardUnknown(m)
}

var xxx_messageInfo_RollbackProxyOptions proto.InternalMessageInfo

func init() {
	proto.RegisterType((*App)(nil), "tkestack.io.tke.api.application.v1.App")
	proto.RegisterType((*AppHistory)(nil), "tkestack.io.tke.api.application.v1.AppHistory")
	proto.RegisterType((*AppHistorySpec)(nil), "tkestack.io.tke.api.application.v1.AppHistorySpec")
	proto.RegisterType((*AppList)(nil), "tkestack.io.tke.api.application.v1.AppList")
	proto.RegisterType((*AppResource)(nil), "tkestack.io.tke.api.application.v1.AppResource")
	proto.RegisterType((*AppResourceSpec)(nil), "tkestack.io.tke.api.application.v1.AppResourceSpec")
	proto.RegisterMapType((Resources)(nil), "tkestack.io.tke.api.application.v1.AppResourceSpec.ResourcesEntry")
	proto.RegisterType((*AppSpec)(nil), "tkestack.io.tke.api.application.v1.AppSpec")
	proto.RegisterType((*AppStatus)(nil), "tkestack.io.tke.api.application.v1.AppStatus")
	proto.RegisterType((*AppValues)(nil), "tkestack.io.tke.api.application.v1.AppValues")
	proto.RegisterType((*Chart)(nil), "tkestack.io.tke.api.application.v1.Chart")
	proto.RegisterType((*ConfigMap)(nil), "tkestack.io.tke.api.application.v1.ConfigMap")
	proto.RegisterMapType((map[string][]byte)(nil), "tkestack.io.tke.api.application.v1.ConfigMap.BinaryDataEntry")
	proto.RegisterMapType((map[string]string)(nil), "tkestack.io.tke.api.application.v1.ConfigMap.DataEntry")
	proto.RegisterType((*ConfigMapList)(nil), "tkestack.io.tke.api.application.v1.ConfigMapList")
	proto.RegisterType((*History)(nil), "tkestack.io.tke.api.application.v1.History")
	proto.RegisterType((*ResourceValues)(nil), "tkestack.io.tke.api.application.v1.ResourceValues")
	proto.RegisterType((*RollbackProxyOptions)(nil), "tkestack.io.tke.api.application.v1.RollbackProxyOptions")
}

func init() {
	proto.RegisterFile("tkestack.io/tke/api/application/v1/generated.proto", fileDescriptor_3f4cb6939211b33c)
}

var fileDescriptor_3f4cb6939211b33c = []byte{
	// 1517 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0xcd, 0x6f, 0x5b, 0xc5,
	0x16, 0xcf, 0xf5, 0x47, 0xe2, 0x3b, 0xf9, 0x7c, 0xf3, 0xfa, 0xde, 0xf3, 0x0b, 0xc8, 0x8e, 0x8c,
	0x54, 0x52, 0x4a, 0xaf, 0x49, 0xf8, 0x68, 0xd5, 0xaa, 0x20, 0xdf, 0x04, 0xda, 0xa2, 0xa4, 0xad,
	0xa6, 0x4e, 0x11, 0x88, 0x05, 0x93, 0xeb, 0x89, 0x73, 0xb1, 0x7d, 0xef, 0x30, 0x33, 0x76, 0x31,
	0xab, 0x4a, 0xac, 0x91, 0x58, 0xb2, 0x64, 0x8d, 0x10, 0x1b, 0xc4, 0x82, 0x25, 0x12, 0x42, 0x5d,
	0x76, 0xd9, 0x05, 0x8a, 0x68, 0xf8, 0x17, 0x10, 0x8b, 0xac, 0xd0, 0xcc, 0x9d, 0xb9, 0x1f, 0x4e,
	0xa2, 0x38, 0x5d, 0x64, 0x03, 0xbb, 0x3b, 0xe7, 0x9c, 0xdf, 0x99, 0x33, 0x67, 0x7e, 0x73, 0xce,
	0xb1, 0xc1, 0xaa, 0xe8, 0x10, 0x2e, 0xb0, 0xd7, 0x71, 0xfc, 0xb0, 0x2e, 0x3a, 0xa4, 0x8e, 0xa9,
	0x5f, 0xc7, 0x94, 0x76, 0x7d, 0x0f, 0x0b, 0x3f, 0x0c, 0xea, 0x83, 0x95, 0x7a, 0x9b, 0x04, 0x84,
	0x61, 0x41, 0x5a, 0x0e, 0x65, 0xa1, 0x08, 0x61, 0x2d, 0x85, 0x71, 0x44, 0x87, 0x38, 0x98, 0xfa,
	0x4e, 0x0a, 0xe3, 0x0c, 0x56, 0x16, 0x2f, 0xb5, 0x7d, 0xb1, 0xdb, 0xdf, 0x76, 0xbc, 0xb0, 0x57,
	0x6f, 0x87, 0xed, 0xb0, 0xae, 0xa0, 0xdb, 0xfd, 0x1d, 0xb5, 0x52, 0x0b, 0xf5, 0x15, 0xb9, 0x5c,
	0x7c, 0xad, 0x73, 0x85, 0xcb, 0x08, 0x30, 0xf5, 0x7b, 0xd8, 0xdb, 0xf5, 0x03, 0xc2, 0x86, 0x75,
	0xda, 0x69, 0x4b, 0x01, 0xaf, 0xf7, 0x88, 0xc0, 0x47, 0x04, 0xb2, 0x58, 0x3f, 0x0e, 0xc5, 0xfa,
	0x81, 0xf0, 0x7b, 0xe4, 0x10, 0xe0, 0x8d, 0x93, 0x00, 0xdc, 0xdb, 0x25, 0x3d, 0x3c, 0x8a, 0xab,
	0x7d, 0x91, 0x03, 0xf9, 0x06, 0xa5, 0xf0, 0x23, 0x50, 0x92, 0xb1, 0xb4, 0xb0, 0xc0, 0x65, 0x6b,
	0xc9, 0x5a, 0x9e, 0x5e, 0x7d, 0xc5, 0x89, 0x5c, 0x3a, 0x69, 0x97, 0x0e, 0xed, 0xb4, 0xa5, 0x80,
	0x3b, 0xd2, 0xda, 0x19, 0xac, 0x38, 0x77, 0xb6, 0x3f, 0x26, 0x9e, 0xd8, 0x24, 0x02, 0xbb, 0xf0,
	0xd1, 0x5e, 0x75, 0x62, 0x7f, 0xaf, 0x0a, 0x12, 0x19, 0x8a, 0xbd, 0xc2, 0x4d, 0x50, 0xe0, 0x94,
	0x78, 0xe5, 0x9c, 0xf2, 0x7e, 0xd1, 0x39, 0x39, 0xd5, 0x4e, 0x83, 0xd2, 0x7b, 0x94, 0x78, 0xee,
	0x8c, 0x76, 0x5c, 0x90, 0x2b, 0xa4, 0xdc, 0xc0, 0x2d, 0x30, 0xc9, 0x05, 0x16, 0x7d, 0x5e, 0xce,
	0x2b, 0x87, 0x97, 0xc6, 0x75, 0xa8, 0x40, 0xee, 0x9c, 0x76, 0x39, 0x19, 0xad, 0x91, 0x76, 0x56,
	0xfb, 0xd9, 0x02, 0xa0, 0x41, 0xe9, 0x4d, 0x9f, 0x8b, 0x90, 0x0d, 0xcf, 0x20, 0x2d, 0xcd, 0x4c,
	0x5a, 0x56, 0xc7, 0x3c, 0x85, 0x8e, 0xef, 0xb8, 0xec, 0xd4, 0xfe, 0xcc, 0x81, 0xb9, 0xac, 0x19,
	0xbc, 0x08, 0x0a, 0x62, 0x48, 0x89, 0x3a, 0x86, 0xed, 0xfe, 0xcf, 0x80, 0x9a, 0x43, 0x4a, 0x0e,
	0xf6, 0xaa, 0x53, 0x0d, 0x4a, 0xe5, 0x27, 0x52, 0x46, 0xf0, 0x65, 0x50, 0x12, 0x24, 0xc0, 0x81,
	0xb8, 0xb5, 0xae, 0x22, 0xb3, 0xdd, 0x05, 0x0d, 0x28, 0x35, 0xb5, 0x1c, 0xc5, 0x16, 0x70, 0x09,
	0x14, 0x02, 0xdc, 0x23, 0xea, 0x26, 0xec, 0x24, 0x9e, 0xdb, 0xb8, 0x47, 0x90, 0xd2, 0xc0, 0x6b,
	0x60, 0x56, 0x60, 0xd6, 0x26, 0x62, 0xad, 0xdb, 0xe7, 0x82, 0xb0, 0x72, 0x41, 0x99, 0xfe, 0x47,
	0x9b, 0xce, 0x36, 0xd3, 0x4a, 0x94, 0xb5, 0x85, 0x0d, 0x30, 0x1f, 0x09, 0xa4, 0x43, 0x4e, 0xb1,
	0x47, 0xca, 0xc5, 0xcc, 0x21, 0xe6, 0x9b, 0x59, 0x35, 0x1a, 0xb5, 0x87, 0x6d, 0x60, 0xef, 0xaa,
	0x5c, 0xf8, 0x84, 0x97, 0x27, 0x97, 0xf2, 0xe3, 0x32, 0x50, 0x27, 0xd0, 0x7d, 0x5e, 0xef, 0x64,
	0xdf, 0x34, 0x5e, 0x64, 0xce, 0xb4, 0x16, 0x25, 0xbe, 0x6b, 0x3f, 0x58, 0x40, 0xa6, 0x72, 0xc3,
	0xe7, 0x02, 0x7e, 0x78, 0x88, 0x3c, 0xce, 0x78, 0xe4, 0x91, 0x68, 0x45, 0x9d, 0x38, 0xe9, 0x46,
	0x92, 0x22, 0xce, 0x06, 0x28, 0xfa, 0x82, 0xf4, 0x78, 0x39, 0xa7, 0x8e, 0xf3, 0xe2, 0x98, 0xcc,
	0x71, 0x67, 0xb5, 0xcf, 0xe2, 0x2d, 0x89, 0x46, 0x91, 0x93, 0xda, 0x2f, 0x16, 0x98, 0x6e, 0x50,
	0x8a, 0x08, 0x0f, 0xfb, 0xcc, 0x23, 0x67, 0x40, 0xfc, 0xad, 0x0c, 0xf1, 0x5f, 0x1d, 0x33, 0x7c,
	0x13, 0xe0, 0xb1, 0xcc, 0xff, 0xbc, 0x00, 0xe6, 0x47, 0xec, 0xfe, 0xde, 0xd4, 0x7f, 0x68, 0x01,
	0x9b, 0xe9, 0x6c, 0x18, 0xee, 0xbb, 0xcf, 0x90, 0x6d, 0xc7, 0x2c, 0xf8, 0xdb, 0x81, 0x60, 0x43,
	0xb7, 0x62, 0x9e, 0x44, 0x2c, 0x3f, 0x48, 0x2f, 0x50, 0xb2, 0xe9, 0x22, 0x05, 0x73, 0x59, 0x30,
	0x5c, 0x00, 0xf9, 0x0e, 0x19, 0x46, 0x17, 0x82, 0xe4, 0x27, 0xbc, 0x09, 0x8a, 0x03, 0xdc, 0xed,
	0x93, 0xd3, 0x14, 0x42, 0xe3, 0xf4, 0xbe, 0x04, 0x72, 0x14, 0x39, 0xb8, 0x9a, 0xbb, 0x62, 0xd5,
	0xbe, 0x2d, 0xa8, 0x67, 0xf8, 0xcf, 0xed, 0xbf, 0x07, 0x8a, 0xde, 0x2e, 0x66, 0xa2, 0x3c, 0xa9,
	0xd2, 0x7a, 0x61, 0x9c, 0xb4, 0xae, 0x49, 0x80, 0xfb, 0x5f, 0x53, 0x27, 0xd4, 0xf2, 0xc0, 0x7c,
	0xa0, 0xc8, 0x1f, 0xdc, 0x06, 0x93, 0x2a, 0xdd, 0xbc, 0x3c, 0x75, 0xaa, 0xfe, 0x1b, 0xdd, 0x95,
	0xfb, 0x9c, 0xe9, 0xbf, 0xd1, 0x5a, 0x52, 0x27, 0x56, 0x22, 0xed, 0x19, 0xbe, 0x05, 0xc0, 0x8e,
	0x1f, 0xe0, 0xae, 0xff, 0x19, 0x61, 0xbc, 0x5c, 0x5a, 0xca, 0x2f, 0xdb, 0x6e, 0x55, 0x16, 0x94,
	0x77, 0x62, 0xe9, 0xc1, 0x5e, 0x75, 0x36, 0x5e, 0xa9, 0xbc, 0xa7, 0x20, 0xf0, 0x3c, 0x98, 0x6c,
	0xb1, 0x21, 0xea, 0x07, 0x65, 0x7b, 0xc9, 0x5a, 0x2e, 0x25, 0x5d, 0x7f, 0x5d, 0x49, 0x91, 0xd6,
	0xd6, 0xbe, 0x29, 0x02, 0x3b, 0x9e, 0x0d, 0x60, 0x1d, 0x14, 0xe9, 0x2e, 0xe6, 0x86, 0x31, 0xff,
	0x37, 0x89, 0xb8, 0x2b, 0x85, 0x07, 0x7b, 0xd5, 0x52, 0x83, 0x52, 0xf5, 0x8d, 0x22, 0x3b, 0xf8,
	0x2e, 0x80, 0xe1, 0x36, 0x27, 0x6c, 0x40, 0x5a, 0x37, 0xa2, 0xf9, 0xca, 0x0f, 0x03, 0x45, 0x9f,
	0xbc, 0xbb, 0xa8, 0xd1, 0xf0, 0xce, 0x21, 0x0b, 0x74, 0x04, 0x4a, 0x12, 0x86, 0x91, 0x2e, 0xc1,
	0x9c, 0xdc, 0x4b, 0xc6, 0x9b, 0x14, 0x61, 0x50, 0x5a, 0x89, 0xb2, 0xb6, 0x70, 0x00, 0xa0, 0x16,
	0x6c, 0x60, 0x2e, 0xb6, 0x68, 0x4b, 0x4e, 0x7a, 0x8a, 0x72, 0xd3, 0xab, 0x2f, 0x8d, 0x57, 0xbf,
	0x9b, 0x7e, 0x8f, 0x24, 0x41, 0xa3, 0x43, 0xde, 0xd0, 0x11, 0x3b, 0xc8, 0x57, 0xc3, 0xc8, 0xc0,
	0xe7, 0xf2, 0xd8, 0x45, 0x75, 0xec, 0xf8, 0xd5, 0x20, 0x2d, 0x47, 0xb1, 0x05, 0x5c, 0x07, 0x0b,
	0x2c, 0xec, 0x76, 0xb7, 0xb1, 0xd7, 0x31, 0x5a, 0x45, 0xcf, 0xbc, 0x5b, 0xd6, 0xa8, 0x05, 0x34,
	0xa2, 0x47, 0x87, 0x10, 0xf2, 0xac, 0x5d, 0xcc, 0x45, 0x93, 0xe1, 0x80, 0xfb, 0x32, 0x75, 0x32,
	0x72, 0x4d, 0xc6, 0x67, 0x3a, 0xeb, 0xc6, 0x21, 0x6f, 0xe8, 0x88, 0x1d, 0x24, 0xa7, 0x18, 0xc1,
	0x3c, 0x0c, 0xca, 0x25, 0x75, 0x33, 0x31, 0xa7, 0x90, 0x92, 0x22, 0xad, 0x85, 0x17, 0xc0, 0x54,
	0x8f, 0x70, 0x8e, 0xdb, 0x44, 0x91, 0xcf, 0x76, 0xe7, 0xb5, 0xe1, 0xd4, 0x66, 0x24, 0x46, 0x46,
	0x2f, 0xd3, 0xd7, 0xc3, 0x81, 0xbf, 0x43, 0xb8, 0x28, 0x83, 0x6c, 0xd1, 0xd9, 0xd4, 0x72, 0x14,
	0x5b, 0xd4, 0xbe, 0xb3, 0x40, 0xf2, 0x56, 0xe0, 0x06, 0x98, 0x65, 0xf8, 0x41, 0xb4, 0x68, 0x26,
	0x65, 0xee, 0x7c, 0xcc, 0x97, 0xb4, 0xf2, 0x60, 0x54, 0x80, 0xb2, 0x60, 0x58, 0x07, 0x76, 0x2c,
	0xd0, 0xf5, 0xef, 0x5f, 0x71, 0x9d, 0x37, 0x0a, 0x94, 0xd8, 0xc0, 0x5a, 0x5c, 0x06, 0xf2, 0xea,
	0x79, 0x82, 0xe4, 0x4d, 0x9b, 0x67, 0x5c, 0xfb, 0x31, 0x0f, 0xa2, 0xda, 0x91, 0xa9, 0xae, 0xd6,
	0x89, 0xd5, 0xf5, 0x4d, 0x30, 0xa7, 0x6a, 0xcd, 0x0d, 0x16, 0xf6, 0xa9, 0x7c, 0xdb, 0x3a, 0x22,
	0x53, 0x99, 0xe6, 0xd6, 0x32, 0x5a, 0x34, 0x62, 0x2d, 0x0f, 0xa3, 0x24, 0xb7, 0x93, 0x12, 0x1d,
	0x1f, 0x66, 0xcd, 0x28, 0x50, 0x62, 0x03, 0xaf, 0x80, 0x19, 0xb5, 0xb8, 0x4f, 0x98, 0x22, 0x65,
	0x54, 0xab, 0xcf, 0x69, 0xcc, 0xcc, 0x5a, 0x4a, 0x87, 0x32, 0x96, 0xf2, 0xb2, 0x19, 0xa1, 0xe1,
	0x16, 0xda, 0xd0, 0x15, 0x3a, 0xbe, 0x6c, 0x14, 0x89, 0x91, 0xd1, 0xcb, 0x4d, 0xd4, 0x27, 0x27,
	0x4c, 0xf5, 0x8e, 0xc9, 0xec, 0x26, 0x28, 0xa5, 0x43, 0x19, 0x4b, 0x83, 0xbc, 0x8b, 0x39, 0x7f,
	0x10, 0xb2, 0x96, 0xe2, 0xfa, 0x08, 0xd2, 0xe8, 0x50, 0xc6, 0x52, 0x22, 0xfd, 0x1e, 0x0d, 0x99,
	0x7c, 0xbf, 0x84, 0x86, 0x8a, 0xb9, 0xa5, 0x04, 0x79, 0x2b, 0xa5, 0x43, 0x19, 0xcb, 0xda, 0xf7,
	0x79, 0x60, 0xaf, 0x85, 0xc1, 0x8e, 0xdf, 0xde, 0xc4, 0x67, 0xf1, 0x2b, 0xf1, 0x7d, 0x50, 0x50,
	0xde, 0xa3, 0xa1, 0xf6, 0xf2, 0x58, 0xed, 0xca, 0x84, 0xe7, 0xac, 0x63, 0x81, 0xa3, 0xe1, 0x24,
	0x6e, 0xc5, 0x52, 0x84, 0x94, 0x4b, 0xf8, 0x09, 0x00, 0xdb, 0x7e, 0x80, 0xd9, 0x50, 0xca, 0x14,
	0x5d, 0xa7, 0x57, 0xaf, 0x9f, 0x6e, 0x03, 0x37, 0xc6, 0x47, 0xdb, 0xc4, 0x67, 0x49, 0x14, 0x28,
	0xb5, 0xc9, 0xe2, 0x65, 0x60, 0xc7, 0xc6, 0x47, 0xcc, 0x3c, 0xe7, 0xd2, 0x33, 0x8f, 0x9d, 0x9a,
	0x5f, 0x16, 0xaf, 0x83, 0xf9, 0x91, 0xbd, 0x4e, 0x82, 0xcf, 0xa4, 0xc7, 0x9f, 0x9f, 0x2c, 0x30,
	0x1b, 0x47, 0x7d, 0x06, 0xbf, 0x45, 0x50, 0xf6, 0xb7, 0xc8, 0xa5, 0x53, 0x65, 0xf5, 0x98, 0x5f,
	0x24, 0x7f, 0xe4, 0x80, 0xf9, 0x81, 0x95, 0xe9, 0x2f, 0xd6, 0x89, 0xfd, 0x65, 0x0b, 0x4c, 0xf5,
	0x75, 0xeb, 0xcb, 0x9d, 0xba, 0x1d, 0xc4, 0x0f, 0xd7, 0xf4, 0x3b, 0xe3, 0x4b, 0x16, 0x7e, 0x9e,
	0x6e, 0xc9, 0xc7, 0xfc, 0x85, 0x00, 0x5f, 0x30, 0x23, 0x57, 0x54, 0x3e, 0x66, 0x33, 0x73, 0x94,
	0x19, 0x9f, 0x56, 0x01, 0xc0, 0x94, 0x9a, 0x42, 0x13, 0xd5, 0x8c, 0x98, 0x4d, 0x8d, 0x58, 0x83,
	0x52, 0x56, 0xf0, 0x75, 0x30, 0xdd, 0x22, 0xdc, 0x63, 0x3e, 0x15, 0xa6, 0x65, 0xda, 0xee, 0xbf,
	0x35, 0x68, 0x7a, 0x3d, 0x51, 0xa1, 0xb4, 0x5d, 0xa6, 0xbb, 0x4c, 0x9d, 0xd8, 0x5d, 0xae, 0x25,
	0xb3, 0xba, 0x2e, 0xf1, 0x55, 0x73, 0xb9, 0x96, 0xaa, 0xf0, 0xf6, 0xe8, 0x4d, 0x5d, 0x2d, 0x7d,
	0xf5, 0x75, 0x75, 0xe2, 0xe1, 0xaf, 0x4b, 0x13, 0xb5, 0x10, 0x9c, 0x33, 0x9d, 0xfb, 0x2e, 0x0b,
	0x3f, 0x1d, 0xde, 0x51, 0x11, 0xf0, 0x53, 0xde, 0xdf, 0x05, 0x30, 0xe5, 0xe9, 0x69, 0x39, 0x97,
	0x2d, 0xa6, 0x66, 0x4e, 0x36, 0x7a, 0x77, 0xf9, 0xd1, 0xd3, 0xca, 0xc4, 0xe3, 0xa7, 0x95, 0x89,
	0x27, 0x4f, 0x2b, 0x13, 0x0f, 0xf7, 0x2b, 0xd6, 0xa3, 0xfd, 0x8a, 0xf5, 0x78, 0xbf, 0x62, 0x3d,
	0xd9, 0xaf, 0x58, 0xbf, 0xed, 0x57, 0xac, 0x2f, 0x7f, 0xaf, 0x4c, 0x7c, 0x90, 0x1b, 0xac, 0xfc,
	0x15, 0x00, 0x00, 0xff, 0xff, 0x8b, 0x0e, 0x13, 0x1b, 0x0f, 0x14, 0x00, 0x00,
}

func (m *App) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *App) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *App) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Status.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x1a
	{
		size, err := m.Spec.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AppHistory) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppHistory) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppHistory) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Spec.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AppHistorySpec) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppHistorySpec) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppHistorySpec) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Histories) > 0 {
		for iNdEx := len(m.Histories) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Histories[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	i -= len(m.TargetNamespace)
	copy(dAtA[i:], m.TargetNamespace)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TargetNamespace)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.TargetCluster)
	copy(dAtA[i:], m.TargetCluster)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TargetCluster)))
	i--
	dAtA[i] = 0x22
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.TenantID)
	copy(dAtA[i:], m.TenantID)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TenantID)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Type)
	copy(dAtA[i:], m.Type)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Type)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AppList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ListMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AppResource) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppResource) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppResource) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Spec.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AppResourceSpec) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppResourceSpec) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppResourceSpec) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Resources) > 0 {
		keysForResources := make([]string, 0, len(m.Resources))
		for k := range m.Resources {
			keysForResources = append(keysForResources, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForResources)
		for iNdEx := len(keysForResources) - 1; iNdEx >= 0; iNdEx-- {
			v := m.Resources[string(keysForResources[iNdEx])]
			baseI := i
			{
				size, err := (&v).MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
			i -= len(keysForResources[iNdEx])
			copy(dAtA[i:], keysForResources[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForResources[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x32
		}
	}
	i -= len(m.TargetNamespace)
	copy(dAtA[i:], m.TargetNamespace)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TargetNamespace)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.TargetCluster)
	copy(dAtA[i:], m.TargetCluster)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TargetCluster)))
	i--
	dAtA[i] = 0x22
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.TenantID)
	copy(dAtA[i:], m.TenantID)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TenantID)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Type)
	copy(dAtA[i:], m.Type)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Type)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AppSpec) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppSpec) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppSpec) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i--
	if m.DryRun {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x48
	if len(m.Finalizers) > 0 {
		for iNdEx := len(m.Finalizers) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Finalizers[iNdEx])
			copy(dAtA[i:], m.Finalizers[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Finalizers[iNdEx])))
			i--
			dAtA[i] = 0x42
		}
	}
	{
		size, err := m.Values.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x3a
	{
		size, err := m.Chart.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x32
	i -= len(m.TargetNamespace)
	copy(dAtA[i:], m.TargetNamespace)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TargetNamespace)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.TargetCluster)
	copy(dAtA[i:], m.TargetCluster)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TargetCluster)))
	i--
	dAtA[i] = 0x22
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.TenantID)
	copy(dAtA[i:], m.TenantID)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TenantID)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Type)
	copy(dAtA[i:], m.Type)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Type)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AppStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Manifest)
	copy(dAtA[i:], m.Manifest)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Manifest)))
	i--
	dAtA[i] = 0x52
	i -= len(m.Message)
	copy(dAtA[i:], m.Message)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Message)))
	i--
	dAtA[i] = 0x4a
	i -= len(m.Reason)
	copy(dAtA[i:], m.Reason)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Reason)))
	i--
	dAtA[i] = 0x42
	{
		size, err := m.LastTransitionTime.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x3a
	i = encodeVarintGenerated(dAtA, i, uint64(m.RollbackRevision))
	i--
	dAtA[i] = 0x30
	i = encodeVarintGenerated(dAtA, i, uint64(m.Revision))
	i--
	dAtA[i] = 0x28
	{
		size, err := m.ReleaseLastUpdated.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x22
	i -= len(m.ReleaseStatus)
	copy(dAtA[i:], m.ReleaseStatus)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.ReleaseStatus)))
	i--
	dAtA[i] = 0x1a
	i = encodeVarintGenerated(dAtA, i, uint64(m.ObservedGeneration))
	i--
	dAtA[i] = 0x10
	i -= len(m.Phase)
	copy(dAtA[i:], m.Phase)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Phase)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *AppValues) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppValues) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppValues) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Values[iNdEx])
			copy(dAtA[i:], m.Values[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Values[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	i -= len(m.RawValues)
	copy(dAtA[i:], m.RawValues)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.RawValues)))
	i--
	dAtA[i] = 0x12
	i -= len(m.RawValuesType)
	copy(dAtA[i:], m.RawValuesType)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.RawValuesType)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Chart) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Chart) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Chart) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i--
	if m.ImportedRepo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x40
	i -= len(m.RepoPassword)
	copy(dAtA[i:], m.RepoPassword)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.RepoPassword)))
	i--
	dAtA[i] = 0x3a
	i -= len(m.RepoUsername)
	copy(dAtA[i:], m.RepoUsername)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.RepoUsername)))
	i--
	dAtA[i] = 0x32
	i -= len(m.RepoURL)
	copy(dAtA[i:], m.RepoURL)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.RepoURL)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.ChartVersion)
	copy(dAtA[i:], m.ChartVersion)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.ChartVersion)))
	i--
	dAtA[i] = 0x22
	i -= len(m.ChartName)
	copy(dAtA[i:], m.ChartName)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.ChartName)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.ChartGroupName)
	copy(dAtA[i:], m.ChartGroupName)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.ChartGroupName)))
	i--
	dAtA[i] = 0x12
	i -= len(m.TenantID)
	copy(dAtA[i:], m.TenantID)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.TenantID)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ConfigMap) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfigMap) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConfigMap) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BinaryData) > 0 {
		keysForBinaryData := make([]string, 0, len(m.BinaryData))
		for k := range m.BinaryData {
			keysForBinaryData = append(keysForBinaryData, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForBinaryData)
		for iNdEx := len(keysForBinaryData) - 1; iNdEx >= 0; iNdEx-- {
			v := m.BinaryData[string(keysForBinaryData[iNdEx])]
			baseI := i
			if v != nil {
				i -= len(v)
				copy(dAtA[i:], v)
				i = encodeVarintGenerated(dAtA, i, uint64(len(v)))
				i--
				dAtA[i] = 0x12
			}
			i -= len(keysForBinaryData[iNdEx])
			copy(dAtA[i:], keysForBinaryData[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForBinaryData[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Data) > 0 {
		keysForData := make([]string, 0, len(m.Data))
		for k := range m.Data {
			keysForData = append(keysForData, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForData)
		for iNdEx := len(keysForData) - 1; iNdEx >= 0; iNdEx-- {
			v := m.Data[string(keysForData[iNdEx])]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintGenerated(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(keysForData[iNdEx])
			copy(dAtA[i:], keysForData[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForData[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ConfigMapList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfigMapList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConfigMapList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ListMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *History) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *History) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *History) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Manifest)
	copy(dAtA[i:], m.Manifest)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Manifest)))
	i--
	dAtA[i] = 0x3a
	i -= len(m.Description)
	copy(dAtA[i:], m.Description)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Description)))
	i--
	dAtA[i] = 0x32
	i -= len(m.AppVersion)
	copy(dAtA[i:], m.AppVersion)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.AppVersion)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.Chart)
	copy(dAtA[i:], m.Chart)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Chart)))
	i--
	dAtA[i] = 0x22
	i -= len(m.Status)
	copy(dAtA[i:], m.Status)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Status)))
	i--
	dAtA[i] = 0x1a
	{
		size, err := m.Updated.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	i = encodeVarintGenerated(dAtA, i, uint64(m.Revision))
	i--
	dAtA[i] = 0x8
	return len(dAtA) - i, nil
}

func (m ResourceValues) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m ResourceValues) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m ResourceValues) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m) > 0 {
		for iNdEx := len(m) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m[iNdEx])
			copy(dAtA[i:], m[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *RollbackProxyOptions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RollbackProxyOptions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RollbackProxyOptions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Cluster)
	copy(dAtA[i:], m.Cluster)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Cluster)))
	i--
	dAtA[i] = 0x12
	i = encodeVarintGenerated(dAtA, i, uint64(m.Revision))
	i--
	dAtA[i] = 0x8
	return len(dAtA) - i, nil
}

func encodeVarintGenerated(dAtA []byte, offset int, v uint64) int {
	offset -= sovGenerated(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *App) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Spec.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Status.Size()
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *AppHistory) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Spec.Size()
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *AppHistorySpec) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Type)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TenantID)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TargetCluster)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TargetNamespace)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Histories) > 0 {
		for _, e := range m.Histories {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *AppList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ListMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *AppResource) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Spec.Size()
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *AppResourceSpec) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Type)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TenantID)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TargetCluster)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TargetNamespace)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Resources) > 0 {
		for k, v := range m.Resources {
			_ = k
			_ = v
			l = v.Size()
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + 1 + l + sovGenerated(uint64(l))
			n += mapEntrySize + 1 + sovGenerated(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *AppSpec) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Type)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TenantID)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TargetCluster)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.TargetNamespace)
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Chart.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Values.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Finalizers) > 0 {
		for _, s := range m.Finalizers {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *AppStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Phase)
	n += 1 + l + sovGenerated(uint64(l))
	n += 1 + sovGenerated(uint64(m.ObservedGeneration))
	l = len(m.ReleaseStatus)
	n += 1 + l + sovGenerated(uint64(l))
	l = m.ReleaseLastUpdated.Size()
	n += 1 + l + sovGenerated(uint64(l))
	n += 1 + sovGenerated(uint64(m.Revision))
	n += 1 + sovGenerated(uint64(m.RollbackRevision))
	l = m.LastTransitionTime.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Reason)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Message)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Manifest)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *AppValues) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RawValuesType)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.RawValues)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Values) > 0 {
		for _, s := range m.Values {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *Chart) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TenantID)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.ChartGroupName)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.ChartName)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.ChartVersion)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.RepoURL)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.RepoUsername)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.RepoPassword)
	n += 1 + l + sovGenerated(uint64(l))
	n += 2
	return n
}

func (m *ConfigMap) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Data) > 0 {
		for k, v := range m.Data {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + 1 + len(v) + sovGenerated(uint64(len(v)))
			n += mapEntrySize + 1 + sovGenerated(uint64(mapEntrySize))
		}
	}
	if len(m.BinaryData) > 0 {
		for k, v := range m.BinaryData {
			_ = k
			_ = v
			l = 0
			if v != nil {
				l = 1 + len(v) + sovGenerated(uint64(len(v)))
			}
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + l
			n += mapEntrySize + 1 + sovGenerated(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *ConfigMapList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ListMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *History) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovGenerated(uint64(m.Revision))
	l = m.Updated.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Status)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Chart)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.AppVersion)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Manifest)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m ResourceValues) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m) > 0 {
		for _, s := range m {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *RollbackProxyOptions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovGenerated(uint64(m.Revision))
	l = len(m.Cluster)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func sovGenerated(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozGenerated(x uint64) (n int) {
	return sovGenerated(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *App) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&App{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Spec:` + strings.Replace(strings.Replace(this.Spec.String(), "AppSpec", "AppSpec", 1), `&`, ``, 1) + `,`,
		`Status:` + strings.Replace(strings.Replace(this.Status.String(), "AppStatus", "AppStatus", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *AppHistory) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AppHistory{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Spec:` + strings.Replace(strings.Replace(this.Spec.String(), "AppHistorySpec", "AppHistorySpec", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *AppHistorySpec) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForHistories := "[]History{"
	for _, f := range this.Histories {
		repeatedStringForHistories += strings.Replace(strings.Replace(f.String(), "History", "History", 1), `&`, ``, 1) + ","
	}
	repeatedStringForHistories += "}"
	s := strings.Join([]string{`&AppHistorySpec{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`TargetCluster:` + fmt.Sprintf("%v", this.TargetCluster) + `,`,
		`TargetNamespace:` + fmt.Sprintf("%v", this.TargetNamespace) + `,`,
		`Histories:` + repeatedStringForHistories + `,`,
		`}`,
	}, "")
	return s
}
func (this *AppList) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForItems := "[]App{"
	for _, f := range this.Items {
		repeatedStringForItems += strings.Replace(strings.Replace(f.String(), "App", "App", 1), `&`, ``, 1) + ","
	}
	repeatedStringForItems += "}"
	s := strings.Join([]string{`&AppList{`,
		`ListMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ListMeta), "ListMeta", "v1.ListMeta", 1), `&`, ``, 1) + `,`,
		`Items:` + repeatedStringForItems + `,`,
		`}`,
	}, "")
	return s
}
func (this *AppResource) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AppResource{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Spec:` + strings.Replace(strings.Replace(this.Spec.String(), "AppResourceSpec", "AppResourceSpec", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *AppResourceSpec) String() string {
	if this == nil {
		return "nil"
	}
	keysForResources := make([]string, 0, len(this.Resources))
	for k := range this.Resources {
		keysForResources = append(keysForResources, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForResources)
	mapStringForResources := "Resources{"
	for _, k := range keysForResources {
		mapStringForResources += fmt.Sprintf("%v: %v,", k, this.Resources[k])
	}
	mapStringForResources += "}"
	s := strings.Join([]string{`&AppResourceSpec{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`TargetCluster:` + fmt.Sprintf("%v", this.TargetCluster) + `,`,
		`TargetNamespace:` + fmt.Sprintf("%v", this.TargetNamespace) + `,`,
		`Resources:` + mapStringForResources + `,`,
		`}`,
	}, "")
	return s
}
func (this *AppSpec) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AppSpec{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`TargetCluster:` + fmt.Sprintf("%v", this.TargetCluster) + `,`,
		`TargetNamespace:` + fmt.Sprintf("%v", this.TargetNamespace) + `,`,
		`Chart:` + strings.Replace(strings.Replace(this.Chart.String(), "Chart", "Chart", 1), `&`, ``, 1) + `,`,
		`Values:` + strings.Replace(strings.Replace(this.Values.String(), "AppValues", "AppValues", 1), `&`, ``, 1) + `,`,
		`Finalizers:` + fmt.Sprintf("%v", this.Finalizers) + `,`,
		`DryRun:` + fmt.Sprintf("%v", this.DryRun) + `,`,
		`}`,
	}, "")
	return s
}
func (this *AppStatus) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AppStatus{`,
		`Stage:` + fmt.Sprintf("%v", this.Phase) + `,`,
		`ObservedGeneration:` + fmt.Sprintf("%v", this.ObservedGeneration) + `,`,
		`ReleaseStatus:` + fmt.Sprintf("%v", this.ReleaseStatus) + `,`,
		`ReleaseLastUpdated:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ReleaseLastUpdated), "Time", "v1.Time", 1), `&`, ``, 1) + `,`,
		`Revision:` + fmt.Sprintf("%v", this.Revision) + `,`,
		`RollbackRevision:` + fmt.Sprintf("%v", this.RollbackRevision) + `,`,
		`LastTransitionTime:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.LastTransitionTime), "Time", "v1.Time", 1), `&`, ``, 1) + `,`,
		`Reason:` + fmt.Sprintf("%v", this.Reason) + `,`,
		`Message:` + fmt.Sprintf("%v", this.Message) + `,`,
		`Manifest:` + fmt.Sprintf("%v", this.Manifest) + `,`,
		`}`,
	}, "")
	return s
}
func (this *AppValues) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&AppValues{`,
		`RawValuesType:` + fmt.Sprintf("%v", this.RawValuesType) + `,`,
		`RawValues:` + fmt.Sprintf("%v", this.RawValues) + `,`,
		`Values:` + fmt.Sprintf("%v", this.Values) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Chart) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Chart{`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`ChartGroupName:` + fmt.Sprintf("%v", this.ChartGroupName) + `,`,
		`ChartName:` + fmt.Sprintf("%v", this.ChartName) + `,`,
		`ChartVersion:` + fmt.Sprintf("%v", this.ChartVersion) + `,`,
		`RepoURL:` + fmt.Sprintf("%v", this.RepoURL) + `,`,
		`RepoUsername:` + fmt.Sprintf("%v", this.RepoUsername) + `,`,
		`RepoPassword:` + fmt.Sprintf("%v", this.RepoPassword) + `,`,
		`ImportedRepo:` + fmt.Sprintf("%v", this.ImportedRepo) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ConfigMap) String() string {
	if this == nil {
		return "nil"
	}
	keysForData := make([]string, 0, len(this.Data))
	for k := range this.Data {
		keysForData = append(keysForData, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForData)
	mapStringForData := "map[string]string{"
	for _, k := range keysForData {
		mapStringForData += fmt.Sprintf("%v: %v,", k, this.Data[k])
	}
	mapStringForData += "}"
	keysForBinaryData := make([]string, 0, len(this.BinaryData))
	for k := range this.BinaryData {
		keysForBinaryData = append(keysForBinaryData, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForBinaryData)
	mapStringForBinaryData := "map[string][]byte{"
	for _, k := range keysForBinaryData {
		mapStringForBinaryData += fmt.Sprintf("%v: %v,", k, this.BinaryData[k])
	}
	mapStringForBinaryData += "}"
	s := strings.Join([]string{`&ConfigMap{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Data:` + mapStringForData + `,`,
		`BinaryData:` + mapStringForBinaryData + `,`,
		`}`,
	}, "")
	return s
}
func (this *ConfigMapList) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForItems := "[]ConfigMap{"
	for _, f := range this.Items {
		repeatedStringForItems += strings.Replace(strings.Replace(f.String(), "ConfigMap", "ConfigMap", 1), `&`, ``, 1) + ","
	}
	repeatedStringForItems += "}"
	s := strings.Join([]string{`&ConfigMapList{`,
		`ListMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ListMeta), "ListMeta", "v1.ListMeta", 1), `&`, ``, 1) + `,`,
		`Items:` + repeatedStringForItems + `,`,
		`}`,
	}, "")
	return s
}
func (this *History) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&History{`,
		`Revision:` + fmt.Sprintf("%v", this.Revision) + `,`,
		`Updated:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Updated), "Time", "v1.Time", 1), `&`, ``, 1) + `,`,
		`Status:` + fmt.Sprintf("%v", this.Status) + `,`,
		`Chart:` + fmt.Sprintf("%v", this.Chart) + `,`,
		`AppVersion:` + fmt.Sprintf("%v", this.AppVersion) + `,`,
		`Description:` + fmt.Sprintf("%v", this.Description) + `,`,
		`Manifest:` + fmt.Sprintf("%v", this.Manifest) + `,`,
		`}`,
	}, "")
	return s
}
func (this *RollbackProxyOptions) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&RollbackProxyOptions{`,
		`Revision:` + fmt.Sprintf("%v", this.Revision) + `,`,
		`Cluster:` + fmt.Sprintf("%v", this.Cluster) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringGenerated(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *App) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: App: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: App: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spec", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Spec.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Status.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppHistory) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppHistory: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppHistory: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spec", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Spec.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppHistorySpec) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppHistorySpec: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppHistorySpec: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = AppType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetCluster", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetCluster = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetNamespace", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetNamespace = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Histories", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Histories = append(m.Histories, History{})
			if err := m.Histories[len(m.Histories)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ListMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, App{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppResource) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppResource: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppResource: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spec", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Spec.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppResourceSpec) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppResourceSpec: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppResourceSpec: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = AppType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetCluster", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetCluster = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetNamespace", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetNamespace = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Resources", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Resources == nil {
				m.Resources = make(Resources)
			}
			var mapkey string
			mapvalue := &ResourceValues{}
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthGenerated
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthGenerated
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &ResourceValues{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Resources[mapkey] = *mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppSpec) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppSpec: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppSpec: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = AppType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetCluster", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetCluster = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetNamespace", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetNamespace = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chart", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Chart.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Values.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Finalizers", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Finalizers = append(m.Finalizers, FinalizerName(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DryRun", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DryRun = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Phase = AppPhase(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObservedGeneration", wireType)
			}
			m.ObservedGeneration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ObservedGeneration |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReleaseStatus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReleaseStatus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReleaseLastUpdated", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ReleaseLastUpdated.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Revision", wireType)
			}
			m.Revision = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Revision |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RollbackRevision", wireType)
			}
			m.RollbackRevision = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RollbackRevision |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastTransitionTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.LastTransitionTime.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Manifest", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Manifest = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppValues) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppValues: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppValues: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawValuesType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RawValuesType = RawValuesType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawValues", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RawValues = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Chart) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Chart: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Chart: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChartGroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChartGroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChartName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChartName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChartVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChartVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepoURL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RepoURL = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepoUsername", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RepoUsername = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepoPassword", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RepoPassword = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImportedRepo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ImportedRepo = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfigMap) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConfigMap: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConfigMap: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Data[mapkey] = mapvalue
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BinaryData", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BinaryData == nil {
				m.BinaryData = make(map[string][]byte)
			}
			var mapkey string
			mapvalue := []byte{}
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapbyteLen uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapbyteLen |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intMapbyteLen := int(mapbyteLen)
					if intMapbyteLen < 0 {
						return ErrInvalidLengthGenerated
					}
					postbytesIndex := iNdEx + intMapbyteLen
					if postbytesIndex < 0 {
						return ErrInvalidLengthGenerated
					}
					if postbytesIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = make([]byte, mapbyteLen)
					copy(mapvalue, dAtA[iNdEx:postbytesIndex])
					iNdEx = postbytesIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.BinaryData[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfigMapList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConfigMapList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConfigMapList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ListMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, ConfigMap{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *History) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: History: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: History: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Revision", wireType)
			}
			m.Revision = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Revision |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Updated", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Updated.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chart", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chart = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Manifest", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Manifest = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResourceValues) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResourceValues: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResourceValues: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			*m = append(*m, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RollbackProxyOptions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RollbackProxyOptions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RollbackProxyOptions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Revision", wireType)
			}
			m.Revision = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Revision |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cluster", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cluster = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGenerated(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthGenerated
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupGenerated
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthGenerated
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthGenerated        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGenerated          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupGenerated = fmt.Errorf("proto: unexpected end of group")
)
