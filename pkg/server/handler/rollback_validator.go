package handler

import (
	"fmt"

	"git.woa.com/kmetis/starship-engine/pkg/consts"

	"git.woa.com/kmetis/starship-engine/pkg/db"
)

// validateRollbackRestrictions 验证回滚限制条件
func validateRollbackRestrictions(clusterInfo *db.TaskClusterInfo) error {
	// 1.验证必须是发布任务
	if clusterInfo.AppAction != consts.PUBLISH {
		return fmt.Errorf("回滚操作仅支持发布任务，当前任务类型: %s", clusterInfo.AppAction)
	}

	// 2. 验证组件必须是 eklet
	if clusterInfo.Component != consts.EKLET {
		return fmt.Errorf("回滚操作仅支持eklet组件，当前组件: %s", clusterInfo.Component)
	}

	// 3. 验证 addonVersion 必须为空
	if clusterInfo.AppVersion != "" {
		return fmt.Errorf("回滚操作仅暂不支持addon")
	}

	return nil
}

// validateBatchRollbackClusters 验证批量回滚的集群列表
func validateBatchRollbackClusters(taskName string, clusterIds []string) ([]*db.TaskClusterInfo, error) {
	var validClusters []*db.TaskClusterInfo
	var errors []string

	for _, clusterId := range clusterIds {
		// 获取集群信息
		clusterInfo, err := db.GetClusterInfoByNameAndID(taskName, clusterId, "")
		if err != nil {
			errors = append(errors, fmt.Sprintf("集群 %s: 获取集群信息失败 - %v", clusterId, err))
			continue
		}

		if clusterInfo == nil {
			errors = append(errors, fmt.Sprintf("集群 %s: 未找到集群", clusterId))
			continue
		}

		// 验证回滚限制条件
		if err := validateRollbackRestrictions(clusterInfo); err != nil {
			errors = append(errors, fmt.Sprintf("集群 %s: %v", clusterId, err))
			continue
		}

		validClusters = append(validClusters, clusterInfo)
	}

	if len(errors) > 0 {
		return validClusters, fmt.Errorf("部分集群验证失败: %v", errors)
	}

	return validClusters, nil
}
