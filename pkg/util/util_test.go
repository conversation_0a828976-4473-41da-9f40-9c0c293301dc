package util

import (
	"fmt"
	"reflect"
	"testing"
)

func TestStringToSliceMap(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		want    []map[string]string
		wantErr bool
	}{
		{
			name:  "normal case",
			input: `[{"key1":"value1"},{"key2":"value2"}]`,
			want: []map[string]string{
				{"key1": "value1"},
				{"key2": "value2"},
			},
			wantErr: false,
		},
		{
			name:    "empty string",
			input:   "",
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := StringToSliceMap(tt.input)

			// 检查错误情况
			if (err != nil) != tt.wantErr {
				t.Errorf("StringToSliceMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 检查返回值
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StringToSliceMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenerateRandomString(t *testing.T) {
	for i := 0; i < 10; i++ {
		generateRandomString := GenerateRandomString(6)
		fmt.Println(generateRandomString)
	}
}

func TestCheckTimeIsValid(t *testing.T) {
	tests := []struct {
		name      string
		startTime string
		endTime   string
		want      bool
	}{
		{
			name:      "Valid time window - current time within range",
			startTime: "2025-01-07 18:00:00",
			endTime:   "2025-03-31 18:00:00",
			want:      true,
		},
		{
			name:      "Valid time window - current time within range",
			startTime: "2025-01-07 18:00:00",
			endTime:   "2025-01-31 18:00:00",
			want:      false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := CheckTimeIsValid(tt.startTime, tt.endTime)
			if got != tt.want {
				t.Errorf("CheckTimeIsValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCompareVersion(t *testing.T) {
	tests := []struct {
		name    string
		v1      string
		v2      string
		want    int
		wantErr bool
	}{
		{
			name:    "equal versions",
			v1:      "1.2.3",
			v2:      "1.2.3",
			want:    0,
			wantErr: false,
		},
		{
			name:    "v1 greater than v2 - patch version",
			v1:      "1.2.4",
			v2:      "1.2.3",
			want:    1,
			wantErr: false,
		},
		{
			name:    "v1 greater than v2 - minor version",
			v1:      "1.3.0",
			v2:      "1.2.9",
			want:    1,
			wantErr: false,
		},
		{
			name:    "v1 greater than v2 - major version",
			v1:      "2.0.0",
			v2:      "1.9.9",
			want:    1,
			wantErr: false,
		},
		{
			name:    "v1 less than v2 - patch version",
			v1:      "1.2.3",
			v2:      "1.2.4",
			want:    -1,
			wantErr: false,
		},
		{
			name:    "v1 less than v2 - minor version",
			v1:      "1.2.9",
			v2:      "1.3.0",
			want:    -1,
			wantErr: false,
		},
		{
			name:    "v1 less than v2 - major version",
			v1:      "1.9.9",
			v2:      "2.0.0",
			want:    -1,
			wantErr: false,
		},
		{
			name:    "with pre-release versions",
			v1:      "1.2.3-alpha",
			v2:      "1.2.3",
			want:    -1,
			wantErr: false,
		},
		{
			name:    "with pre-release versions reverse",
			v1:      "1.2.3",
			v2:      "1.2.3-alpha",
			want:    1,
			wantErr: false,
		},
		{
			name:    "invalid version format - v1",
			v1:      "invalid",
			v2:      "1.2.3",
			want:    0,
			wantErr: true,
		},
		{
			name:    "invalid version format - v2",
			v1:      "1.2.3",
			v2:      "invalid",
			want:    0,
			wantErr: true,
		},
		{
			name:    "empty versions",
			v1:      "",
			v2:      "",
			want:    0,
			wantErr: true,
		},
		{
			name:    "complex pre-release versions",
			v1:      "1.2.3-alpha.1",
			v2:      "1.2.3-alpha.2",
			want:    -1,
			wantErr: false,
		},
		{
			name:    "build metadata",
			v1:      "1.2.3+build123",
			v2:      "1.2.3+build456",
			want:    0,
			wantErr: false,
		},
		{
			name:    "v1 less than v2 - major version",
			v1:      "v1.9.9",
			v2:      "v2.0.0",
			want:    -1,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CompareVersion(tt.v1, tt.v2)

			// 检查错误情况
			if (err != nil) != tt.wantErr {
				t.Errorf("CompareVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 检查返回值
			if got != tt.want {
				t.Errorf("CompareVersion(%s, %s) = %d, want %d", tt.v1, tt.v2, got, tt.want)
			}
		})
	}
}
