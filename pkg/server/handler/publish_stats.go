package handler

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/api/v1/common"
	"git.woa.com/kmetis/starship-engine/pkg/db"
)

// TaskCreationStatsRequest 任务创建统计请求
type TaskCreationStatsRequest struct {
	StartDate string `form:"start_date"` // 开始日期 YYYY-MM-DD
	EndDate   string `form:"end_date"`   // 结束日期 YYYY-MM-DD
	Component string `form:"component"`  // 组件名称，为空表示所有组件
	Days      int    `form:"days"`       // 快速时间范围：7、14、30天
}

// ClusterDeploymentStatsRequest 集群发布统计请求
type ClusterDeploymentStatsRequest struct {
	StartDate string `form:"start_date"` // 开始日期 YYYY-MM-DD
	EndDate   string `form:"end_date"`   // 结束日期 YYYY-MM-DD
	Component string `form:"component"`  // 组件名称，为空表示所有组件
	Days      int    `form:"days"`       // 快速时间范围：7、14、30天
}

// getPublishStatsOperator 获取发布统计操作实例
func getPublishStatsOperator() *db.PublishStatsOperator {
	return db.NewPublishStatsOperator()
}

// validatePublishStatsOperator 验证发布统计操作实例
func validatePublishStatsOperator(c *gin.Context) *db.PublishStatsOperator {
	statsOp := getPublishStatsOperator()
	if statsOp == nil {
		klog.Errorf("发布统计操作实例创建失败，数据库连接未初始化")
		handleError(c, http.StatusInternalServerError, fmt.Errorf("数据库连接未初始化"))
		return nil
	}
	return statsOp
}

// parseDateRange 解析日期范围参数
func parseDateRange(req interface{}) (string, string, error) {
	var startDate, endDate string
	var days int

	switch r := req.(type) {
	case *TaskCreationStatsRequest:
		startDate, endDate, days = r.StartDate, r.EndDate, r.Days
	case *ClusterDeploymentStatsRequest:
		startDate, endDate, days = r.StartDate, r.EndDate, r.Days
	default:
		return "", "", fmt.Errorf("不支持的请求类型")
	}

	// 如果指定了快速时间范围，优先使用
	if days > 0 {
		startDate, endDate = db.GetQuickDateRange(days)
	}

	// 如果没有指定日期范围，默认使用最近14天
	if startDate == "" || endDate == "" {
		startDate, endDate = db.GetQuickDateRange(14)
	}

	// 验证日期格式
	if _, err := time.Parse("2006-01-02", startDate); err != nil {
		return "", "", fmt.Errorf("开始日期格式错误，应为YYYY-MM-DD")
	}
	if _, err := time.Parse("2006-01-02", endDate); err != nil {
		return "", "", fmt.Errorf("结束日期格式错误，应为YYYY-MM-DD")
	}

	return startDate, endDate, nil
}

// TaskCreationStats 任务创建统计API
func TaskCreationStats(c *gin.Context) {
	var req TaskCreationStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		klog.Errorf("绑定任务创建统计请求参数失败: %v", err)
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 解析日期范围
	startDate, endDate, err := parseDateRange(&req)
	if err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	statsOp := validatePublishStatsOperator(c)
	if statsOp == nil {
		return
	}

	// 验证日期范围
	if err := statsOp.ValidateDateRange(startDate, endDate); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 获取任务创建统计数据
	response, err := statsOp.GetTaskCreationStats(startDate, endDate, req.Component)
	if err != nil {
		klog.Errorf("获取任务创建统计数据失败: %v", err)
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, common.NewSuccess(response))
}

// ClusterDeploymentStats 集群发布统计API
func ClusterDeploymentStats(c *gin.Context) {
	var req ClusterDeploymentStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		klog.Errorf("绑定集群发布统计请求参数失败: %v", err)
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 解析日期范围
	startDate, endDate, err := parseDateRange(&req)
	if err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	statsOp := validatePublishStatsOperator(c)
	if statsOp == nil {
		return
	}

	// 验证日期范围
	if err := statsOp.ValidateDateRange(startDate, endDate); err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	// 获取集群发布统计数据
	response, err := statsOp.GetClusterDeploymentStats(startDate, endDate, req.Component)
	if err != nil {
		klog.Errorf("获取集群发布统计数据失败: %v", err)
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, common.NewSuccess(response))
}

// GetComponents 获取组件列表API
func GetComponents(c *gin.Context) {
	statsOp := validatePublishStatsOperator(c)
	if statsOp == nil {
		return
	}

	// 获取组件列表
	components, err := statsOp.GetAllComponents()
	if err != nil {
		klog.Errorf("获取组件列表失败: %v", err)
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, common.NewSuccess(components))
}

// GetDateRange 获取数据时间范围API
func GetDateRange(c *gin.Context) {
	statsOp := validatePublishStatsOperator(c)
	if statsOp == nil {
		return
	}

	// 获取时间范围
	minDate, maxDate, err := statsOp.GetDateRange()
	if err != nil {
		klog.Errorf("获取时间范围失败: %v", err)
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	result := map[string]string{
		"min_date": minDate,
		"max_date": maxDate,
	}

	c.JSON(http.StatusOK, common.NewSuccess(result))
}

// GetQuickStats 获取快速统计数据API
func GetQuickStats(c *gin.Context) {
	statsOp := validatePublishStatsOperator(c)
	if statsOp == nil {
		return
	}

	// 获取最近7天的数据
	startDate, endDate := db.GetQuickDateRange(7)

	// 获取任务创建统计
	taskStats, err := statsOp.GetTaskCreationStats(startDate, endDate, "")
	if err != nil {
		klog.Errorf("获取快速任务统计失败: %v", err)
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	// 获取集群发布统计
	clusterStats, err := statsOp.GetClusterDeploymentStats(startDate, endDate, "")
	if err != nil {
		klog.Errorf("获取快速集群统计失败: %v", err)
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	// 计算总数
	totalTasks := 0
	totalClusters := 0
	componentCount := 0

	for _, daily := range taskStats.DailyStats {
		totalTasks += daily.TotalTasks
	}

	componentMap := make(map[string]bool)
	for _, daily := range clusterStats.DailyStats {
		totalClusters += daily.TotalClusters
		for component := range daily.ByComponent {
			componentMap[component] = true
		}
	}
	componentCount = len(componentMap)

	result := map[string]interface{}{
		"date_range":      []string{startDate, endDate},
		"total_tasks":     totalTasks,
		"total_clusters":  totalClusters,
		"component_count": componentCount,
		"period":          "最近7天",
	}

	c.JSON(http.StatusOK, common.NewSuccess(result))
}
