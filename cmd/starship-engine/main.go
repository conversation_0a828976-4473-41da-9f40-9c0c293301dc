package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/spf13/pflag"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship-engine/pkg/config"
	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/environment"
	"git.woa.com/kmetis/starship-engine/pkg/igniter"
	"git.woa.com/kmetis/starship-engine/pkg/leader"
	"git.woa.com/kmetis/starship-engine/pkg/log"
	"git.woa.com/kmetis/starship-engine/pkg/server"
	"git.woa.com/tke/tops/conf"
)

var Version, BuildTime, GoVersion string

func main() {
	var envSettings environment.EnvSettings
	fs := pflag.CommandLine
	envSettings.AddFlags(fs)
	pflag.Parse()
	log.InitLog(envSettings.LogDir)

	if envSettings.Version {
		fmt.Println("Version:", Version, "Build:", BuildTime, "Go:", GoVersion)
		return
	}
	err := config.InitConfig(envSettings.Config)
	if err != nil {
		panic(err)
	}
	db.InitDB(config.GetDatabaseConfig())
	tConfig, err := conf.LoadConfigFromFile(envSettings.TopsFile)
	if err != nil {
		klog.Warningf("Failed to load tops config: %v", err)
		// 使用默认配置
		tConfig = &conf.Config{}
	}
	stop := make(chan struct{})

	// 设置信号处理，捕获 SIGINT (Ctrl+C) 和 SIGTERM (K8s优雅关闭)
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		sig := <-sigCh
		klog.Infof("Received signal: %v, initiating graceful shutdown...", sig)
		close(stop)
	}()

	// 创建Igniter引擎
	engine := igniter.NewIgniter(envSettings, tConfig)

	// 初始化HTTP服务器（所有副本都启动，处理所有API）
	httpServer := server.NewServer()

	// 设置ES管理器
	httpServer.SetESManager(engine)

	// 启动HTTP服务器（所有副本都启动）
	go func() {
		klog.Info("HTTP server starting...")
		if err := httpServer.Start(); err != nil {
			if errors.Is(err, http.ErrServerClosed) {
				klog.Info("HTTP server stopped")
				return
			}
			klog.Errorf("HTTP server failed to start: %v", err)
			close(stop)
			return
		}
	}()

	// 检查是否在K8s环境中运行
	podName := os.Getenv("POD_NAME")
	podNamespace := os.Getenv("POD_NAMESPACE")

	var leaderElection *leader.LeaderElection

	if podName != "" && podNamespace != "" {
		// 在K8s环境中，只有Leader运行Igniter引擎
		klog.Info("Running in Kubernetes environment, enabling leader election for Igniter engine")

		// Leader Election回调
		callbacks := leader.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				klog.Info("Started leading, starting Igniter engine...")
				go func() {
					if err := engine.Start(stop); err != nil {
						klog.Errorf("Igniter engine failed: %v", err)
						close(stop)
					}
				}()
			},
			OnStoppedLeading: func() {
				klog.Info("Stopped leading, Igniter engine will stop automatically")
				// Igniter会通过stop channel自动停止
			},
			OnNewLeader: func(identity string) {
				klog.Infof("New leader elected: %s", identity)
			},
		}

		// 创建Leader Election
		var err error
		leaderElection, err = leader.NewLeaderElectionFromEnv(callbacks)
		if err != nil {
			klog.Errorf("Failed to create leader election: %v", err)
			// 降级到单实例模式
			klog.Warning("Falling back to single instance mode")
			leaderElection = nil
		}

		// 启动Leader Election（如果启用）
		if leaderElection != nil {
			go func() {
				klog.Info("Starting leader election...")
				if err := leaderElection.Run(context.Background()); err != nil {
					klog.Errorf("Leader election failed: %v", err)
					close(stop)
				}
			}()
		} else {
			// Leader Election失败，直接启动Igniter
			go func() {
				if err := engine.Start(stop); err != nil {
					klog.Errorf("Igniter engine failed: %v", err)
					close(stop)
				}
			}()
		}
	} else {
		// 非K8s环境或环境变量未设置，直接启动Igniter
		klog.Info("Running in standalone mode, starting Igniter engine directly")
		go func() {
			if err := engine.Start(stop); err != nil {
				klog.Errorf("Igniter engine failed: %v", err)
				close(stop)
			}
		}()
	}

	<-stop

	// 优雅关闭
	klog.Info("Shutting down services...")

	// 停止Leader Election
	if leaderElection != nil {
		leaderElection.Stop()
	}

	// 停止HTTP服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := httpServer.Stop(ctx); err != nil {
		klog.Errorf("Error shutting down HTTP server: %v", err)
	} else {
		klog.Info("HTTP server stopped successfully")
	}
}
