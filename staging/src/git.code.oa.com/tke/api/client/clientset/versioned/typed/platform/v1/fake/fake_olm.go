/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeOLMs implements OLMInterface
type FakeOLMs struct {
	Fake *FakePlatformV1
}

var olmsResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "olms"}

var olmsKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "OLM"}

// Get takes name of the oLM, and returns the corresponding oLM object, and an error if there is any.
func (c *FakeOLMs) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.OLM, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(olmsResource, name), &platformv1.OLM{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OLM), err
}

// List takes label and field selectors, and returns the list of OLMs that match those selectors.
func (c *FakeOLMs) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.OLMList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(olmsResource, olmsKind, opts), &platformv1.OLMList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.OLMList{ListMeta: obj.(*platformv1.OLMList).ListMeta}
	for _, item := range obj.(*platformv1.OLMList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested oLMs.
func (c *FakeOLMs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(olmsResource, opts))
}

// Create takes the representation of a oLM and creates it.  Returns the server's representation of the oLM, and an error, if there is any.
func (c *FakeOLMs) Create(ctx context.Context, oLM *platformv1.OLM, opts v1.CreateOptions) (result *platformv1.OLM, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(olmsResource, oLM), &platformv1.OLM{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OLM), err
}

// Update takes the representation of a oLM and updates it. Returns the server's representation of the oLM, and an error, if there is any.
func (c *FakeOLMs) Update(ctx context.Context, oLM *platformv1.OLM, opts v1.UpdateOptions) (result *platformv1.OLM, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(olmsResource, oLM), &platformv1.OLM{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OLM), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeOLMs) UpdateStatus(ctx context.Context, oLM *platformv1.OLM, opts v1.UpdateOptions) (*platformv1.OLM, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(olmsResource, "status", oLM), &platformv1.OLM{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OLM), err
}

// Delete takes name of the oLM and deletes it. Returns an error if one occurs.
func (c *FakeOLMs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(olmsResource, name), &platformv1.OLM{})
	return err
}

// Patch applies the patch and returns the patched oLM.
func (c *FakeOLMs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.OLM, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(olmsResource, name, pt, data, subresources...), &platformv1.OLM{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.OLM), err
}
