package db

import (
	"fmt"
	"testing"

	"git.woa.com/kmetis/starship-engine/pkg/consts"
)

func TestUpdateStatusByTaskId2(t *testing.T) {
	InitSqliteDB()

	err := UpdateTaskByTaskId(4, map[string]interface{}{
		"status": "Completed",
		"notify": false,
	})

	if err != nil {
		panic(err)
	}
}

func TestGetTaskByTaskId(t *testing.T) {
	InitSqliteDB()
	task, err := GetTaskByTaskId(1)
	if err != nil {
		panic(err)
	}

	if task.Notify {
		fmt.Println("打印机器人")
	} else {
		fmt.Println("不打印")
	}

}

func TestGetTaskByFields(t *testing.T) {
	InitSqliteDB()
	fields, err := GetTaskByFields(map[string]interface{}{
		"status": consts.RUNNING,
		"notify": true,
	})
	if err != nil {
		panic(err)
	}
	for _, field := range fields {
		fmt.Println(field)
	}
}
