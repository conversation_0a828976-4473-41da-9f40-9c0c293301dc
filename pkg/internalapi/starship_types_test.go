package internalapi

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestTaskWrapper_UnmarshalJSON 测试 TaskWrapper 的动态命名规则解析
func TestTaskWrapper_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		wantErr  bool
		validate func(*testing.T, *TaskWrapper)
	}{
		{
			name: "PascalCase格式（云API标准）",
			jsonData: `{
				"TaskId": 123,
				"Name": "eklet upgrade",
				"AppName": "eklet",
				"Status": "done",
				"Stage": "precheck",
				"ExtendInfo": "{\"test\":\"value\"}",
				"SubTasks": [
					{
						"SubTaskId": 1,
						"Action": "precheck",
						"Status": "done",
						"Risks": [
							{
								"Id": 1,
								"Name": "Test Risk",
								"Code": "ERROR",
								"Level": "warning",
								"Detail": "Test detail"
							}
						]
					}
				]
			}`,
			wantErr: false,
			validate: func(t *testing.T, wrapper *TaskWrapper) {
				assert.Equal(t, int64(123), wrapper.TaskId)
				assert.Equal(t, "eklet upgrade", wrapper.Name)
				assert.Equal(t, "eklet", wrapper.AppName)
				assert.Equal(t, "done", wrapper.Status)
				assert.Equal(t, "precheck", wrapper.Stage)
				assert.Equal(t, "{\"test\":\"value\"}", wrapper.ExtendInfo)
				assert.Len(t, wrapper.Subtasks, 1)
				assert.Equal(t, int64(1), wrapper.Subtasks[0].SubtaskId)
				assert.Equal(t, "precheck", wrapper.Subtasks[0].Action)
				assert.Len(t, wrapper.Subtasks[0].Risks, 1)
				assert.Equal(t, int64(1), wrapper.Subtasks[0].Risks[0].Id)
				assert.Equal(t, "Test Risk", wrapper.Subtasks[0].Risks[0].Name)
			},
		},
		{
			name: "snake_case格式（protobuf标准）",
			jsonData: `{
				"task_id": 456,
				"name": "test task",
				"app_name": "test-app",
				"status": "running",
				"stage": "upgrade",
				"extend_info": "{\"skip\":true}",
				"sub_tasks": [
					{
						"sub_task_id": 2,
						"action": "upgrade",
						"status": "running",
						"risks": [
							{
								"id": 2,
								"name": "Another Risk",
								"code": "WARNING",
								"level": "info",
								"detail": "Info detail"
							}
						]
					}
				]
			}`,
			wantErr: false,
			validate: func(t *testing.T, wrapper *TaskWrapper) {
				assert.Equal(t, int64(456), wrapper.TaskId)
				assert.Equal(t, "test task", wrapper.Name)
				assert.Equal(t, "test-app", wrapper.AppName)
				assert.Equal(t, "running", wrapper.Status)
				assert.Equal(t, "upgrade", wrapper.Stage)
				assert.Equal(t, "{\"skip\":true}", wrapper.ExtendInfo)
				assert.Len(t, wrapper.Subtasks, 1)
				assert.Equal(t, int64(2), wrapper.Subtasks[0].SubtaskId)
				assert.Equal(t, "upgrade", wrapper.Subtasks[0].Action)
				assert.Len(t, wrapper.Subtasks[0].Risks, 1)
				assert.Equal(t, int64(2), wrapper.Subtasks[0].Risks[0].Id)
				assert.Equal(t, "Another Risk", wrapper.Subtasks[0].Risks[0].Name)
			},
		},
		{
			name: "混合命名格式",
			jsonData: `{
				"TaskId": 789,
				"task_name": "mixed task",
				"AppName": "mixed-app",
				"status": "failed",
				"Stage": "postcheck",
				"ExtendInfo": "{\"mixed\":true}",
				"SubTasks": [
					{
						"sub_task_id": 3,
						"Action": "postcheck",
						"status": "failed",
						"Risks": [
							{
								"Id": 3,
								"name": "Mixed Risk",
								"Code": "FAILED",
								"level": "fatal",
								"Detail": "Fatal detail"
							}
						]
					}
				]
			}`,
			wantErr: false,
			validate: func(t *testing.T, wrapper *TaskWrapper) {
				assert.Equal(t, int64(789), wrapper.TaskId)
				assert.Equal(t, "mixed task", wrapper.Name)
				assert.Equal(t, "mixed-app", wrapper.AppName)
				assert.Equal(t, "failed", wrapper.Status)
				assert.Equal(t, "postcheck", wrapper.Stage)
				assert.Equal(t, "{\"mixed\":true}", wrapper.ExtendInfo)
				assert.Len(t, wrapper.Subtasks, 1)
				assert.Equal(t, int64(3), wrapper.Subtasks[0].SubtaskId)
				assert.Equal(t, "postcheck", wrapper.Subtasks[0].Action)
				assert.Len(t, wrapper.Subtasks[0].Risks, 1)
				assert.Equal(t, int64(3), wrapper.Subtasks[0].Risks[0].Id)
				assert.Equal(t, "Mixed Risk", wrapper.Subtasks[0].Risks[0].Name)
			},
		},
		{
			name: "空SubTasks数组",
			jsonData: `{
				"TaskId": 999,
				"Name": "empty subtasks",
				"Status": "pending",
				"SubTasks": []
			}`,
			wantErr: false,
			validate: func(t *testing.T, wrapper *TaskWrapper) {
				assert.Equal(t, int64(999), wrapper.TaskId)
				assert.Equal(t, "empty subtasks", wrapper.Name)
				assert.Equal(t, "pending", wrapper.Status)
				assert.Len(t, wrapper.Subtasks, 0)
			},
		},
		{
			name: "无效JSON",
			jsonData: `{
				"TaskId": "invalid",
				"Name": 123,
			}`,
			wantErr: true,
			validate: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var wrapper TaskWrapper
			err := json.Unmarshal([]byte(tt.jsonData), &wrapper)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.validate != nil {
					tt.validate(t, &wrapper)
				}
			}
		})
	}
}

// TestTaskWrapper_MarshalJSON 测试 TaskWrapper 的序列化
func TestTaskWrapper_MarshalJSON(t *testing.T) {
	wrapper := TaskWrapper{
		TaskId:     123,
		Name:       "test-task",
		AppName:    "eklet",
		Status:     "running",
		Stage:      "precheck",
		ExtendInfo: `{"test": "value"}`,
		Subtasks: []*SubTaskWrapper{
			{
				SubtaskId: 1,
				Action:    "precheck",
				Status:    "done",
				Risks: []*RiskWrapper{
					{
						Id:     1,
						Name:   "Test Risk",
						Code:   "ERROR",
						Level:  "warning",
						Detail: "Test detail",
					},
				},
			},
		},
	}

	data, err := json.Marshal(wrapper)
	assert.NoError(t, err)
	assert.NotEmpty(t, data)
	
	// 验证序列化结果包含预期字段
	var result map[string]interface{}
	err = json.Unmarshal(data, &result)
	assert.NoError(t, err)
	assert.Equal(t, float64(123), result["TaskId"])
	assert.Equal(t, "test-task", result["Name"])
	assert.Equal(t, "eklet", result["AppName"])
}

// TestDescribeAppUpgradingProgressResponseImp_UnmarshalJSON 测试完整响应结构的解析
func TestDescribeAppUpgradingProgressResponseImp_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		wantErr  bool
	}{
		{
			name: "PascalCase格式的Task字段",
			jsonData: `{
				"RequestId": "req-123",
				"Task": {
					"TaskId": 123,
					"Name": "test-task",
					"Status": "running"
				}
			}`,
			wantErr: false,
		},
		{
			name: "snake_case格式的Task字段",
			jsonData: `{
				"RequestId": "req-456",
				"Task": {
					"task_id": 456,
					"task_name": "test-task-2",
					"task_status": "completed"
				}
			}`,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var response DescribeAppUpgradingProgressResponseImp
			err := json.Unmarshal([]byte(tt.jsonData), &response)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, response.RequestId)
			}
		})
	}
}

// TestTaskWrapper_RealWorldData 测试真实世界的数据格式
func TestTaskWrapper_RealWorldData(t *testing.T) {
	// 用户提供的实际JSON数据
	jsonData := `{
		"AppName": "eklet",
		"Batch": 1,
		"ChangeId": "102323-1758091250",
		"ClusterId": "cls-ldva5idu",
		"CreateTime": "2025-09-17 07:38:03",
		"Envs": "[]",
		"ExtendInfo": "",
		"Name": "eklet upgrade",
		"Reason": "",
		"Region": "",
		"Stage": "",
		"Status": "pending",
		"SubTasks": [
			{
				"Action": "precheck",
				"AppName": "eklet",
				"CostTime": 0,
				"CreateTime": "2025-09-17 07:38:03",
				"ParentTaskId": 1,
				"Reason": "",
				"Risks": [],
				"Status": "pending",
				"SubTaskId": 1,
				"UpdateTime": "2025-09-17 07:38:03"
			},
			{
				"Action": "upgrade",
				"AppName": "eklet",
				"CostTime": 0,
				"CreateTime": "2025-09-17 07:38:03",
				"ParentTaskId": 1,
				"Reason": "",
				"Risks": [],
				"Status": "pending",
				"SubTaskId": 2,
				"UpdateTime": "2025-09-17 07:38:03"
			}
		],
		"TaskId": 1,
		"Timeout": 60,
		"Type": "",
		"UpdateTime": "2025-09-17 07:38:03"
	}`

	var wrapper TaskWrapper
	err := json.Unmarshal([]byte(jsonData), &wrapper)
	assert.NoError(t, err)

	// 验证关键字段
	assert.Equal(t, int64(1), wrapper.TaskId)
	assert.Equal(t, "eklet upgrade", wrapper.Name)
	assert.Equal(t, "eklet", wrapper.AppName)
	assert.Equal(t, "pending", wrapper.Status)
	assert.Equal(t, "cls-ldva5idu", wrapper.ClusterId)
	assert.Equal(t, int32(1), wrapper.Batch)
	assert.Equal(t, int32(60), wrapper.Timeout)
	assert.Len(t, wrapper.Subtasks, 2)

	// 验证子任务
	assert.Equal(t, int64(1), wrapper.Subtasks[0].SubtaskId)
	assert.Equal(t, "precheck", wrapper.Subtasks[0].Action)
	assert.Equal(t, "pending", wrapper.Subtasks[0].Status)
	assert.Equal(t, int64(1), wrapper.Subtasks[0].ParentTaskId)

	assert.Equal(t, int64(2), wrapper.Subtasks[1].SubtaskId)
	assert.Equal(t, "upgrade", wrapper.Subtasks[1].Action)
	assert.Equal(t, "pending", wrapper.Subtasks[1].Status)

	// 验证序列化后的结果
	serialized, err := json.Marshal(wrapper)
	assert.NoError(t, err)
	t.Logf("Serialized result: %s", string(serialized))
}

// BenchmarkTaskWrapper_UnmarshalJSON 性能测试
func BenchmarkTaskWrapper_UnmarshalJSON(b *testing.B) {
	jsonData := `{
		"TaskId": 123,
		"Name": "benchmark-task",
		"Status": "running",
		"CreateTime": "2023-01-01T00:00:00Z",
		"SubTasks": [
			{
				"SubTaskId": 1,
				"Action": "precheck",
				"Status": "done",
				"Risks": [
					{
						"Id": 1,
						"Name": "Test Risk",
						"Code": "ERROR",
						"Level": "warning"
					}
				]
			}
		]
	}`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var wrapper TaskWrapper
		json.Unmarshal([]byte(jsonData), &wrapper)
	}
}

// BenchmarkTaskWrapper_MarshalJSON 序列化性能测试
func BenchmarkTaskWrapper_MarshalJSON(b *testing.B) {
	wrapper := TaskWrapper{
		TaskId:  123,
		Name:    "benchmark-task",
		Status:  "running",
		Subtasks: []*SubTaskWrapper{
			{
				SubtaskId: 1,
				Action:    "precheck",
				Status:    "done",
				Risks: []*RiskWrapper{
					{
						Id:    1,
						Name:  "Test Risk",
						Code:  "ERROR",
						Level: "warning",
					},
				},
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		json.Marshal(wrapper)
	}
}
