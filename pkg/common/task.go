package common

import (
	"fmt"
	"os"
	"regexp"
	"strings"
	"text/tabwriter"
	"time"

	"git.woa.com/kmetis/starship-engine/pkg/changeOrder"
	"git.woa.com/kmetis/starship-engine/pkg/consts"
	"git.woa.com/kmetis/starship-engine/pkg/db"
	"git.woa.com/kmetis/starship-engine/pkg/model"
	"git.woa.com/kmetis/starship-engine/pkg/util"

	"gopkg.in/yaml.v3"
)

func CreateTask(taskName, cluster, region, appAction string) error {
	//task := &db.Task{
	//	TaskName:  taskName,
	//	ClusterId: cluster,
	//	Region:    region,
	//	AppAction: appAction,
	//}
	//if err := db.CreateTask(task); err != nil {
	//	klog.V(3).ErrorS(err, "Failed to create task.")
	//	return err
	//}
	return nil
}

func CreateTaskFromFile(fileName, appAction string) (string, error) {
	// read yaml
	task, err := readAndParseYAML(fileName)
	if err != nil {
		return "", err
	}

	// check task
	if err := validateTask(task); err != nil {
		return "", err
	}

	if err := db.CreateTaskWithTx(&task, appAction); err != nil {
		return "", err
	}

	return task.Name, nil
}

func CreateTaskFromRequest(task *model.Task, appAction string) error {
	// 验证任务
	if err := checkChangeId(*task); err != nil {
		return fmt.Errorf("check change order failed: %v", err)
	}

	// 创建任务
	if err := db.CreateTaskWithTx(task, appAction); err != nil {
		return fmt.Errorf("创建任务失败: %v", err)
	}

	return nil
}

func CreateTaskByClusterOptions(task *model.Task, appAction string) error {
	// 验证任务
	if err := checkChangeId(*task); err != nil {
		return fmt.Errorf("check change order failed: %v", err)
	}

	if err := db.CreateTaskWithoutBatch(task, appAction); err != nil {
		return fmt.Errorf("创建任务失败：%v", err)
	}
	return nil
}

func GetList(appAction, taskName string) (err error) {
	var getList []*db.TaskNumCount
	//db.Get
	if taskName == "" {
		getList, err = db.GetTaskStats(appAction, "", nil)
		if err != nil {
			return err
		}
	} else {
		getList, err = db.GetTaskStats(appAction, taskName, nil)
		if err != nil {
			return err
		}
	}

	if len(getList) == 0 {
		fmt.Println("No resource found")
		return nil
	}
	// 创建 tabwriter.Writer 对象
	writer := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', tabwriter.TabIndent)
	// 输出表头
	fmt.Fprintln(writer, "TaskId\tTaskName\tComponent\tTotal\tSuccess\tFailed\tRunning\tStatus\tAge")
	for _, get := range getList {
		// 计算 age
		age := util.FormatAge(time.Since(get.CreateTime))
		// 输出数据
		fmt.Fprintf(writer, "%d\t%s\t%s\t%d\t%d\t%d\t%d\t%s\t%s\n",
			get.TaskId, get.TaskName, get.Component, get.Total, get.Success, get.Failed, get.Running, get.Status, age)
	}
	writer.Flush()

	return nil
}

func DescribeTask(appAction, taskName string) (err error) {
	task, clusters, err := db.GetClusterByTask(appAction, taskName)
	if err != nil {
		return err
	}
	if len(clusters) == 0 {
		fmt.Println("No resource found")
		return nil
	}

	// 创建 tabwriter.Writer 对象
	writer := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', tabwriter.TabIndent)
	if task.Component == consts.APPFABRIC || task.Component == consts.EKS_POD {
		// 输出表头
		fmt.Fprintln(writer, "TaskId\tBatchId\tClusterId\tNamespace\tname\tStage\tStatus\tReason\tAge")
		for _, cluster := range clusters {
			// 计算 age
			age := util.FormatAge(time.Since(cluster.CreateTime))
			// 输出数据
			fmt.Fprintf(writer, "%d\t%d\t%s\t%s\t%s\t%s\t%s\t%s\t%s\n",
				cluster.TaskId, cluster.BatchId, cluster.ClusterId, cluster.NameSpace, cluster.Name, cluster.Stage, cluster.Status, cluster.Reason, age)
		}
	} else {
		// 输出表头
		fmt.Fprintln(writer, "TaskId\tBatchId\tClusterId\tMetaId\tRegion\tType\tStage\tStatus\tReason\tAge")

		for _, cluster := range clusters {
			// 计算 age
			age := util.FormatAge(time.Since(cluster.CreateTime))
			// 输出数据
			fmt.Fprintf(writer, "%d\t%d\t%s\t%s\t%s\t%s\t%s\t%s\t%s\t%s\n",
				cluster.TaskId, cluster.BatchId, cluster.ClusterId, cluster.MetaID, cluster.Region,
				cluster.Type, cluster.Stage, cluster.Status, cluster.Reason, age)
		}
	}
	writer.Flush()

	return nil
}

func PauseTask(appAction, taskName string) error {
	err := db.UpdateStatusByName(appAction, taskName, consts.PAUSE_STA)
	if err != nil {
		fmt.Printf("Failed to update task status, %v\n", err.Error())
		return err
	}
	return nil
}

func UnpauseTask(appAction, taskName string) error {
	err := db.UpdateStatusByName(appAction, taskName, consts.RUNNING)
	if err != nil {
		fmt.Printf("Failed to update task status, %v\n", err.Error())
		return err
	}
	return nil
}

func readAndParseYAML(fileName string) (model.Task, error) {
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("Failed to read %s\n", fileName)
		return model.Task{}, err
	}
	var task model.Task
	if err := yaml.Unmarshal(data, &task); err != nil {
		fmt.Println("Failed to unmarshal file.")
		return model.Task{}, err
	}
	return task, nil
}

func validateTask(task model.Task) error {
	err := task.Validate()
	if err != nil {
		fmt.Println(err)
		return err
	}

	return checkChangeId(task)
}

func checkChangeId(task model.Task) error {
	order, err := changeOrder.GetPostOrder(task.UserData.ChangeId)
	if err != nil {
		fmt.Println(err)
		return err
	}

	if order.Data.Item.ID == "" {
		fmt.Println("Cannot find change order according to changeId.")
		return fmt.Errorf("failed to find change order according to changeId")
	}

	// check status, 3 represent change order is pass
	if order.Data.Item.Status != "3" {
		fmt.Println("Change order is not pass.")
		return fmt.Errorf("change order is not pass")
	}

	// 1.check time is valid
	startTimeStr := order.Data.Item.TimeWindow.StartTime
	endTimeStr := order.Data.Item.TimeWindow.EndTime
	if startTimeStr != "" && endTimeStr != "" {
		if valid := util.CheckTimeIsValid(startTimeStr, endTimeStr); !valid {
			return fmt.Errorf("check time error")
		}
	} else {
		fmt.Println("Publish TimeWindow is null.")
		return fmt.Errorf("publish timeWindow is null")
	}

	// 2.1 check user is matching changeId
	if contains := util.Contains(order.Data.Item.Applicant, task.UserData.User); !contains {
		fmt.Printf("Current user %s is not applicant.\n", task.UserData.User)
		return fmt.Errorf("current user %s is not applicant", task.UserData.User)
	}

	// 2.2 Verify if the user is a member of publish group
	allowedUsers, err := os.ReadFile("/etc/config/users")
	if err != nil {
		fmt.Printf("Failed to read allowed users: %v\n", err)
		return fmt.Errorf("failed to read allowed users")
	}

	if !strings.Contains(string(allowedUsers), task.UserData.User) {
		fmt.Printf("Current user %s is not allowed to execute this task.\n", task.UserData.User)
		return fmt.Errorf("current user %s is not allowed", task.UserData.User)
	}

	// 3.check component is matching changeId
	componentMap, err := util.LoadMap("/etc/config/components")
	if err != nil {
		fmt.Printf("Failed to load component map: %v\n", err)
		return err
	}

	for _, plmApp := range order.Data.Item.PlmApp {
		if contains := util.Contains(componentMap[plmApp.Name], task.Component.Name); contains {
			// 4. check cluster file validation
			if err := validateClusterFile(task); err != nil {
				return err
			}
			return nil
		}
	}
	return fmt.Errorf("current upgrade component %s is not in change order", task.Component.Name)
}

// validateClusterFile 验证集群文件的内容
func validateClusterFile(task model.Task) error {
	// 检查是否提供了cluster list 或 文件内容
	hasClusterFileContent := strings.TrimSpace(task.ClusterFileContent) != ""
	hasCluster := len(task.Cluster) > 0

	if !hasCluster && !hasClusterFileContent {
		return nil // 如果都没有提供，跳过验证（由其他验证逻辑处理）
	}

	// 根据组件类型选择相应的验证方法
	switch task.Component.Name {
	case consts.EKLET_AGENT:
		return validateEkletAgentFileData(task)
	case consts.TCR:
		return nil
	case consts.EKS_POD:
		return nil
	default:
		// 对于普通组件，验证集群数据（支持文件内容和集群数组）
		return validateNormalComponentFileData(task)
	}
}

func isValidClusterID(clusterID string) bool {
	clusterIdRegex := regexp.MustCompile(`^cls-[a-zA-Z0-9]{8}$`)
	return clusterIdRegex.MatchString(clusterID)
}

// isValidNamespace 验证命名空间格式：eks-开头，后跟8位数字或字母
func isValidNamespace(namespace string) bool {
	namespaceRegex := regexp.MustCompile(`^eks-[a-zA-Z0-9]{8}$`)
	return namespaceRegex.MatchString(namespace)
}

// validateEkletAgentFileData 验证eklet-agent组件的集群数据（只验证文件内容和task.cluster）
func validateEkletAgentFileData(task model.Task) error {
	hasClusterFileContent := strings.TrimSpace(task.ClusterFileContent) != ""
	hasCluster := len(task.Cluster) > 0

	// 检查是否恰当地提供了一个数据源（不允许同时提供两个数据源）
	if hasClusterFileContent && hasCluster {
		return fmt.Errorf("both cluster file content and task.Cluster provided for eklet-agent component, only one data source is allowed")
	}

	// 检查是否至少提供了一个数据源
	if !hasClusterFileContent && !hasCluster {
		return fmt.Errorf("neither cluster file content nor task.Cluster provided for eklet-agent component")
	}

	// 验证ClusterFileContent（如果提供）
	if hasClusterFileContent {
		agents, err := util.ReadEkletAgentFileFromContent(task.ClusterFileContent)
		if err != nil {
			return fmt.Errorf("failed to read eklet-agent file content: %v", err)
		}

		if len(agents) == 0 {
			return fmt.Errorf("eklet-agent file content is empty")
		}

		// 验证集群ID格式
		for i, agent := range agents {
			if !isValidClusterID(agent.ClusterId) {
				return fmt.Errorf("invalid cluster ID format at line %d: %s", i+1, agent.ClusterId)
			}
			// 验证命名空间格式：必须是eks-开头，然后八位数字或字母
			if !isValidNamespace(agent.Namespace) {
				return fmt.Errorf("invalid namespace format at index %d: %s", i, agent.Namespace)
			}
		}
		fmt.Printf("Successfully validated eklet-agent file content with.\n")
		return nil
	}

	// 验证task.Cluster字段（如果提供）
	if hasCluster {
		if err := validateTaskClusterArray(task.Cluster, task.Component.Name); err != nil {
			return err
		}
		return nil
	}
	return nil
}

// validateNormalComponentFileData 验证普通组件的集群数据（验证文件内容和task.cluster）
func validateNormalComponentFileData(task model.Task) error {
	hasClusterFileContent := strings.TrimSpace(task.ClusterFileContent) != ""
	hasCluster := len(task.Cluster) > 0

	// 对于普通组件，允许同时提供多个数据源，按优先级使用
	// 但至少需要提供一个数据源
	if !hasClusterFileContent && !hasCluster {
		return fmt.Errorf("neither cluster file content nor task.Cluster provided for component %s", task.Component.Name)
	}

	// 验证ClusterFileContent（如果提供，优先验证）
	if hasClusterFileContent {
		clusters, err := util.ReadFileFromContent(task.ClusterFileContent)
		if err != nil {
			return fmt.Errorf("failed to read cluster file content: %v", err)
		}

		if len(clusters) == 0 {
			return fmt.Errorf("cluster file content is empty")
		}

		// 验证集群ID格式
		for i, clusterID := range clusters {
			clusterID = strings.TrimSpace(clusterID)
			if clusterID == "" {
				continue // 跳过空行
			}
			if !isValidClusterID(clusterID) {
				return fmt.Errorf("invalid cluster ID format at line %d: %s", i+1, clusterID)
			}
		}
		fmt.Printf("Successfully validated cluster file content with %d clusters.\n", len(clusters))
		return nil
	}

	// 验证task.Cluster字段（如果提供）
	if hasCluster {
		if err := validateTaskClusterArray(task.Cluster, task.Component.Name); err != nil {
			return err
		}
		return nil
	}
	return nil
}

// validateTaskClusterArray 验证task.Cluster字段格式，根据组件类型要求不同格式
func validateTaskClusterArray(clusters []string, componentName string) error {
	if len(clusters) == 0 {
		return nil // 空数组允许，由其他验证逻辑处理
	}

	// 根据组件类型确定期望的格式
	isEkletAgent := componentName == consts.EKLET_AGENT
	var expectedFormat string
	if isEkletAgent {
		expectedFormat = "Format: Cluster ID + EksID (cls-xxxxxxxx eks-xxxxxxxx)"
	} else {
		expectedFormat = "Format: Cluster ID (cls-xxxxxxxx)"
	}
	// 检测第一个有效条目的格式，确定整个数组应该使用的格式
	var detectedFormat int // 1=格式1, 2=格式2

	for i, cluster := range clusters {
		cluster = strings.TrimSpace(cluster)
		if cluster == "" {
			continue // 跳过空字符串
		}

		// 检查当前条目是格式1还是格式2
		var currentFormat int
		if isValidClusterID(cluster) {
			currentFormat = 1 // 格式1: 纯集群ID
		} else {
			parts := strings.Fields(cluster)
			if len(parts) == 2 {
				clusterID := parts[0]
				namespace := parts[1]
				// 验证集群ID格式
				if !isValidClusterID(clusterID) {
					return fmt.Errorf("invalid cluster ID format at index %d: %s", i, clusterID)
				}

				// 验证命名空间格式
				if !isValidNamespace(namespace) {
					return fmt.Errorf("invalid namespace format at index %d: %s", i, namespace)
				}

				currentFormat = 2 // 格式2: 集群ID+命名空间
			} else {
				return fmt.Errorf("invalid task.Cluster format at index %d: %s (expected format: %s)", i, cluster, expectedFormat)
			}
		}

		// 设置或验证格式一致性
		if detectedFormat == 0 {
			detectedFormat = currentFormat
		} else if detectedFormat != currentFormat {
			return fmt.Errorf("mixed formats detected in task.Cluster array. All entries must use the same format. Expected: %s", expectedFormat)
		}
	}

	// 验证检测到的格式是否符合组件要求
	if isEkletAgent && detectedFormat == 1 {
		return fmt.Errorf("eklet-agent component requires format 2 (cls-xxxxxxxx eks-xxxxxxxx), but format 1 detected")
	} else if !isEkletAgent && detectedFormat == 2 {
		return fmt.Errorf("component %s requires format 1 (cls-xxxxxxxx), but format 2 detected", componentName)
	}

	fmt.Printf("Successfully validated task.Cluster array with %d entries using %s.\n", len(clusters), expectedFormat)
	return nil
}
