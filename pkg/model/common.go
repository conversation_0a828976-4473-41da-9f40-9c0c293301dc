package model

type ExtendInfo struct {
	Envs                    []map[string]string `json:"Envs,omitempty"`
	Args                    []map[string]string `json:"Args,omitempty"`
	ContainerName           string              `json:"ContainerName,omitempty"`
	Scheduler               bool                `json:"Scheduler,omitempty"`
	UpdateStrategy          string              `json:"UpdateStrategy,omitempty"`
	SkipWarningPreCheckItem bool                `json:"SkipWarningPreCheckItem,omitempty"`

	HostAlias   map[string][]string `json:"HostAlias,omitempty"`
	Annotations []map[string]string `json:"Annotations,omitempty"`

	// 支持camp发布
	Name      string  `json:"Name,omitempty"`
	Namespace string  `json:"Namespace,omitempty"`
	Patch     []Patch `json:"Patch,omitempty"`

	// rollback
	Rollback map[string]string `json:"Rollback,omitempty"`

	// eksId
	EksId string `json:"EksId,omitempty"`

	// rawvalues支持
	RawValues string `json:"RawValues,omitempty"`
}

type Patch struct {
	// 变更key
	Key string `json:"key"`
	// 变更value
	Value      string `json:"value,omitempty"`
	Scope      string `json:"scope,omitempty"`
	OriginData string `json:"originData,omitempty"`
}

// 集群筛选条件，如：[{"key": "Region", "operator":"in","value":"bj,sh,gz"}]
type ClusterOption struct {
	Key      string `json:"key"`
	Operator string `json:"operator"`
	Value    string `json:"value"`
}
