/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
	v1 "tkestack.io/tke/api/application/v1"
	scheme "tkestack.io/tke/api/client/clientset/versioned/scheme"
)

// AppsGetter has a method to return a AppInterface.
// A group's client should implement this interface.
type AppsGetter interface {
	Apps(namespace string) AppInterface
}

// AppInterface has methods to work with App resources.
type AppInterface interface {
	Create(ctx context.Context, app *v1.App, opts metav1.CreateOptions) (*v1.App, error)
	Update(ctx context.Context, app *v1.App, opts metav1.UpdateOptions) (*v1.App, error)
	UpdateStatus(ctx context.Context, app *v1.App, opts metav1.UpdateOptions) (*v1.App, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.App, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.AppList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.App, err error)
	AppExpansion
}

// apps implements AppInterface
type apps struct {
	client rest.Interface
	ns     string
}

// newApps returns a Apps
func newApps(c *ApplicationV1Client, namespace string) *apps {
	return &apps{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the app, and returns the corresponding app object, and an error if there is any.
func (c *apps) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.App, err error) {
	result = &v1.App{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("apps").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Apps that match those selectors.
func (c *apps) List(ctx context.Context, opts metav1.ListOptions) (result *v1.AppList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.AppList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("apps").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested apps.
func (c *apps) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("apps").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a app and creates it.  Returns the server's representation of the app, and an error, if there is any.
func (c *apps) Create(ctx context.Context, app *v1.App, opts metav1.CreateOptions) (result *v1.App, err error) {
	result = &v1.App{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("apps").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(app).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a app and updates it. Returns the server's representation of the app, and an error, if there is any.
func (c *apps) Update(ctx context.Context, app *v1.App, opts metav1.UpdateOptions) (result *v1.App, err error) {
	result = &v1.App{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("apps").
		Name(app.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(app).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *apps) UpdateStatus(ctx context.Context, app *v1.App, opts metav1.UpdateOptions) (result *v1.App, err error) {
	result = &v1.App{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("apps").
		Name(app.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(app).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the app and deletes it. Returns an error if one occurs.
func (c *apps) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("apps").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched app.
func (c *apps) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.App, err error) {
	result = &v1.App{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("apps").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
