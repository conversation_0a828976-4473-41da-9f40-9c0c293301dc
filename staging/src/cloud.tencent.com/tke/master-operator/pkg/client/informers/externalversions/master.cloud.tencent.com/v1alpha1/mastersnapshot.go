/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	time "time"

	mastercloudtencentcomv1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	versioned "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	internalinterfaces "cloud.tencent.com/tke/master-operator/pkg/client/informers/externalversions/internalinterfaces"
	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/client/listers/master.cloud.tencent.com/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// MasterSnapshotInformer provides access to a shared informer and lister for
// MasterSnapshots.
type MasterSnapshotInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1alpha1.MasterSnapshotLister
}

type masterSnapshotInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
	namespace        string
}

// NewMasterSnapshotInformer constructs a new informer for MasterSnapshot type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewMasterSnapshotInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredMasterSnapshotInformer(client, namespace, resyncPeriod, indexers, nil)
}

// NewFilteredMasterSnapshotInformer constructs a new informer for MasterSnapshot type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredMasterSnapshotInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options v1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.MasterV1alpha1().MasterSnapshots(namespace).List(context.TODO(), options)
			},
			WatchFunc: func(options v1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.MasterV1alpha1().MasterSnapshots(namespace).Watch(context.TODO(), options)
			},
		},
		&mastercloudtencentcomv1alpha1.MasterSnapshot{},
		resyncPeriod,
		indexers,
	)
}

func (f *masterSnapshotInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredMasterSnapshotInformer(client, f.namespace, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *masterSnapshotInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&mastercloudtencentcomv1alpha1.MasterSnapshot{}, f.defaultInformer)
}

func (f *masterSnapshotInformer) Lister() v1alpha1.MasterSnapshotLister {
	return v1alpha1.NewMasterSnapshotLister(f.Informer().GetIndexer())
}
