package timer

import (
	"time"

	"k8s.io/klog/v2"
)

type Timer struct {
	name     string
	period   int
	callback func()

	stop chan struct{}
}

func NewTimer(name string, period int, callback func()) *Timer {
	return &Timer{
		name:     name,
		period:   period,
		callback: callback,
		stop:     make(chan struct{}),
	}
}

func (t *Timer) Run() {
	klog.Infof("%s start", t.name)
	ticker := time.NewTicker(time.Duration(t.period) * time.Second)
	defer func() {
		ticker.Stop()
	}()

Loop:
	for {
		select {
		case <-t.stop:
			break Loop
		case <-ticker.C:
			func() {
				defer func() {
					if err := recover(); err != nil {
						klog.Errorf("handle failed: %v", err)
					}
				}()
				t.callback()
			}()
		}
	}

	klog.Infof("%s stop", t.name)
}

func (t *Timer) Stop() {
	if t.stop != nil {
		t.stop <- struct{}{}
	}
}
