/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKEStack
 * available.
 *
 * Copyright (C) 2012-2023 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	platformv1 "git.code.oa.com/tke/api/platform/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeDeSchedulers implements DeSchedulerInterface
type FakeDeSchedulers struct {
	Fake *FakePlatformV1
}

var deschedulersResource = schema.GroupVersionResource{Group: "platform.tke", Version: "v1", Resource: "deschedulers"}

var deschedulersKind = schema.GroupVersionKind{Group: "platform.tke", Version: "v1", Kind: "DeScheduler"}

// Get takes name of the deScheduler, and returns the corresponding deScheduler object, and an error if there is any.
func (c *FakeDeSchedulers) Get(ctx context.Context, name string, options v1.GetOptions) (result *platformv1.DeScheduler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(deschedulersResource, name), &platformv1.DeScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DeScheduler), err
}

// List takes label and field selectors, and returns the list of DeSchedulers that match those selectors.
func (c *FakeDeSchedulers) List(ctx context.Context, opts v1.ListOptions) (result *platformv1.DeSchedulerList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(deschedulersResource, deschedulersKind, opts), &platformv1.DeSchedulerList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &platformv1.DeSchedulerList{ListMeta: obj.(*platformv1.DeSchedulerList).ListMeta}
	for _, item := range obj.(*platformv1.DeSchedulerList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested deSchedulers.
func (c *FakeDeSchedulers) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(deschedulersResource, opts))
}

// Create takes the representation of a deScheduler and creates it.  Returns the server's representation of the deScheduler, and an error, if there is any.
func (c *FakeDeSchedulers) Create(ctx context.Context, deScheduler *platformv1.DeScheduler, opts v1.CreateOptions) (result *platformv1.DeScheduler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(deschedulersResource, deScheduler), &platformv1.DeScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DeScheduler), err
}

// Update takes the representation of a deScheduler and updates it. Returns the server's representation of the deScheduler, and an error, if there is any.
func (c *FakeDeSchedulers) Update(ctx context.Context, deScheduler *platformv1.DeScheduler, opts v1.UpdateOptions) (result *platformv1.DeScheduler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(deschedulersResource, deScheduler), &platformv1.DeScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DeScheduler), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeDeSchedulers) UpdateStatus(ctx context.Context, deScheduler *platformv1.DeScheduler, opts v1.UpdateOptions) (*platformv1.DeScheduler, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(deschedulersResource, "status", deScheduler), &platformv1.DeScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DeScheduler), err
}

// Delete takes name of the deScheduler and deletes it. Returns an error if one occurs.
func (c *FakeDeSchedulers) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(deschedulersResource, name), &platformv1.DeScheduler{})
	return err
}

// Patch applies the patch and returns the patched deScheduler.
func (c *FakeDeSchedulers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *platformv1.DeScheduler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(deschedulersResource, name, pt, data, subresources...), &platformv1.DeScheduler{})
	if obj == nil {
		return nil, err
	}
	return obj.(*platformv1.DeScheduler), err
}
