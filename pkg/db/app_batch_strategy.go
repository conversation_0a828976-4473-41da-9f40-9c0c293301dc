package db

import "time"

type AppBatchStrategy struct {
	ID        int64     `json:"id" gorm:"primaryKey;autoIncrement;column:id"`       // 业务分批策略ID
	Name      string    `json:"name" gorm:"type:varchar(64);not null;column:name"`  // 业务分批策略名称
	Strategy  string    `json:"strategy" gorm:"type:text;not null;column:strategy"` // 策略内容
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime;column:created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime;column:updated_at"` // 更新时间
}

func (AppBatchStrategy) TableName() string {
	return "app_batch_strategy"
}
