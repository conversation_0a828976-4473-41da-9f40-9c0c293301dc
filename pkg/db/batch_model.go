package db

import "time"

type Batch struct {
	ID int64 `gorm:"column:id;primaryKey;autoIncrement;comment:集群的唯一标识符;NOT NULL" json:"id"`

	// TaskId 存储关联的 Task 的 ID，不允许为空
	TaskId     int64  `gorm:"column:task_id;type:bigint;comment:关联的任务ID;NOT NULL" json:"task_id"`
	BatchId    int64  `gorm:"column:batch_id;type:bigint;comment:关联的任务批次ID;NOT NULL" json:"batch_id"`
	SubBatchId int64  `gorm:"column:sub_batch_id;type:bigint;comment:关联的任务子批次ID;" json:"sub_batch_id"`
	Region     string `gorm:"column:region;type:varchar(50);comment:地域" json:"region"`

	// 集群信息
	StartTime *time.Time `gorm:"column:start_time;type:datetime;comment:开始时间" json:"start_time"`
	EndTime   *time.Time `gorm:"column:end_time;type:datetime;comment:结束" json:"end_time"`

	// todo：后续需调整原来的DB的字段值类型
	Interval int64 `gorm:"column:interval;type:bigint;default:0;comment:间隔时间" json:"interval"`

	// 状态
	Status string `gorm:"column:status;type:varchar(50);default:Pending;check:status IN ('Pending', 'Running', 'Completed');comment:任务状态;NOT NULL" json:"status"`

	CreateTime time.Time `gorm:"column:create_time;type:datetime;default:CURRENT_TIMESTAMP;comment:创建时间;NOT NULL" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;type:datetime;default:CURRENT_TIMESTAMP;comment:更新时间;NOT NULL" json:"update_time"`
}

func (batch *Batch) TableName() string {
	return "batch"
}
