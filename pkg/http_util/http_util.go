package http_util

import (
	"bytes"
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"k8s.io/klog/v2"
)

// PostRequest 发送POST请求，带有重试机制
func PostRequest(url string, body []byte, header http.Header, timeout time.Duration) ([]byte, int, error) {
	return PostRequestWithRetry(url, body, header, timeout, 3, time.Second)
}

// PostRequestNoRetry 发送POST请求，不进行重试（用于非幂等操作）
func PostRequestNoRetry(url string, body []byte, header http.Header, timeout time.Duration) ([]byte, int, error) {
	return doPostRequest(url, body, header, timeout)
}

// PostRequestWithRetry 发送POST请求，支持自定义重试次数和重试间隔
func PostRequestWithRetry(url string, body []byte, header http.Header, timeout time.Duration, maxRetries int, retryInterval time.Duration) ([]byte, int, error) {
	var lastErr error
	var lastStatusCode int

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			klog.Warningf("Retrying POST request to %s, attempt %d/%d", url, attempt, maxRetries)
			time.Sleep(retryInterval * time.Duration(attempt)) // 指数退避
		}

		respBody, statusCode, err := doPostRequest(url, body, header, timeout)

		// 如果请求成功，直接返回
		if err == nil && statusCode >= 200 && statusCode < 300 {
			if attempt > 0 {
				klog.Infof("POST request to %s succeeded on attempt %d", url, attempt+1)
			}
			return respBody, statusCode, nil
		}

		// 记录错误信息
		lastErr = err
		lastStatusCode = statusCode

		// 如果是客户端错误（4xx），不进行重试
		if statusCode >= 400 && statusCode < 500 {
			klog.Warningf("POST request to %s failed with client error %d, not retrying", url, statusCode)
			break
		}

		// 记录重试原因
		if err != nil {
			klog.Warningf("POST request to %s failed with error: %v", url, err)
		} else {
			klog.Warningf("POST request to %s failed with status code: %d", url, statusCode)
		}
	}

	// 所有重试都失败了
	if lastErr != nil {
		return nil, lastStatusCode, fmt.Errorf("POST request failed after %d attempts, last error: %w", maxRetries+1, lastErr)
	}
	return nil, lastStatusCode, fmt.Errorf("POST request failed after %d attempts, last status code: %d", maxRetries+1, lastStatusCode)
}

// doPostRequest 执行单次POST请求
func doPostRequest(url string, body []byte, header http.Header, timeout time.Duration) ([]byte, int, error) {
	client := &http.Client{
		Transport: &http.Transport{
			DisableKeepAlives: true,
		},
		Timeout: timeout,
	}

	req, err := http.NewRequestWithContext(context.Background(), "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, 0, err
	}

	if header == nil {
		header = http.Header{}
	}
	req.Header = header
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, 0, err
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, 0, err
	}

	return respBody, resp.StatusCode, nil
}
