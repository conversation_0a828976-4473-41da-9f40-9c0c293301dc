package environment

import (
	"github.com/spf13/pflag"
)

// DefaultTopsFile tops 配置文件的默认路径
var DefaultTopsFile = "/etc/tops/conf/tops-readonly.yaml"

// DefaultConcurrencyWorker 并发工作者的默认数量
var DefaultConcurrencyWorker = 10

// DefaultConcurrencyTask 并发任务的默认数量
var DefaultConcurrencyTask = 15

// DefaultDuration 持续时间的默认值
var DefaultDuration = 10

// DefaultFuseRate 熔断率的默认值
var DefaultFuseRate = 0.3

// DefaultRequestDur 请求持续时间的默认值
var DefaultRequestDur = 60

// DefaultLogDir 日志目录的默认路径
//var DefaultLogDir = "./starship-engine.log"

// DefaultRobotTime 默认的机器人回调时间
var DefaultRobotTime = 90

// DefaultConfig 默认数据配置路径
var DefaultConfig = "/root/conf/config.yaml"

// DefaultBatchDuration 默认分批处理时间
var DefaultBatchDuration = 60

// EnvSettings 存储环境设置的结构体
type EnvSettings struct {
	TopsFile          string
	Version           bool
	Help              bool
	LogDir            string
	ConcurrencyWorker int
	ConcurrencyTask   int
	Duration          int
	FuseRate          float64
	RequestGwDur      int
	RobotTime         int
	Config            string
	BatchDuration     int
	Debug             bool
}

// AddFlags 为 EnvSettings 结构体添加命令行标志
func (s *EnvSettings) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&s.TopsFile, "topsFile", DefaultTopsFile, "Tops config file.")
	fs.BoolVar(&s.Version, "version", false, "Show build version")
	fs.BoolVar(&s.Help, "h", false, "Show help information")
	//fs.StringVar(&s.LogDir, "logDir", DefaultLogDir, "Log file")
	fs.IntVar(&s.ConcurrencyWorker, "concurrencyWorker", DefaultConcurrencyWorker, "Default concurrency worker 5")
	fs.IntVar(&s.ConcurrencyTask, "concurrencyTask", DefaultConcurrencyTask, "Default concurrency task 10")
	fs.IntVar(&s.Duration, "duration", DefaultDuration, "Default duration 20s")
	fs.Float64Var(&s.FuseRate, "fuseRate", DefaultFuseRate, "Default fuse rate 0.3")
	fs.IntVar(&s.RequestGwDur, "requestGwDur", DefaultRequestDur, "Default request duration 60")
	fs.IntVar(&s.RobotTime, "robotTime", DefaultRobotTime, "Default robot time 90")
	fs.StringVar(&s.Config, "config", DefaultConfig, "config file path")
	fs.IntVar(&s.BatchDuration, "batchDuration", DefaultBatchDuration, "batch duration 60")
	fs.BoolVar(&s.Debug, "debug", false, "debug mode")
}
