package publish

import (
	"git.woa.com/kmetis/starship-engine/pkg/common"

	"k8s.io/klog/v2"
)

func (publish *PublishHandler) CreateTask(taskName, cluster, region string) error {
	if err := common.CreateTask(taskName, cluster, region, PUBLISH); err != nil {
		return err
	}
	klog.V(2).InfoS("Creating publish task", "taskName", taskName, "cluster", cluster, "region", region)
	return nil
}

func (publish *PublishHandler) CreateTaskFromFile(fileName string) (string, error) {
	taskName, err := common.CreateTaskFromFile(fileName, PUBLISH)
	if err != nil {
		klog.V(1).ErrorS(err, "Error processing publish file.")
		return "", err
	}
	klog.V(2).InfoS("Creating publish task from file", "fileName", fileName)
	return taskName, nil
}
