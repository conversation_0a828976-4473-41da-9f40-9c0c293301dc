package db

import (
	"time"
)

// Cluster ：TaskName 和 AppAction 是唯一约束
type Cluster struct {
	ID int64 `gorm:"column:id;primaryKey;autoIncrement;comment:集群的唯一标识符;NOT NULL" json:"id"`

	// TaskId 存储关联的 Task 的 ID，不允许为空
	TaskId     int64 `gorm:"column:task_id;type:bigint;comment:关联的任务ID;NOT NULL" json:"task_id"`
	BatchId    int64 `gorm:"column:batch_id;type:bigint;comment:关联的批次任务ID;NOT NULL" json:"batch_id"`
	SubBatchId int64 `gorm:"column:sub_batch_id;type:bigint;comment:关联的子批次任务ID;" json:"sub_batch_id"`

	// 集群信息
	ClusterId string `gorm:"column:cluster_id;type:varchar(50);comment:集群 ID;NOT NULL" json:"cluster_id"`
	MetaID    string `gorm:"column:meta_id;type:varchar(100);comment:meta 集群 ID;NOT NULL" json:"meta_id"`
	Region    string `gorm:"column:region;type:varchar(30);comment:地域信息;NOT NULL" json:"region"`
	Type      string `gorm:"column:type;type:varchar(100);comment:集群类型;NOT NULL" json:"type"`
	AppID     uint64 `gorm:"column:app_id;type:bigint unsigned;comment:应用程序的唯一标识符;NOT NULL" json:"app_id"`
	UIN       string `gorm:"column:uin;type:varchar(100);comment:用户的唯一标识符;NOT NULL" json:"uin"`
	SubUIN    string `gorm:"column:sub_uin;type:varchar(100);comment:子用户的唯一标识符;NOT NULL" json:"sub_uin"`

	// 应用详情
	NameSpace string `gorm:"column:namespace;type:varchar(100);comment:命名空间;NOT NULL" json:"namespace"`
	Name      string `gorm:"column:name;type:varchar(100);comment:应用程序的名称;NOT NULL" json:"name"`

	// StarshipTaskID 存储任务的唯一标识符，不允许为空
	StarshipTaskID   string    `gorm:"column:starship_task_id;type:varchar(100);comment:任务的唯一标识符;NOT NULL" json:"starship_task_id"`
	StarshipChangeId string    `gorm:"column:starship_change_id;type:varchar(100);comment:任务的唯一标识符;NOT NULL" json:"starship_change_id"`
	RollbackTaskID   string    `gorm:"column:rollback_task_id;type:varchar(100);comment:回滚任务ID;default:NULL" json:"rollback_task_id"`
	Stage            string    `gorm:"column:stage;type:varchar(50);comment:集群的阶段;NOT NULL" json:"stage"`
	Status           string    `gorm:"column:status;type:varchar(50);comment:集群状态;NOT NULL" json:"status"`
	Reason           string    `gorm:"column:reason;type:varchar(4096);comment:操作结果的原因;default:NULL" json:"reason"`
	CreateTime       time.Time `gorm:"column:create_time;type:datetime;default:CURRENT_TIMESTAMP;comment:创建时间;NOT NULL" json:"create_time"`
	UpdateTime       time.Time `gorm:"column:update_time;type:datetime;default:CURRENT_TIMESTAMP;comment:更新时间;NOT NULL" json:"update_time"`
}

func (cluster *Cluster) TableName() string {
	return "cluster"
}
