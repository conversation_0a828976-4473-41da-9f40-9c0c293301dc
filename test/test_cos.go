package main

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/tencentyun/cos-go-sdk-v5"
)

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func TestCos() {
	// 存储桶名称，由 bucketname-appid 组成，appid 必须填入，可以在 COS 控制台查看存储桶名称。 https://console.cloud.tencent.com/cos5/bucket
	// 替换为用户的 region，存储桶 region 可以在 COS 控制台“存储桶概览”查看 https://console.cloud.tencent.com/ ，关于地域的详情见 https://cloud.tencent.com/document/product/436/6224 。
	u, _ := url.Parse("")

	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			// 通过环境变量获取密钥
			// 环境变量 SECRETID 表示用户的 SecretId，登录访问管理控制台查看密钥，https://console.cloud.tencent.com/cam/capi
			// SecretID: os.Getenv("SECRETID"), // 用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
			// 环境变量 SECRETKEY 表示用户的 SecretKey，登录访问管理控制台查看密钥，https://console.cloud.tencent.com/cam/capi
			// SecretKey: os.Getenv("SECRETKEY"), // 用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
			SecretID:  "",
			SecretKey: "",
		},
	})

	key := "starship-engine/ommFzAqI.txt" // 前面不需要带斜杠/
	file := "ommFzAqI.txt"

	// 测试1: 使用 Object.Download 下载文件到本地
	fmt.Println("=== 测试 Object.Download ===")
	opt := &cos.MultiDownloadOptions{
		ThreadPoolSize: 5,
	}
	_, err := client.Object.Download(
		context.Background(), key, file, opt,
	)
	if err != nil {
		fmt.Printf("Download failed: %v\n", err)
	} else {
		fmt.Printf("Successfully downloaded %s to %s\n", key, file)
	}

	// 测试2: 使用 Object.Get 获取文件内容到内存
	fmt.Println("\n=== 测试 Object.Get ===")
	resp, err := client.Object.Get(context.Background(), key, nil)
	if err != nil {
		fmt.Printf("Get failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取文件内容
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Read content failed: %v\n", err)
		return
	}

	fmt.Printf("Successfully got file content from %s\n", key)
	fmt.Printf("Content length: %d bytes\n", len(content))
	fmt.Printf("Content preview (first 200 chars):\n%s\n",
		string(content[:min(200, len(content))]))

	// 测试3: 获取不存在的文件
	fmt.Println("\n=== 测试获取不存在的文件 ===")
	nonExistentKey := "starship-engine/non-existent-file.txt"
	resp2, err := client.Object.Get(context.Background(), nonExistentKey, nil)
	if err != nil {
		fmt.Printf("Expected error for non-existent file: %v\n", err)
	} else {
		resp2.Body.Close()
		fmt.Printf("Unexpected success for non-existent file\n")
	}
}
